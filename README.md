# MyStoryMaker

Transform simple ideas into beautifully crafted stories that captivate young minds and inspire creativity. Create personalized stories for your children with AI technology.

## 🌟 Features

- **AI-Powered Story Generation** - Create unique, personalized stories using advanced AI
- **Custom Illustrations** - Generate beautiful images to accompany your stories
- **Audio Narration** - Convert stories to audio with multiple voice options
- **Story Management** - Save, organize, and share your created stories
- **Public Story Gallery** - Browse and vote on community-created stories
- **Responsive Design** - Optimized for desktop and mobile devices
- **User Authentication** - Secure user accounts with Clerk
- **Subscription Management** - Flexible pricing plans with premium features

## 🚀 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS 4
- **Authentication**: Clerk
- **Database**: PostgreSQL
- **Storage**: AWS S3 (R2)
- **AI Services**: Custom AI integration for story and image generation
- **Audio**: AssemblyAI for transcription
- **Deployment**: Docker-ready with standalone output

## 🛠️ Getting Started

### Prerequisites

- Node.js 18+ 
- npm or yarn
- PostgreSQL database
- Clerk account
- AWS S3 or Cloudflare R2 account

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/mystorymaker4.git
cd mystorymaker4
```

2. Install dependencies:
```bash
npm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

Fill in your environment variables:
- Clerk authentication keys
- PostgreSQL connection details
- AWS S3/R2 storage credentials
- AI service API keys

4. Run the development server:
```bash
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the application.

## 📝 Available Scripts

- `npm run dev` - Start development server with Turbopack
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run analyze` - Analyze bundle size

## 🏗️ Project Structure

```
mystorymaker4/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── components/        # Reusable components
│   ├── hooks/            # Custom React hooks
│   ├── types/            # TypeScript type definitions
│   └── (routes)/         # Page routes
├── lib/                   # Utility functions and services
├── public/               # Static assets
└── docker/              # Docker configuration
```

## 🐳 Docker Deployment

The application is containerized and ready for deployment:

```bash
# Build the Docker image
docker build -t mystorymaker .

# Run the container
docker run -p 3000:3000 mystorymaker
```

Or use Docker Compose:

```bash
docker-compose up
```

## 🔧 Configuration

### Environment Variables

Key environment variables needed:

- `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` - Clerk public key
- `CLERK_SECRET_KEY` - Clerk secret key
- `POSTGRES_HOST` - PostgreSQL host
- `POSTGRES_PORT` - PostgreSQL port
- `POSTGRES_DATABASE` - PostgreSQL database name
- `POSTGRES_USER` - PostgreSQL username
- `POSTGRES_PASSWORD` - PostgreSQL password
- `R2_ACCESS_KEY_ID` - R2/S3 access key
- `R2_SECRET_ACCESS_KEY` - R2/S3 secret key

## 🎨 Features Overview

### Story Creation
- AI-powered story generation based on user prompts
- Customizable themes, characters, and settings
- Age-appropriate content filtering

### Image Generation
- AI-generated illustrations for stories
- Multiple art styles and themes
- High-quality image output

### Audio Features
- Text-to-speech conversion
- Multiple voice options
- Audio transcription capabilities

### User Management
- Secure authentication with Clerk
- User profiles and preferences
- Subscription management

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is private and proprietary.

## 🔗 Links

- [Live Application](https://mystorymaker.app)
- [Documentation](https://docs.mystorymaker.app)

---

Built with ❤️ for families who love stories 