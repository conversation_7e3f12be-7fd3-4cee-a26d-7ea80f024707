// Middleware disabled - using manual route protection in components instead
// This ensures we can use database sessions without Edge Runtime limitations

// import authConfig from "./auth.config"
// import NextAuth from "next-auth"
// import { NextResponse } from 'next/server';

// const publicRoutes = [
//   '/',
//   '/sign-in',
//   '/sign-up',
//   '/auth/error',
//   '/about',
//   '/contact',
//   '/pricing',
//   '/privacy',
//   '/terms',
//   '/public',
//   '/shared',
//   '/stories',
//   '/api/auth',
//   '/api/public',
//   '/api/shared',
//   '/api/contact',
//   '/api/pricing',
//   '/api/themes',
//   '/api/voices',
//   '/api/age-ranges',
//   '/api/debug',
// ];

// // Routes that need authentication but should be handled by the route itself
// const authenticatedApiRoutes = [
//   '/api/images',
//   '/api/audio',
//   '/api/stories',
//   '/api/my-stories',
//   '/api/transcribe',
//   '/api/transcriptions',
//   '/api/generate-audio',
//   '/api/subscription',
//   '/api/user',
//   '/api/tags',
// ];

// function isPublicRoute(pathname: string): boolean {
//   return publicRoutes.some(route => 
//     pathname === route || pathname.startsWith(route + '/')
//   );
// }

// function isAuthenticatedApiRoute(pathname: string): boolean {
//   return authenticatedApiRoutes.some(route => 
//     pathname.startsWith(route)
//   );
// }

// const { auth } = NextAuth(authConfig)

// export default auth((req) => {
//   const { pathname } = req.nextUrl;

//   // Handle shared story URLs by redirecting to API route
//   if (pathname.startsWith('/shared/')) {
//     const token = pathname.split('/shared/')[1];
    
//     if (token) {
//       // Redirect to API route that will handle the database lookup and redirect
//       const redirectUrl = new URL(`/api/shared/${token}`, req.url);
//       return NextResponse.redirect(redirectUrl);
//     }
    
//     // If no token, redirect to 404
//     return NextResponse.redirect(new URL('/404', req.url));
//   }

//   // Allow public routes to pass through
//   if (isPublicRoute(pathname)) {
//     return NextResponse.next();
//   }
  
//   // For authenticated API routes, let them handle their own auth
//   if (isAuthenticatedApiRoute(pathname)) {
//     return NextResponse.next();
//   }
  
//   // For other protected routes, check authentication
//   if (!req.auth) {
//     // Redirect to sign-in for unauthenticated users
//     const signInUrl = new URL('/sign-in', req.url);
//     // Use pathname + search to avoid including the wrong domain
//     const redirectPath = pathname + req.nextUrl.search;
//     signInUrl.searchParams.set('redirect_url', redirectPath);
//     return NextResponse.redirect(signInUrl);
//   }
  
//   return NextResponse.next();
// });

export const config = {
  // Disable middleware entirely - no routes will be matched
  matcher: []
};