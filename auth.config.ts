import <PERSON> from "next-auth/providers/google"
import Facebook from "next-auth/providers/facebook"
import type { NextAuthConfig } from "next-auth"

export default {
  providers: [
    // Only include Google OAuth if properly configured
    ...(process.env.AUTH_GOOGLE_ID && 
        process.env.AUTH_GOOGLE_SECRET && 
        process.env.AUTH_GOOGLE_ID !== "your-google-oauth-id" && 
        process.env.AUTH_GOOGLE_SECRET !== "your-google-oauth-secret" 
      ? [Google({
          clientId: process.env.AUTH_GOOGLE_ID,
          clientSecret: process.env.AUTH_GOOGLE_SECRET,
        })] : []),

    // Only include Facebook OAuth if properly configured
    ...(process.env.AUTH_FACEBOOK_ID && 
        process.env.AUTH_FACEBOOK_SECRET && 
        process.env.AUTH_FACEBOOK_ID !== "your-facebook-app-id" && 
        process.env.AUTH_FACEBOOK_SECRET !== "your-facebook-app-secret"
      ? [Facebook({
          clientId: process.env.AUTH_FACEBOOK_ID,
          clientSecret: process.env.AUTH_FACEBOOK_SECRET,
        })] : []),

    // Note: Resend email provider is NOT included here because it requires a database adapter
    // Email authentication is only available in the main auth.ts configuration
  ],
  // Removed JWT session strategy - using database sessions in main auth.ts
  pages: {
    signIn: '/sign-in',
    signOut: '/',
    error: '/sign-in',
  },
} satisfies NextAuthConfig 