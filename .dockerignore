Dockerfile
.dockerignore
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.next
.env*
.vercel
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# git
.git
.gitignore
README.md

# Additional build optimization excludes
.vscode
.idea
*.log
coverage
.nyc_output
.eslintcache
.parcel-cache
.cache
dist
build
out

# OS generated files
Thumbs.db
ehthumbs.db
Desktop.ini

# Development files
*.test.js
*.test.ts
*.spec.js
*.spec.ts
__tests__
test
tests 