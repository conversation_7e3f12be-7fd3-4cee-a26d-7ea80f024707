---
description: 
globs: 
alwaysApply: true
---
### Code Structure & Modularity
- **Never create a file longer than 500 lines of code.** If a file approaches this limit, refactor by splitting it into modules or helper files.
- **Organize code into clearly separated modules**, grouped by feature or responsibility.
- **Use clear, consistent imports** (prefer relative imports within packages).

### AI Behavior Rules
- **Never assume missing context. Ask questions if uncertain.**
- **Never hallucinate libraries or functions** – only use known, verified packages.
- **Use Context 7 MCP if needing tech stack specifics.**
- **Don't create documention for features**

### Technology Stack
- NextAuth.js for authentication with custom billing system - on a remote server, not local
- PostgreSQL for backend database. Use direct PostgreSQL queries via pg client.
- Development environment is MacOS.
- Production environment will be linux docker build.
- If running or restarting the app via "npm run dev", check that it's not already running and kill if so.
- **Elements should be server side instead of client side whenever possible, only use client side if not possible to use server side.**