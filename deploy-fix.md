# Production Deployment Fix

## Issue
The production environment has a webpack runtime error because it's using a cached build that still references the deleted `AudioPlayer.tsx` component.

## Solution 1: Force Clean Docker Build (Recommended)

```bash
# Remove all Docker build cache
docker builder prune -a -f

# Rebuild the image without cache
docker build --no-cache -t mystorymaker .

# Or if using docker-compose
docker-compose build --no-cache

# Restart the container
docker-compose up -d
```

## Solution 2: Clear Next.js Build Cache in Docker

If you have access to the running container:

```bash
# Access the running container
docker exec -it <container-name> sh

# Remove the .next directory
rm -rf .next

# Restart the container to trigger a rebuild
docker restart <container-name>
```

## Solution 3: Update Dockerfile (Prevent Future Issues)

Add this line before the build step in your Dockerfile to ensure clean builds:

```dockerfile
# Clear any existing .next directory before build
RUN rm -rf .next

# Use build cache mount for faster builds
RUN --mount=type=cache,target=/app/.next/cache \
    npm run build
```

## Solution 4: Environment-Specific Build

If deploying to a platform like Vercel, Railway, or similar:

1. **Clear deployment cache** in your platform's dashboard
2. **Trigger a new deployment** from the main branch
3. **Force rebuild** by pushing a small commit

## Verification

After applying the fix, verify by:
1. Checking the URL: https://mystorymaker.app/stories/feb023b8-22fa-4734-b0cc-a638bd4ea61b/the-secret-of-the-floating-island
2. Looking for successful compilation in deployment logs
3. Testing audio functionality on public stories

## Prevention

To prevent this in the future:
- Always clear build cache when removing components
- Consider adding `RUN rm -rf .next` before builds in production
- Use `--no-cache` flag when making significant structural changes
