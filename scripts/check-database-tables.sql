-- Check if all NextAuth.js tables exist and have correct structure

-- List all NextAuth.js related tables
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'accounts', 'sessions', 'verification_token', 'verification_tokens')
ORDER BY table_name;

-- Check users table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'users'
ORDER BY ordinal_position;

-- Check sessions table structure  
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sessions'
ORDER BY ordinal_position;

-- Check verification_token table structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'verification_token'
ORDER BY ordinal_position;

-- Check if there are any users in the database
SELECT id, email, name, "emailVerified" FROM users LIMIT 5;

-- Check if there are any active sessions
SELECT id, "userId", expires, "sessionToken" FROM sessions WHERE expires > NOW() LIMIT 5; 