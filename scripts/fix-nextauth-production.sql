-- Fix NextAuth.js tables for production
-- This script ensures all NextAuth tables exist with correct structure

-- 1. Fix verification table naming (NextAuth expects singular 'verification_token')
DROP TABLE IF EXISTS verification_tokens CASCADE;

CREATE TABLE IF NOT EXISTS verification_token (
  identifier TEXT NOT NULL,
  expires TIMESTAMPTZ NOT NULL,
  token TEXT NOT NULL,
  PRIMARY KEY (identifier, token)
);

-- 2. Ensure users table has correct structure for NextAuth
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS name VARCHA<PERSON>(255),
ADD COLUMN IF NOT EXISTS email VARCHAR(255),
ADD COLUMN IF NOT EXISTS "emailVerified" TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS image TEXT;

-- Make sure email is unique
CREATE UNIQUE INDEX IF NOT EXISTS users_email_unique ON users(email);

-- 3. Ensure accounts table exists with correct structure
CREATE TABLE IF NOT EXISTS accounts (
  id SERIAL PRIMARY KEY,
  "userId" INTEGER NOT NULL,
  type VARCHAR(255) NOT NULL,
  provider VARCHAR(255) NOT NULL,
  "providerAccountId" VARCHAR(255) NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  expires_at BIGINT,
  id_token TEXT,
  scope TEXT,
  session_state TEXT,
  token_type TEXT,
  UNIQUE(provider, "providerAccountId")
);

-- 4. Ensure sessions table exists with correct structure
CREATE TABLE IF NOT EXISTS sessions (
  id SERIAL PRIMARY KEY,
  "userId" INTEGER NOT NULL,
  expires TIMESTAMPTZ NOT NULL,
  "sessionToken" VARCHAR(255) NOT NULL UNIQUE
);

-- 5. Create necessary indexes
CREATE UNIQUE INDEX IF NOT EXISTS sessions_sessionToken_unique ON sessions("sessionToken");
CREATE INDEX IF NOT EXISTS accounts_userId_idx ON accounts("userId");
CREATE INDEX IF NOT EXISTS sessions_userId_idx ON sessions("userId");
CREATE INDEX IF NOT EXISTS verification_token_token_idx ON verification_token(token);
CREATE INDEX IF NOT EXISTS verification_token_identifier_idx ON verification_token(identifier);

-- 6. Add foreign key constraints (if they don't exist)
DO $$ 
BEGIN 
  -- Add foreign key for accounts table
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'accounts_userId_fkey' 
    AND table_name = 'accounts'
  ) THEN
    ALTER TABLE accounts ADD CONSTRAINT accounts_userId_fkey 
    FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE;
  END IF;
  
  -- Add foreign key for sessions table
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.table_constraints 
    WHERE constraint_name = 'sessions_userId_fkey' 
    AND table_name = 'sessions'
  ) THEN
    ALTER TABLE sessions ADD CONSTRAINT sessions_userId_fkey 
    FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE;
  END IF;
END $$;

-- 7. Clean up any expired sessions and verification tokens
DELETE FROM sessions WHERE expires < NOW();
DELETE FROM verification_token WHERE expires < NOW();

-- 8. Verify table structure
SELECT 'NextAuth tables check:' as status;
SELECT table_name, 
       CASE 
         WHEN table_name IN ('users', 'accounts', 'sessions', 'verification_token') THEN '✅ EXISTS'
         ELSE '❌ MISSING'
       END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'accounts', 'sessions', 'verification_token', 'verification_tokens')
ORDER BY table_name;

-- Show session token column details to verify structure
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name = 'sessions'
AND column_name = 'sessionToken';
