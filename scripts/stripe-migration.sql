-- Stripe Integration Migration
-- Add Stripe-specific fields to existing tables

-- Add Stripe customer ID to users table
ALTER TABLE users 
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_created_at TIMESTAMPTZ DEFAULT NOW();

-- Add Stripe subscription fields to user_subscriptions table
ALTER TABLE user_subscriptions 
ADD COLUMN IF NOT EXISTS stripe_subscription_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_customer_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_price_id VARCHAR(255);

-- Add Stripe price IDs to subscription_plans table
ALTER TABLE subscription_plans 
ADD COLUMN IF NOT EXISTS stripe_monthly_price_id VARCHAR(255),
ADD COLUMN IF NOT EXISTS stripe_yearly_price_id VARCHAR(255);

-- Create checkout sessions table for tracking payment flows
CREATE TABLE IF NOT EXISTS stripe_checkout_sessions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  stripe_session_id VARCHAR(255) NOT NULL UNIQUE,
  stripe_customer_id VARCHAR(255),
  plan_name VARCHAR(50) NOT NULL,
  billing_period VARCHAR(10) NOT NULL, -- 'monthly' or 'yearly'
  amount_total INTEGER, -- Amount in cents
  currency VARCHAR(3) DEFAULT 'usd',
  status VARCHAR(20) NOT NULL DEFAULT 'open', -- 'open', 'complete', 'expired'
  payment_status VARCHAR(20), -- 'paid', 'unpaid', 'no_payment_required'
  success_url TEXT,
  cancel_url TEXT,
  expires_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_stripe_customer_id ON users(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_subscription_id ON user_subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_stripe_customer_id ON user_subscriptions(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_stripe_checkout_sessions_user_id ON stripe_checkout_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_stripe_checkout_sessions_stripe_session_id ON stripe_checkout_sessions(stripe_session_id);
CREATE INDEX IF NOT EXISTS idx_stripe_checkout_sessions_status ON stripe_checkout_sessions(status);

-- Update existing subscription plans with placeholder Stripe price IDs
-- These will need to be updated with actual Stripe price IDs from your dashboard
UPDATE subscription_plans 
SET 
  stripe_monthly_price_id = CASE 
    WHEN name = 'starter' THEN 'price_starter_monthly_placeholder'
    WHEN name = 'family' THEN 'price_family_monthly_placeholder'
    ELSE NULL
  END,
  stripe_yearly_price_id = CASE 
    WHEN name = 'starter' THEN 'price_starter_yearly_placeholder'
    WHEN name = 'family' THEN 'price_family_yearly_placeholder'
    ELSE NULL
  END
WHERE name IN ('starter', 'family');

COMMENT ON COLUMN users.stripe_customer_id IS 'Stripe customer ID for billing';
COMMENT ON COLUMN user_subscriptions.stripe_subscription_id IS 'Stripe subscription ID';
COMMENT ON COLUMN user_subscriptions.stripe_customer_id IS 'Stripe customer ID associated with subscription';
COMMENT ON COLUMN subscription_plans.stripe_monthly_price_id IS 'Stripe price ID for monthly billing';
COMMENT ON COLUMN subscription_plans.stripe_yearly_price_id IS 'Stripe price ID for yearly billing';
COMMENT ON TABLE stripe_checkout_sessions IS 'Tracks Stripe checkout sessions for payment flow monitoring and analytics'; 