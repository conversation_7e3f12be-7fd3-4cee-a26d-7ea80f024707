-- Add password authentication support to users table

-- Add password_hash column to users table
ALTER TABLE users ADD COLUMN IF NOT EXISTS password_hash VARCHAR(255);

-- Add index for better performance on email lookups (if not exists)
CREATE INDEX IF NOT EXISTS users_email_idx ON users(email);

-- Note: Users can have either:
-- 1. password_hash (for credentials auth) 
-- 2. emailVerified (for magic link auth)
-- 3. Both (users can use either method)
-- 4. OAuth accounts (handled via accounts table)

-- The password_hash will be NULL for users who only use magic link or OAuth
-- The emailVerified will be NULL for users who only use password auth initially 