-- Fix verification table naming issue
-- NextAuth.js expects 'verification_token' (singular), not 'verification_tokens' (plural)

-- Drop the incorrectly named table if it exists
DROP TABLE IF EXISTS verification_tokens;

-- Create the correctly named table
CREATE TABLE IF NOT EXISTS verification_token
(
  identifier TEXT NOT NULL,
  expires TIMESTAMPTZ NOT NULL,
  token TEXT NOT NULL,
 
  PRIMARY KEY (identifier, token)
);

-- Create index for better performance
CREATE UNIQUE INDEX IF NOT EXISTS "verification_token_token_unique" ON verification_token(token);
CREATE INDEX IF NOT EXISTS "verification_token_identifier_idx" ON verification_token(identifier); 