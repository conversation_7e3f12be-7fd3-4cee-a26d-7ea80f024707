-- Update plan features to match new specifications

-- Update Free Plan
UPDATE subscription_plans SET 
  image_regenerations_per_story = 2,
  features = '{"audio_generation": false, "premium_voices": false, "pdf_download": false, "email_support": false}'
WHERE name = 'free';

-- Update Starter Plan  
UPDATE subscription_plans SET 
  price_monthly = 6.00, 
  price_yearly = 60.00,
  features = '{"audio_generation": true, "premium_voices": false, "pdf_download": true, "email_support": true}'
WHERE name = 'starter';

-- Update Family Plan
UPDATE subscription_plans SET 
  price_monthly = 10.00, 
  price_yearly = 100.00,
  features = '{"audio_generation": true, "premium_voices": true, "pdf_download": true, "email_support": true, "voice_selection": true, "word_highlighting": true}'
WHERE name = 'family';

-- Verify the updates
SELECT name, display_name, price_monthly, price_yearly, stories_per_month, image_regenerations_per_story, features 
FROM subscription_plans 
ORDER BY price_monthly; 