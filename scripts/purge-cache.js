#!/usr/bin/env node

/**
 * Cache Purge Utility Script
 * 
 * Usage:
 * node scripts/purge-cache.js all
 * node scripts/purge-cache.js story <story-uuid>
 * node scripts/purge-cache.js user <user-id>
 * node scripts/purge-cache.js stats
 */

const https = require('https');
const http = require('http');

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

async function makeRequest(method, path, data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname + url.search,
      method: method,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Cache-Purge-Script/1.0'
      }
    };

    if (data) {
      const jsonData = JSON.stringify(data);
      options.headers['Content-Length'] = Buffer.byteLength(jsonData);
    }

    const req = client.request(options, (res) => {
      let responseData = '';
      
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      
      res.on('end', () => {
        try {
          const parsed = JSON.parse(responseData);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: responseData });
        }
      });
    });

    req.on('error', (err) => {
      reject(err);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function purgeCache(type, key = null) {
  try {
    console.log(`🧹 Purging cache: ${type}${key ? ` (${key})` : ''}`);
    
    if (type === 'stats') {
      const response = await makeRequest('GET', '/api/cache/purge');
      console.log('📊 Cache Stats:', JSON.stringify(response.data, null, 2));
      return;
    }

    const response = await makeRequest('POST', '/api/cache/purge', { type, key });
    
    if (response.status === 200) {
      console.log('✅ Success:', response.data.message);
    } else if (response.status === 401) {
      console.log('❌ Error: Unauthorized. Make sure you are logged in.');
    } else {
      console.log('❌ Error:', response.data.error || 'Unknown error');
    }
  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);

if (args.length === 0) {
  console.log(`
🧹 Cache Purge Utility

Usage:
  node scripts/purge-cache.js all                    # Clear all cache
  node scripts/purge-cache.js story <story-uuid>     # Clear specific story cache
  node scripts/purge-cache.js user <user-id>         # Clear user-specific cache
  node scripts/purge-cache.js stats                  # Show cache statistics

Examples:
  node scripts/purge-cache.js all
  node scripts/purge-cache.js story abc123-def456-ghi789
  node scripts/purge-cache.js user user_123456
  node scripts/purge-cache.js stats
`);
  process.exit(1);
}

const [type, key] = args;

if (type === 'all' || type === 'stats') {
  purgeCache(type);
} else if ((type === 'story' || type === 'user') && key) {
  purgeCache(type, key);
} else {
  console.log('❌ Invalid arguments. Use --help for usage information.');
  process.exit(1);
} 