-- NextAuth.js PostgreSQL Schema Migration
-- This creates the required tables for NextAuth.js authentication

-- Create accounts table for OAuth providers
CREATE TABLE IF NOT EXISTS accounts (
  id SERIAL PRIMARY KEY,
  "userId" INTEGER NOT NULL,
  type VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  provider VARCHAR(255) NOT NULL,
  "providerAccountId" VARCHAR(255) NOT NULL,
  refresh_token TEXT,
  access_token TEXT,
  expires_at BIGINT,
  id_token TEXT,
  scope TEXT,
  session_state TEXT,
  token_type TEXT,
  UNIQUE(provider, "providerAccountId")
);

-- Create sessions table for user sessions
CREATE TABLE IF NOT EXISTS sessions (
  id SERIAL PRIMARY KEY,
  "userId" INTEGER NOT NULL,
  expires TIMESTAMPTZ NOT NULL,
  "sessionToken" VARCHAR(255) NOT NULL UNIQUE
);

-- Create users table for user information
CREATE TABLE IF NOT EXISTS users (
  id SERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255),
  email VARCHAR(255) UNIQUE,
  "emailVerified" TIMESTAMPTZ,
  image TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create verification_tokens table for email verification
CREATE TABLE IF NOT EXISTS verification_tokens (
  identifier VARCHAR(255) NOT NULL,
  token VARCHAR(255) NOT NULL,
  expires TIMESTAMPTZ NOT NULL,
  PRIMARY KEY (identifier, token)
);

-- Add foreign key constraints
ALTER TABLE accounts ADD CONSTRAINT fk_accounts_userId 
  FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE;

ALTER TABLE sessions ADD CONSTRAINT fk_sessions_userId 
  FOREIGN KEY ("userId") REFERENCES users(id) ON DELETE CASCADE;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_accounts_userId ON accounts("userId");
CREATE INDEX IF NOT EXISTS idx_sessions_userId ON sessions("userId");
CREATE INDEX IF NOT EXISTS idx_sessions_sessionToken ON sessions("sessionToken");
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Create a subscription_plans table for custom billing
CREATE TABLE IF NOT EXISTS subscription_plans (
  id SERIAL PRIMARY KEY,
  name VARCHAR(50) NOT NULL UNIQUE,
  display_name VARCHAR(100) NOT NULL,
  price_monthly DECIMAL(10,2),
  price_yearly DECIMAL(10,2),
  stories_per_month INTEGER NOT NULL DEFAULT 0,
  image_regenerations_per_story INTEGER NOT NULL DEFAULT 0,
  features JSONB DEFAULT '{}',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create user_subscriptions table
CREATE TABLE IF NOT EXISTS user_subscriptions (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  plan_id INTEGER NOT NULL REFERENCES subscription_plans(id),
  status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, cancelled, expired
  current_period_start TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  current_period_end TIMESTAMPTZ NOT NULL DEFAULT NOW() + INTERVAL '1 month',
  cancel_at_period_end BOOLEAN DEFAULT false,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create user_usage table to track monthly usage
CREATE TABLE IF NOT EXISTS user_usage (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  month_year VARCHAR(7) NOT NULL, -- Format: YYYY-MM
  stories_created INTEGER DEFAULT 0,
  images_regenerated INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(user_id, month_year)
);

-- Insert default subscription plans
INSERT INTO subscription_plans (name, display_name, price_monthly, price_yearly, stories_per_month, image_regenerations_per_story, features) 
VALUES 
  ('free', 'Free Plan', 0.00, 0.00, 3, 2, '{"audio_generation": false, "premium_voices": false, "pdf_download": false, "email_support": false}'),
  ('starter', 'Starter Plan', 9.99, 99.99, 30, 3, '{"audio_generation": true, "premium_voices": false, "pdf_download": true, "email_support": true}'),
  ('family', 'Family Plan', 19.99, 199.99, 75, 4, '{"audio_generation": true, "premium_voices": true, "pdf_download": true, "email_support": true, "voice_selection": true, "word_highlighting": true}')
ON CONFLICT (name) DO NOTHING;

-- Create indexes for subscription tables
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_user_id ON user_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_subscriptions_status ON user_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_user_usage_user_id ON user_usage(user_id);
CREATE INDEX IF NOT EXISTS idx_user_usage_month_year ON user_usage(month_year);

-- Update existing stories table to reference new users table
-- First, let's check if we need to migrate existing user data
-- This will be handled in a separate migration step

COMMENT ON TABLE accounts IS 'NextAuth.js accounts table for OAuth providers';
COMMENT ON TABLE sessions IS 'NextAuth.js sessions table for user sessions';
COMMENT ON TABLE users IS 'NextAuth.js users table for user information';
COMMENT ON TABLE verification_tokens IS 'NextAuth.js verification tokens for email verification';
COMMENT ON TABLE subscription_plans IS 'Available subscription plans';
COMMENT ON TABLE user_subscriptions IS 'User subscription information';
COMMENT ON TABLE user_usage IS 'Monthly usage tracking for users'; 