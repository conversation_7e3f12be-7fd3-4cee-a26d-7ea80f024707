version: '3.8'
services:
  app:
    build: 
      context: .
      cache_from:
        - mystorymaker:latest
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - POSTGRES_DATABASE=${POSTGRES_DATABASE}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_SSL=${POSTGRES_SSL:-false}
      - NEXT_PUBLIC_TURNSTILE_SITE_KEY=${NEXT_PUBLIC_TURNSTILE_SITE_KEY}
      - TURNSTILE_SECRET_KEY=${TURNSTILE_SECRET_KEY}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - ASSEMBLYAI_API_KEY=${ASSEMBLYAI_API_KEY}
      - WEBHOOK_SECRET=${WEBHOOK_SECRET}
      - R2_IMAGES_BUCKET_NAME=${R2_IMAGES_BUCKET_NAME}
      - R2_AUDIO_BUCKET_NAME=${R2_AUDIO_BUCKET_NAME}
      - R2_ENDPOINT=${R2_ENDPOINT}
      - R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
      - R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}
    env_file:
      - .env
    restart: unless-stopped