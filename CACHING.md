# Caching Implementation

This document explains the caching strategy implemented to resolve issues with Cloudflare caching and ensure fresh content delivery.

## Problem

The application was experiencing caching issues where:
- Newly created public stories weren't appearing on the Discover Stories page
- Deleted stories were still showing up on production
- Changes were visible on dev but not on production (due to Cloudflare caching)

## Solution

### 1. Cache Control Headers

Added appropriate cache control headers to API endpoints:

#### Public Stories API (`/api/public/stories`)
- Cache for 1 minute (`max-age=60`)
- Allow stale content for 5 minutes while revalidating (`stale-while-revalidate=300`)
- Specific CDN cache control for Cloudflare and Vercel

#### Featured Stories API (`/api/public/stories/featured`)
- Cache for 2 minutes (`max-age=120`)
- Allow stale content for 10 minutes while revalidating (`stale-while-revalidate=600`)

#### Themes API (`/api/themes`)
- Cache for 1 hour (`max-age=3600`)
- Allow stale content for 24 hours while revalidating (`stale-while-revalidate=86400`)

### 2. Cache Invalidation

Implemented automatic cache invalidation when:
- Stories are made public/private (`PATCH /api/stories/[uuid]`)
- Stories are deleted (`DELETE /api/stories/[uuid]`)

### 3. Client-Side Cache Busting

Added cache busting to the Discover Stories page:
- Timestamp parameter in API requests (`?t=${Date.now()}`)
- `cache: 'no-store'` option to prevent browser caching
- Manual refresh button for users

### 4. Cache Management API

Created `/api/cache/purge` endpoint for manual cache management:

```bash
# Clear all cache
curl -X POST /api/cache/purge -H "Content-Type: application/json" -d '{"type":"all"}'

# Clear specific story cache
curl -X POST /api/cache/purge -H "Content-Type: application/json" -d '{"type":"story","key":"story-uuid"}'

# Clear user cache
curl -X POST /api/cache/purge -H "Content-Type: application/json" -d '{"type":"user","key":"user-id"}'

# Get cache statistics
curl /api/cache/purge
```

### 5. Cache Purge Utility Script

Created `scripts/purge-cache.js` for easy cache management:

```bash
# Clear all cache
node scripts/purge-cache.js all

# Clear specific story cache
node scripts/purge-cache.js story abc123-def456-ghi789

# Clear user cache
node scripts/purge-cache.js user user_123456

# Show cache statistics
node scripts/purge-cache.js stats
```

## Cache Headers Explained

### Standard Headers
- `Cache-Control`: Controls browser and proxy caching behavior
- `max-age`: How long content is fresh (in seconds)
- `s-maxage`: How long shared caches (CDNs) should cache
- `stale-while-revalidate`: How long stale content can be served while fetching fresh content

### CDN-Specific Headers
- `CDN-Cache-Control`: General CDN cache control
- `Vercel-CDN-Cache-Control`: Vercel-specific cache control

## Troubleshooting

### If stories still don't appear after making them public:

1. **Check cache invalidation is working:**
   ```bash
   node scripts/purge-cache.js stats
   ```

2. **Manually clear cache:**
   ```bash
   node scripts/purge-cache.js all
   ```

3. **Use the refresh button** on the Discover Stories page

4. **Check Cloudflare cache settings** in your Cloudflare dashboard

### If deleted stories still appear:

1. **Verify the story is marked as deleted** in the database:
   ```sql
   SELECT id, title, is_deleted, deleted_at FROM stories WHERE story_uuid = 'your-uuid';
   ```

2. **Clear cache manually:**
   ```bash
   node scripts/purge-cache.js story your-story-uuid
   ```

3. **Check the API response** directly:
   ```bash
   curl "https://your-domain.com/api/public/stories?t=$(date +%s)"
   ```

## Monitoring

Monitor cache effectiveness by:
1. Checking cache hit rates in Cloudflare Analytics
2. Using the cache stats endpoint: `GET /api/cache/purge`
3. Monitoring API response times

## Best Practices

1. **Always invalidate cache** when data changes
2. **Use appropriate cache durations** based on data volatility
3. **Test cache behavior** in production environment
4. **Monitor cache hit rates** and adjust as needed
5. **Provide manual refresh options** for users when needed 