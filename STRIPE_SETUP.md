# Stripe Integration Setup Guide

This guide will help you set up Stripe subscriptions for your MyStoryMaker application.

## Prerequisites

1. A Stripe account (sign up at https://stripe.com)
2. Access to your Stripe Dashboard
3. Your application already deployed or accessible via a public URL for webhooks

## Step 1: Get Stripe API Keys

1. Go to your [Stripe Dashboard](https://dashboard.stripe.com/)
2. Navigate to **Developers > API keys**
3. Copy your **Publishable key** and **Secret key**
4. Add these to your `.env.local` file:

```bash
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here

# Application URL (required for Stripe redirects)
NEXTAUTH_URL=http://localhost:3000  # For development
# NEXTAUTH_URL=https://yourdomain.com  # For production
```

## Step 2: Create Products and Prices

1. In Stripe Dashboard, go to **Products**
2. Create two products:

### Starter Plan
- **Name**: Starter Plan
- **Description**: 25 stories per month with audio generation
- **Pricing**: 
  - Monthly: $9.99/month
  - Yearly: $99.99/year (17% discount)

### Family Plan
- **Name**: Family Plan  
- **Description**: 100 stories per month with premium features
- **Pricing**:
  - Monthly: $19.99/month
  - Yearly: $199.99/year (17% discount)

3. Copy the Price IDs for each plan and add them to your `.env.local`:

```bash
# Stripe Price IDs
STRIPE_STARTER_MONTHLY_PRICE_ID=price_1234567890abcdef
STRIPE_STARTER_YEARLY_PRICE_ID=price_1234567890ghijkl
STRIPE_FAMILY_MONTHLY_PRICE_ID=price_1234567890mnopqr
STRIPE_FAMILY_YEARLY_PRICE_ID=price_1234567890stuvwx
```

## Step 3: Set Up Webhooks

1. In Stripe Dashboard, go to **Developers > Webhooks**
2. Click **Add endpoint**
3. Set the endpoint URL to: `https://yourdomain.com/api/stripe/webhook`
4. Select the following events:
   - `checkout.session.completed`
   - `customer.subscription.created`
   - `customer.subscription.updated`
   - `customer.subscription.deleted`
   - `invoice.payment_succeeded`
   - `invoice.payment_failed`
5. Copy the **Signing secret** and add it to your `.env.local`:

```bash
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

## Step 4: Run Database Migration

Run the Stripe database migration to add the required fields:

```bash
# If you have a database migration tool, run:
psql -d your_database < scripts/stripe-migration.sql

# Or run the SQL commands manually in your database
```

## Step 5: Update Database with Price IDs

After creating your Stripe products, update your database with the actual price IDs:

```sql
-- Update starter plan price IDs
UPDATE subscription_plans 
SET 
  stripe_monthly_price_id = 'price_your_actual_starter_monthly_id',
  stripe_yearly_price_id = 'price_your_actual_starter_yearly_id'
WHERE name = 'starter';

-- Update family plan price IDs
UPDATE subscription_plans 
SET 
  stripe_monthly_price_id = 'price_your_actual_family_monthly_id',
  stripe_yearly_price_id = 'price_your_actual_family_yearly_id'
WHERE name = 'family';
```

## Step 6: Configure Stripe Customer Portal

1. In Stripe Dashboard, go to **Settings > Billing > Customer portal**
2. Enable the customer portal
3. Configure the settings:
   - **Business information**: Add your company details
   - **Privacy policy**: Add your privacy policy URL
   - **Terms of service**: Add your terms of service URL
   - **Features**: Enable subscription cancellation, plan changes, payment method updates

## Step 7: Test the Integration

1. Use Stripe's test credit card numbers:
   - Success: `4242 4242 4242 4242`
   - Decline: `4000 0000 0000 0002`
2. Test the complete flow:
   - Sign up for an account
   - Select a plan on the pricing page
   - Complete the checkout process
   - Verify the subscription appears in both Stripe and your database
   - Test the billing portal functionality

## Step 8: Go Live

When ready for production:

1. Get your live API keys from Stripe Dashboard
2. Update your environment variables with live keys
3. Update webhook endpoint to production URL
4. Test with real payment methods
5. Monitor webhook deliveries in Stripe Dashboard

## Features Included

### For Users
- **Stripe Checkout**: Secure payment processing
- **Billing Portal**: Self-service subscription management
- **Plan Changes**: Upgrade/downgrade between plans
- **Cancellation**: Cancel subscriptions with end-of-period processing
- **Invoice Management**: View and download invoices

### For Developers
- **Webhook Handling**: Automatic subscription sync
- **Database Integration**: Seamless integration with existing user system
- **Error Handling**: Robust error handling and logging
- **Security**: Webhook signature verification

## API Endpoints

- `POST /api/stripe/create-checkout` - Create checkout session
- `POST /api/stripe/billing-portal` - Access billing portal
- `POST /api/stripe/webhook` - Handle Stripe webhooks

## Components

- `SubscriptionManager` - User subscription management UI
- `CustomPricingTable` - Updated with Stripe integration

## Support

For issues with this integration:
1. Check Stripe Dashboard webhook delivery logs
2. Review application logs for errors
3. Verify environment variables are correctly set
4. Test with Stripe's CLI tool for webhook testing

## Troubleshooting

### Common Issues

**1. "Invalid URL" Error**
- **Problem**: Stripe checkout fails with URL validation error
- **Solution**: Ensure `NEXTAUTH_URL` is set in your environment variables
- **Example**: `NEXTAUTH_URL=http://localhost:3000` for development

**2. Webhook Not Receiving Events**
- **Problem**: Subscription updates not syncing to database
- **Solution**: 
  - Verify webhook endpoint URL is accessible
  - Check webhook signing secret matches your environment variable
  - Use Stripe CLI for local testing: `stripe listen --forward-to localhost:3000/api/stripe/webhook`

**3. Price ID Not Found**
- **Problem**: "Stripe price ID not configured" error
- **Solution**: Update your database with actual Stripe price IDs from your dashboard

**4. Customer Portal Access Denied**
- **Problem**: Users can't access billing portal
- **Solution**: Ensure customer portal is enabled in Stripe Dashboard settings

## Security Notes

- Never expose secret keys in client-side code
- Always verify webhook signatures
- Use HTTPS for all webhook endpoints
- Regularly rotate API keys
- Monitor for suspicious activity in Stripe Dashboard 