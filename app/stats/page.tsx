import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '../../auth';
import Navigation from '../components/navigation/Navigation';
import Footer from '../components/Footer';
import { query } from '../../lib/postgres-client';

export const metadata: Metadata = {
  title: 'Writing Stats - MyStoryMaker',
  description: 'Track your creative journey and writing progress',
};

interface StatsData {
  totalStories: number;
  totalWords: number;
  mostUsedThemes: Array<{ name: string; count: number }>;
  mostUsedTags: Array<{ name: string; count: number }>;
}

// Fetch user's writing stats from PostgreSQL database (server-side)
async function getUserStats(userId: string): Promise<StatsData> {
  try {
    // Fetch total stories count
    const storiesCountResult = await query(
      'SELECT COUNT(*) as count FROM stories WHERE user_id = $1 AND is_deleted = false',
      [userId]
    );

    const totalStories = parseInt(storiesCountResult.rows[0]?.count) || 0;

    // Fetch story content to calculate word count
    const contentResult = await query(
      'SELECT content FROM stories WHERE user_id = $1 AND is_deleted = false',
      [userId]
    );

    // Calculate total words from content
    const totalWords = contentResult.rows.reduce((sum: number, story: { content: string }) => {
      if (story.content) {
        // Simple word count: split by whitespace and filter out empty strings
        const words = story.content.trim().split(/\s+/).filter((word: string) => word.length > 0);
        return sum + words.length;
      }
      return sum;
    }, 0);

    // Fetch most used themes
    const themesResult = await query(`
      SELECT t.description as name, COUNT(*) as count
      FROM stories s
      JOIN stories_themes st ON s.id = st.story_id
      JOIN themes t ON st.theme_id = t.id
      WHERE s.user_id = $1 AND s.is_deleted = false
      GROUP BY t.description
      ORDER BY count DESC
      LIMIT 5
    `, [userId]);

    const mostUsedThemes = themesResult.rows.map((row: { name: string; count: string }) => ({
      name: row.name,
      count: parseInt(row.count)
    }));

    // Fetch most used tags (from non-deleted stories)
    const tagsResult = await query(`
      SELECT t.name, COUNT(*) as count
      FROM tags t
      JOIN story_tags st ON t.id = st.tag_id
      JOIN stories s ON st.story_id = s.id
      WHERE s.user_id = $1 AND s.is_deleted = false
      GROUP BY t.name
      ORDER BY count DESC
      LIMIT 5
    `, [userId]);

    const mostUsedTags = tagsResult.rows.map((row: { name: string; count: string }) => ({
      name: row.name,
      count: parseInt(row.count)
    }));

    return {
      totalStories,
      totalWords,
      mostUsedThemes,
      mostUsedTags
    };
  } catch (error) {
    console.error('Error fetching user stats:', error);
    return {
      totalStories: 0,
      totalWords: 0,
      mostUsedThemes: [],
      mostUsedTags: []
    };
  }
}

export default async function StatsPage() {
  // Check if user is authenticated (server-side)
  const session = await auth();
  
  if (!session?.user?.id) {
    redirect('/');
  }

  // Fetch stats server-side
  const stats = await getUserStats(session.user.id);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="stats" />
      
      <main className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-blue-500/20 rounded-xl">
              <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">
              My Writing Stats
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Track your creative journey and writing progress
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Quick Stats Cards */}
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4 lg:gap-6 mb-8">
            {/* Total Stories */}
            <div className="bg-gradient-to-br from-blue-600/20 to-blue-800/20 border border-blue-500/30 rounded-xl p-4 sm:p-6 h-full">
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-blue-500/20 rounded-lg">
                  <svg className="w-5 h-5 sm:w-6 sm:h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                  </svg>
                </div>
              </div>
              <div>
                <p className="text-blue-300 text-sm font-medium mb-1">Total Stories</p>
                <p className="text-2xl sm:text-3xl font-bold text-white">{stats.totalStories}</p>
                <p className="text-blue-300/70 text-xs mt-1">Stories created</p>
              </div>
            </div>

            {/* Total Words */}
            <div className="bg-gradient-to-br from-emerald-600/20 to-emerald-800/20 border border-emerald-500/30 rounded-xl p-4 sm:p-6 h-full">
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-emerald-500/20 rounded-lg">
                  <svg className="w-5 h-5 sm:w-6 sm:h-6 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </div>
              </div>
              <div>
                <p className="text-emerald-300 text-sm font-medium mb-1">Total Words</p>
                <p className="text-2xl sm:text-3xl font-bold text-white">{stats.totalWords.toLocaleString()}</p>
                <p className="text-emerald-300/70 text-xs mt-1">Words written</p>
              </div>
            </div>

            {/* Average Words per Story */}
            <div className="bg-gradient-to-br from-purple-600/20 to-purple-800/20 border border-purple-500/30 rounded-xl p-4 sm:p-6 h-full">
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-purple-500/20 rounded-lg">
                  <svg className="w-5 h-5 sm:w-6 sm:h-6 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
              </div>
              <div>
                <p className="text-purple-300 text-sm font-medium mb-1">Avg Words/Story</p>
                <p className="text-2xl sm:text-3xl font-bold text-white">
                  {stats.totalStories > 0 ? Math.round(stats.totalWords / stats.totalStories).toLocaleString() : '0'}
                </p>
                <p className="text-purple-300/70 text-xs mt-1">Per story</p>
              </div>
            </div>

            {/* Creativity Score */}
            <div className="bg-gradient-to-br from-amber-600/20 to-amber-800/20 border border-amber-500/30 rounded-xl p-4 sm:p-6 h-full">
              <div className="flex items-center justify-between mb-3 sm:mb-4">
                <div className="p-2 sm:p-3 bg-amber-500/20 rounded-lg">
                  <svg className="w-5 h-5 sm:w-6 sm:h-6 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
              </div>
              <div>
                <p className="text-amber-300 text-sm font-medium mb-1">Creativity Score</p>
                <p className="text-2xl sm:text-3xl font-bold text-white">
                  {Math.min(100, Math.round((stats.mostUsedThemes.length * 10) + (stats.mostUsedTags.length * 5) + (stats.totalStories * 2)))}
                </p>
                <p className="text-amber-300/70 text-xs mt-1">Out of 100</p>
              </div>
            </div>
          </div>

          {/* Detailed Stats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-8">
            {/* Most Used Themes */}
            <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-4 sm:p-6 lg:p-8">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <div className="p-2 bg-blue-500/20 rounded-lg">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <h2 className="text-lg sm:text-xl font-bold text-white">Most Used Themes</h2>
              </div>
              
              {stats.mostUsedThemes.length > 0 ? (
                <div className="space-y-3 sm:space-y-4">
                  {stats.mostUsedThemes.map((theme) => {
                    const maxCount = Math.max(...stats.mostUsedThemes.map(t => t.count));
                    const percentage = (theme.count / maxCount) * 100;
                    return (
                      <div key={theme.name} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-300 font-medium text-sm sm:text-base">{theme.name}</span>
                          <span className="text-blue-400 font-semibold text-sm sm:text-base">{theme.count}</span>
                        </div>
                        <div className="w-full bg-slate-700/50 rounded-full h-2">
                          <div 
                            className="bg-gradient-to-r from-blue-500 to-blue-400 h-2 rounded-full transition-all duration-1000 ease-out"
                            style={{ width: `${percentage}%` }}
                          ></div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">🎨</div>
                  <p className="text-gray-400 mb-2 text-sm sm:text-base">No themes used yet</p>
                  <p className="text-gray-500 text-xs sm:text-sm">Start creating stories to see your theme preferences!</p>
                </div>
              )}
            </div>

            {/* Most Used Tags */}
            <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-4 sm:p-6 lg:p-8">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <div className="p-2 bg-purple-500/20 rounded-lg">
                  <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                  </svg>
                </div>
                <h2 className="text-lg sm:text-xl font-bold text-white">Popular Tags</h2>
              </div>
              
              {stats.mostUsedTags.length > 0 ? (
                <div className="space-y-2 sm:space-y-3">
                  {stats.mostUsedTags.map((tag, index) => (
                    <div key={tag.name} className="flex items-center justify-between bg-slate-700/30 hover:bg-slate-700/50 rounded-lg px-3 sm:px-4 py-2 sm:py-3 transition-colors group">
                      <div className="flex items-center gap-2 sm:gap-3">
                        <div className="w-6 h-6 sm:w-8 sm:h-8 bg-purple-500/20 rounded-full flex items-center justify-center text-purple-400 font-bold text-xs sm:text-sm">
                          {index + 1}
                        </div>
                        <span className="text-gray-300 font-medium group-hover:text-white transition-colors text-sm sm:text-base">{tag.name}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-purple-400 font-semibold text-sm sm:text-base">{tag.count}</span>
                        <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-6 sm:py-8">
                  <div className="text-4xl sm:text-6xl mb-3 sm:mb-4">🏷️</div>
                  <p className="text-gray-400 mb-2 text-sm sm:text-base">No tags used yet</p>
                  <p className="text-gray-500 text-xs sm:text-sm">Add tags to your stories to track your favorite topics!</p>
                </div>
              )}
            </div>
          </div>

          {/* Writing Progress */}
          {stats.totalStories > 0 && (
            <div className="mt-6 sm:mt-8 bg-slate-800/80 border border-slate-700/50 rounded-xl p-4 sm:p-6 lg:p-8">
              <div className="flex items-center gap-3 mb-4 sm:mb-6">
                <div className="p-2 bg-emerald-500/20 rounded-lg">
                  <svg className="w-5 h-5 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <h2 className="text-lg sm:text-xl font-bold text-white">Writing Journey</h2>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
                <div className="text-center bg-slate-700/30 rounded-lg p-4">
                  <div className="text-3xl sm:text-4xl mb-2">🌱</div>
                  <p className="text-gray-400 text-xs sm:text-sm mb-1">Getting Started</p>
                  <p className="text-white font-semibold text-sm sm:text-base">
                    {stats.totalStories >= 1 ? '✓ Complete' : 'In Progress'}
                  </p>
                </div>
                <div className="text-center bg-slate-700/30 rounded-lg p-4">
                  <div className="text-3xl sm:text-4xl mb-2">🌿</div>
                  <p className="text-gray-400 text-xs sm:text-sm mb-1">Building Momentum</p>
                  <p className="text-white font-semibold text-sm sm:text-base">
                    {stats.totalStories >= 5 ? '✓ Complete' : `${stats.totalStories}/5`}
                  </p>
                </div>
                <div className="text-center bg-slate-700/30 rounded-lg p-4">
                  <div className="text-3xl sm:text-4xl mb-2">🌳</div>
                  <p className="text-gray-400 text-xs sm:text-sm mb-1">Storytelling Master</p>
                  <p className="text-white font-semibold text-sm sm:text-base">
                    {stats.totalStories >= 10 ? '✓ Complete' : `${stats.totalStories}/10`}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>

      <Footer />
    </div>
  );
}