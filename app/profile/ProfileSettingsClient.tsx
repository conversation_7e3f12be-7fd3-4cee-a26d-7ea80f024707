'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { dataApi, userApi, handleApiError } from '@/lib/utils/api';
import { useApiCall } from '@/app/hooks/api/useApiCall';

interface UserPreferences {
  primaryTheme: string;
  secondaryTheme: string;
  preferredAgeRange: string;
  voice: string;
}

interface Theme {
  id: string;
  name: string;
  description: string;
}

interface AgeRange {
  id: string;
  range: string;
}

interface Voice {
  id: string;
  name: string;
  gender: string;
  voice: string;
  sample_path?: string;
}

export default function ProfileSettingsClient() {
  const { data: session } = useSession();
  const [preferences, setPreferences] = useState<UserPreferences>({
    primaryTheme: 'Virtues',
    secondaryTheme: 'Historical',
    preferredAgeRange: '6-8',
    voice: ''
  });
  const [themes, setThemes] = useState<Theme[]>([]);
  const [ageRanges, setAgeRanges] = useState<AgeRange[]>([]);
  const [voices, setVoices] = useState<Voice[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [currentAudio, setCurrentAudio] = useState<HTMLAudioElement | null>(null);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  
  const saveCall = useApiCall();

  // Cleanup audio when component unmounts
  useEffect(() => {
    return () => {
      if (currentAudio) {
        currentAudio.pause();
        currentAudio.currentTime = 0;
      }
    };
  }, [currentAudio]);

  const stopCurrentAudio = () => {
    if (currentAudio) {
      currentAudio.pause();
      currentAudio.currentTime = 0;
      setCurrentAudio(null);
    }
  };

  const playVoiceSample = (voiceId: string) => {
    // Stop current audio if playing
    stopCurrentAudio();
    
    // Find the selected voice and play its sample - convert IDs to string for comparison
    const selectedVoice = voices.find(voice => String(voice.id) === String(voiceId));
    if (selectedVoice) {
      // Create and play new voice sample
      const audio = new Audio(`/voice_samples/${selectedVoice.sample_path}`);
      
      audio.play().catch(error => {
        console.error('Error playing voice sample:', error);
      });
      
      setCurrentAudio(audio);
    }
  };

  const handleVoiceChange = (voiceId: string) => {
    setPreferences(prev => ({ ...prev, voice: voiceId }));
    playVoiceSample(voiceId);
  };

  useEffect(() => {
    const fetchData = async () => {
      try {
        const { themes: themesData, ageRanges: ageRangesData, voices: voicesData } = await dataApi.getAllData();
        
        setThemes(themesData);
        setAgeRanges(ageRangesData);
        setVoices(voicesData);

        // Set default preferences based on available data
        const defaultPrefs: Partial<UserPreferences> = {};
        
        if (themesData.length > 0) {
          defaultPrefs.primaryTheme = themesData[0].description || themesData[0].name;
          defaultPrefs.secondaryTheme = themesData[1]?.description || themesData[1]?.name || defaultPrefs.primaryTheme;
        }
        
        if (ageRangesData.length > 0) {
          defaultPrefs.preferredAgeRange = ageRangesData[0].range;
        }
        
        if (voicesData.length > 0) {
          defaultPrefs.voice = String(voicesData[0].id);
        }

        // Apply defaults first
        setPreferences(prev => ({ ...prev, ...defaultPrefs }));

        // Then fetch user preferences from API to override defaults
        try {
          const userPrefs = await userApi.getPreferences();
          setPreferences(userPrefs);
        } catch (error) {
          console.error('Error fetching user preferences:', handleApiError(error));
        }
      } catch (error) {
        console.error('Error fetching data:', handleApiError(error));
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleUpdatePreferences = async () => {
    if (!session?.user) return;

    // Stop any playing audio when updating preferences
    stopCurrentAudio();

    try {
      await saveCall.execute(() => userApi.updatePreferences(preferences));
      
      // Show success message
      setShowSuccessMessage(true);
      setTimeout(() => setShowSuccessMessage(false), 3000);
    } catch (error) {
      console.error('Error updating preferences:', handleApiError(error));
      alert('Failed to update preferences. Please try again.');
    }
  };

  if (isLoading) {
    return (
      <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-8">
        <div className="flex flex-col items-center justify-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-300">Loading your preferences...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-8">
        {/* Preferences Header */}
        <div className="flex items-center gap-3 mb-8">
          <div className="p-2 bg-blue-500/20 rounded-lg">
            <svg className="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100-4m0 4v2m0-6V4" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-white">Story Creation Preferences</h2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {/* Primary Theme */}
          <div className="bg-slate-700/30 rounded-lg p-4">
            <label className="flex items-center gap-2 text-sm font-medium text-white mb-3">
              <svg className="w-4 h-4 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              Primary Theme
            </label>
            <select
              value={preferences.primaryTheme}
              onChange={(e) => setPreferences(prev => ({ ...prev, primaryTheme: e.target.value }))}
              className="w-full px-4 py-3 bg-slate-600/50 border border-slate-500 rounded-lg text-white focus:outline-none focus:border-purple-500 focus:ring-2 focus:ring-purple-500/20 transition-all"
            >
              {themes.map((theme) => (
                <option key={theme.id} value={theme.description || theme.name}>
                  {theme.description || theme.name}
                </option>
              ))}
            </select>
            <p className="text-gray-400 text-xs mt-2">Main story theme for your tales</p>
          </div>

          {/* Secondary Theme */}
          <div className="bg-slate-700/30 rounded-lg p-4">
            <label className="flex items-center gap-2 text-sm font-medium text-white mb-3">
              <svg className="w-4 h-4 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
              Secondary Theme
            </label>
            <select
              value={preferences.secondaryTheme}
              onChange={(e) => setPreferences(prev => ({ ...prev, secondaryTheme: e.target.value }))}
              className="w-full px-4 py-3 bg-slate-600/50 border border-slate-500 rounded-lg text-white focus:outline-none focus:border-indigo-500 focus:ring-2 focus:ring-indigo-500/20 transition-all"
            >
              {themes.map((theme) => (
                <option key={theme.id} value={theme.description || theme.name}>
                  {theme.description || theme.name}
                </option>
              ))}
            </select>
            <p className="text-gray-400 text-xs mt-2">Supporting theme for variety</p>
          </div>

          {/* Preferred Age Range */}
          <div className="bg-slate-700/30 rounded-lg p-4">
            <label className="flex items-center gap-2 text-sm font-medium text-white mb-3">
              <svg className="w-4 h-4 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
              </svg>
              Preferred Age Range
            </label>
            <select
              value={preferences.preferredAgeRange}
              onChange={(e) => setPreferences(prev => ({ ...prev, preferredAgeRange: e.target.value }))}
              className="w-full px-4 py-3 bg-slate-600/50 border border-slate-500 rounded-lg text-white focus:outline-none focus:border-emerald-500 focus:ring-2 focus:ring-emerald-500/20 transition-all"
            >
              {ageRanges.map((range) => (
                <option key={range.id} value={range.range}>
                  {range.range} years old
                </option>
              ))}
            </select>
            <p className="text-gray-400 text-xs mt-2">Target audience for your stories</p>
          </div>

          {/* Voice */}
          <div className="bg-slate-700/30 rounded-lg p-4">
            <label className="flex items-center gap-2 text-sm font-medium text-white mb-3">
              <svg className="w-4 h-4 text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
              </svg>
              Narrator Voice
            </label>
            <select
              value={preferences.voice}
              onChange={(e) => handleVoiceChange(e.target.value)}
              className="w-full px-4 py-3 bg-slate-600/50 border border-slate-500 rounded-lg text-white focus:outline-none focus:border-amber-500 focus:ring-2 focus:ring-amber-500/20 transition-all"
            >
              {voices.length === 0 ? (
                <option value="">Loading voices...</option>
              ) : (
                voices.map((voice) => (
                  <option key={voice.id} value={voice.id}>
                    {voice.name} - {voice.gender.charAt(0).toUpperCase() + voice.gender.slice(1)}
                  </option>
                ))
              )}
            </select>
            <p className="text-gray-400 text-xs mt-2">Voice for audio narration</p>
          </div>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="mb-6 bg-green-900/40 border border-green-600/50 rounded-lg p-4">
            <div className="flex items-center gap-3">
              <div className="text-2xl">✅</div>
              <div>
                <p className="text-green-200 font-medium mb-1">Success</p>
                <p className="text-green-300 text-sm">Your preferences have been updated successfully!</p>
              </div>
            </div>
          </div>
        )}

        {/* Update Button */}
        <div className="flex justify-end">
          <button
            onClick={handleUpdatePreferences}
            disabled={saveCall.loading}
            className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 disabled:from-gray-600 disabled:to-gray-700 disabled:cursor-not-allowed text-white py-3 px-6 rounded-lg font-medium transition-all transform hover:scale-[1.02] disabled:hover:scale-100"
          >
            {saveCall.loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                Updating...
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Update Preferences
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}