import { Metadata } from 'next';
import Navigation from './components/Navigation';
import Footer from './components/Footer';
import HeroSection from './components/home/<USER>';
import HowItWorksSection from './components/home/<USER>';
import FeaturedStoriesSection from './components/home/<USER>';
import TestimonialsSection from './components/home/<USER>';
import CTASection from './components/home/<USER>';
import StructuredData from './components/StructuredData';

export const metadata: Metadata = {
  title: 'MyStoryMaker - Create Magical Stories for Your Children',
  description: 'Transform simple story ideas into magical tales with AI. Create personalized children\'s stories that bring families together through the power of storytelling.',
};

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <StructuredData type="website" data={{}} />
      <StructuredData type="organization" data={{}} />
      {/* Skip Navigation Link */}
      <a 
        href="#main-content" 
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-lg z-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
      >
        Skip to main content
      </a>
      <Navigation currentPage="home" />
      <main id="main-content">
        <HeroSection />
        <HowItWorksSection />
        <FeaturedStoriesSection />
        <TestimonialsSection />
        <CTASection />
      </main>
      <Footer />
    </div>
  );
}
