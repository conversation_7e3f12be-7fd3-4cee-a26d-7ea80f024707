interface ImagePromptFormProps {
  userPrompt: string;
  isLoading: boolean;
  canRegenerate: boolean;
  onPromptChange: (prompt: string) => void;
  onSubmit: () => void;
  onClose: () => void;
  error: string | null;
}

export default function ImagePromptForm({
  userPrompt,
  isLoading,
  canRegenerate,
  onPromptChange,
  onSubmit,
  onClose,
  error,
}: ImagePromptFormProps) {
  const handleSubmit = () => {
    onSubmit();
  };

  return (
    <>
      {/* Picture Magic Words */}
      <div>
        <div className="flex items-center gap-2 mb-2">
          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
          </svg>
          <span className="text-white font-medium text-sm sm:text-base">Picture Magic Words</span>
        </div>
        <p className="text-slate-400 text-xs sm:text-sm mb-3">
          Add special details for your new picture!
        </p>
        <textarea
          value={userPrompt}
          onChange={(e) => onPromptChange(e.target.value)}
          placeholder="Example: a red ball, a happy dog, a sunny day..."
          className="w-full h-16 sm:h-20 bg-slate-700 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none text-sm"
          disabled={isLoading}
        />
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-3 p-2 bg-red-500/10 border border-red-500/30 rounded-lg">
          <p className="text-red-400 text-xs sm:text-sm">{error}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex gap-2 sm:gap-2 justify-end">
        <button
          onClick={onClose}
          className="px-3 py-2 sm:py-1.5 bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white border border-slate-600 hover:border-slate-500 rounded-lg transition-colors text-sm"
          disabled={isLoading}
        >
          Close
        </button>
        <button
          onClick={handleSubmit}
          disabled={isLoading || !userPrompt.trim() || !canRegenerate}
          className="bg-blue-600 hover:bg-blue-700 disabled:bg-slate-600 text-white px-4 py-2 sm:py-1.5 rounded-lg font-semibold transition-all flex items-center justify-center gap-2 disabled:cursor-not-allowed text-sm shadow-lg hover:shadow-xl hover:scale-105"
        >
          {isLoading ? (
            <>
              <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
              Creating...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
              </svg>
              Create New Picture!
            </>
          )}
        </button>
      </div>
    </>
  );
}