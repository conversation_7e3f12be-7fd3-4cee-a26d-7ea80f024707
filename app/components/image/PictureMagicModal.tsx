'use client';

import { useImageManagement } from '../../hooks/image/useImageManagement';
import { PictureMagicModalProps } from '../../types/image';
import ImagePowersStatus from './ImagePowersStatus';
import ImageGallery from './ImageGallery';
import ImagePromptForm from './ImagePromptForm';

export default function PictureMagicModal({
  storyUuid,
  isOpen,
  onClose,
  onSuccess
}: PictureMagicModalProps) {
  const {
    state,
    handleSetDefault,
    handleDeleteImage,
    handleRegenerateImage,
    handleRefreshPlan,
    setUserPrompt,
  } = useImageManagement(storyUuid, isOpen);

  const handleSetDefaultImage = (imageId: number) => {
    handleSetDefault(imageId, onSuccess);
  };

  const handleDeleteImageWithCallback = (imageId: number) => {
    handleDeleteImage(imageId);
  };

  const handleSubmitForm = () => {
    handleRegenerateImage(onSuccess);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-slate-800 rounded-xl border-4 border-slate-600/50 w-full max-w-4xl max-h-[95vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b border-slate-700">
          <div className="flex items-center gap-2 sm:gap-3">
            <div className="w-8 h-8 sm:w-10 sm:h-10 bg-gradient-to-br from-purple-500 to-blue-500 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 sm:w-6 sm:h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
            </div>
            <h2 className="text-lg sm:text-xl font-bold text-white">Picture Magic</h2>
          </div>
          <button
            onClick={onClose}
            className="text-slate-400 hover:text-white transition-colors p-1"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="p-3 sm:p-4 overflow-y-auto max-h-[calc(95vh-80px)] sm:max-h-[calc(95vh-100px)] space-y-6 sm:space-y-8">
          {/* Magic Picture Powers */}
          <ImagePowersStatus
            remaining={state.limits?.remaining ?? null}
            isRefreshingPlan={state.isRefreshingPlan}
            onRefreshPlan={handleRefreshPlan}
          />

          {/* Your Pictures */}
                      <ImageGallery
              images={state.images}
              isLoading={state.isLoadingImages}
              onSetDefault={handleSetDefaultImage}
              onDeleteImage={handleDeleteImageWithCallback}
            />

          {/* Picture Magic Words Form */}
          <ImagePromptForm
            userPrompt={state.userPrompt}
            isLoading={state.isLoading}
            canRegenerate={state.limits?.canRegenerate ?? false}
            onPromptChange={setUserPrompt}
            onSubmit={handleSubmitForm}
            onClose={onClose}
            error={state.error}
          />
        </div>
      </div>
    </div>
  );
}