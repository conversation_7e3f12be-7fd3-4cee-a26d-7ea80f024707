interface ImagePowersStatusProps {
  remaining: number | null;
  isRefreshingPlan: boolean;
  onRefreshPlan: () => void;
}

export default function ImagePowersStatus({
  remaining,
  isRefreshingPlan,
  onRefreshPlan,
}: ImagePowersStatusProps) {
  return (
    <div className="bg-gradient-to-r from-purple-600/20 to-blue-600/20 border border-purple-500/30 rounded-lg p-3">
      <div className="flex items-center justify-between flex-wrap gap-2">
        <div className="flex items-center gap-2">
          <svg className="w-4 h-4 sm:w-5 sm:h-5 text-yellow-400" fill="currentColor" viewBox="0 0 24 24">
            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
          </svg>
          <span className="text-white font-medium text-sm sm:text-base">Your Magic Picture Powers</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="text-yellow-400 font-bold text-sm sm:text-base">
            {remaining !== null ? `${remaining} left` : '...'}
          </div>
          <button
            onClick={onRefreshPlan}
            disabled={isRefreshingPlan}
            className="text-xs bg-slate-700 hover:bg-slate-600 disabled:bg-slate-800 text-slate-300 px-2 py-1 rounded transition-colors"
            title="Refresh subscription plan"
          >
            {isRefreshingPlan ? (
              <div className="w-3 h-3 border border-slate-400 border-t-transparent rounded-full animate-spin"></div>
            ) : (
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            )}
          </button>
        </div>
      </div>
      <p className="text-slate-300 text-xs sm:text-sm mt-2">
        {remaining !== null ? `You can change your picture ${remaining} more times for this story!` : 'Loading...'}
      </p>
    </div>
  );
}