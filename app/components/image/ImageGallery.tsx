import Image from 'next/image';
import { ImageData } from '../../types/image';
import { InlineLoadingSpinner } from '../ui/LoadingSpinner';
import ConfirmationModal from '../ui/ConfirmationModal';
import { useState, useEffect } from 'react';

interface ImageGalleryProps {
  images: ImageData[];
  isLoading: boolean;
  onSetDefault: (imageId: number) => void;
  onDeleteImage: (imageId: number) => void;
}

export default function ImageGallery({ images, isLoading, onSetDefault, onDeleteImage }: ImageGalleryProps) {
  const [deletingImageId, setDeletingImageId] = useState<number | null>(null);
  const [confirmDeleteImageId, setConfirmDeleteImageId] = useState<number | null>(null);

  // Reset deleting state when image is removed from array
  useEffect(() => {
    if (deletingImageId && !images.find(img => img.id === deletingImageId)) {
      setDeletingImageId(null);
    }
  }, [images, deletingImageId]);

  // Find the first (oldest) image by created_at timestamp
  const getFirstImageId = () => {
    if (images.length === 0) return null;
    return images.reduce((oldest, current) => 
      new Date(current.created_at) < new Date(oldest.created_at) ? current : oldest
    ).id;
  };

  const firstImageId = getFirstImageId();

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    });
  };

  const handleDeleteClick = (e: React.MouseEvent, imageId: number) => {
    e.stopPropagation(); // Prevent triggering the set default action
    setConfirmDeleteImageId(imageId);
  };

  const handleConfirmDelete = () => {
    if (confirmDeleteImageId) {
      setDeletingImageId(confirmDeleteImageId);
      onDeleteImage(confirmDeleteImageId);
      setConfirmDeleteImageId(null);
    }
  };

  const handleCancelDelete = () => {
    setConfirmDeleteImageId(null);
  };

  return (
    <>
      <div className="">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 sm:w-5 sm:h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <span className="text-white font-medium text-sm sm:text-base">Your Pictures</span>
          </div>
          <span className="text-slate-400 text-xs sm:text-sm hidden sm:block">Tap a picture to choose it for your story</span>
          <span className="text-slate-400 text-xs sm:hidden">Tap to choose</span>
        </div>

        {isLoading ? (
          <InlineLoadingSpinner message="Loading images..." />
        ) : images.length === 0 ? (
          <div className="text-center py-6 sm:py-8 text-slate-400">
            <p className="text-sm sm:text-base">No pictures yet. Create your first one below!</p>
          </div>
        ) : (
          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-2 sm:gap-3">
            {images.map((image, index) => (
              <div
                key={image.id}
                className={`relative group cursor-pointer rounded-lg overflow-hidden border-2 transition-all ${
                  image.default 
                    ? 'border-blue-500 ring-2 ring-blue-500/50' 
                    : 'border-slate-600 hover:border-slate-500'
                }`}
                onClick={() => onSetDefault(image.id)}
              >
                <div className="aspect-[3/2] relative">
                  <Image
                    key={`${image.id}-${image.created_at}`}
                    src={image.url}
                    alt={`Picture ${index + 1}`}
                    fill
                    className="object-cover transition-opacity duration-300"
                    sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                    priority={index < 4}
                    placeholder="blur"
                    blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Rq5TaULdtqdu9n5cRxKbdnhkVZEaNgVZWUqwBBBBBHcEV9K9Dw=="
                  />
                  


                  {image.default && (
                    <div className="absolute top-1 left-1 sm:top-2 sm:left-2 bg-blue-500 text-white px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs font-medium">
                      ⭐ Chosen
                    </div>
                  )}
                  {image.id === firstImageId && (
                    <div className="absolute top-1 left-1 sm:top-2 sm:left-2 bg-yellow-500 text-black px-1.5 py-0.5 sm:px-2 sm:py-1 rounded text-xs font-bold" style={{ marginTop: image.default ? '1.75rem' : '0' }}>
                      First
                    </div>
                  )}
                </div>
                <div className="p-1 sm:p-1.5 bg-slate-700 flex items-center justify-between">
                  <div className="text-xs text-slate-300">
                    {formatDate(image.created_at)}
                  </div>
                  {/* Delete button - only show if there are multiple images */}
                  {images.length > 1 && (
                    <button
                      onClick={(e) => handleDeleteClick(e, image.id)}
                      disabled={deletingImageId === image.id}
                      className="w-5 h-5 sm:w-6 sm:h-6 bg-red-600 hover:bg-red-700 disabled:bg-red-800 text-white rounded-full flex items-center justify-center transition-colors duration-200"
                      title="Delete this picture"
                    >
                      {deletingImageId === image.id ? (
                        <div className="w-2.5 h-2.5 border border-white border-t-transparent rounded-full animate-spin"></div>
                      ) : (
                        <svg className="w-2.5 h-2.5 sm:w-3 sm:h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      )}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={confirmDeleteImageId !== null}
        title="Delete Picture"
        message="Are you sure you want to delete this picture? This action cannot be undone."
        confirmText="Delete Picture"
        cancelText="Cancel"
        onConfirm={handleConfirmDelete}
        onCancel={handleCancelDelete}
        isLoading={deletingImageId === confirmDeleteImageId}
      />
    </>
  );
}