interface StructuredDataProps {
  type: 'website' | 'article' | 'organization' | 'breadcrumb';
  data: ArticleData | BreadcrumbItem[] | Record<string, never>;
}

interface BreadcrumbItem {
  name: string;
  url: string;
}

interface ArticleData {
  title: string;
  description: string;
  image: string;
  url: string;
  datePublished: string;
  dateModified?: string;
  themes?: string[];
  ageRange: string;
}

export default function StructuredData({ type, data }: StructuredDataProps) {
  const generateStructuredData = () => {
    const baseUrl = 'https://mystorymaker.app';
    
    switch (type) {
      case 'website':
        return {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": "MyStoryMaker",
          "description": "Create magical personalized stories for your children with AI technology",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/public/stories?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          },
          "publisher": {
            "@type": "Organization",
            "name": "MyStoryMaker",
            "url": baseUrl,
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            }
          }
        };

      case 'organization':
        return {
          "@context": "https://schema.org",
          "@type": "Organization",
          "name": "MyStoryMaker",
          "description": "AI-powered storytelling platform for creating personalized children's stories",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/images/logo.png`,
            "width": 512,
            "height": 512
          },
          "sameAs": [
            "https://twitter.com/mystorymaker",
            "https://facebook.com/mystorymaker"
          ],
          "contactPoint": {
            "@type": "ContactPoint",
            "contactType": "customer service",
            "url": `${baseUrl}/contact`
          }
        };

      case 'article':
        const articleData = data as ArticleData;
        return {
          "@context": "https://schema.org",
          "@type": "Article",
          "headline": articleData.title,
          "description": articleData.description,
          "image": articleData.image,
          "author": {
            "@type": "Organization",
            "name": "MyStoryMaker"
          },
          "publisher": {
            "@type": "Organization",
            "name": "MyStoryMaker",
            "logo": {
              "@type": "ImageObject",
              "url": `${baseUrl}/images/logo.png`
            }
          },
          "datePublished": articleData.datePublished,
          "dateModified": articleData.dateModified || articleData.datePublished,
          "mainEntityOfPage": {
            "@type": "WebPage",
            "@id": articleData.url
          },
          "genre": articleData.themes?.join(', '),
          "audience": {
            "@type": "Audience",
            "audienceType": articleData.ageRange
          }
        };

      case 'breadcrumb':
        const breadcrumbData = data as BreadcrumbItem[];
        return {
          "@context": "https://schema.org",
          "@type": "BreadcrumbList",
          "itemListElement": breadcrumbData.map((item: BreadcrumbItem, index: number) => ({
            "@type": "ListItem",
            "position": index + 1,
            "name": item.name,
            "item": item.url
          }))
        };

      default:
        return null;
    }
  };

  const structuredData = generateStructuredData();

  if (!structuredData) return null;

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(structuredData)
      }}
    />
  );
} 