import { useState } from 'react';
import { getBaseUrl } from '../../../../lib/utils';
import { trackStoryShared } from '../../analytics';
import { ShareRecord, ShareType } from '../types';

// Function to create URL-friendly slug from title (matches the one in story page)
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

export function useShareModal(storyId: string, storyTitle: string, isPublic: boolean) {
  const [shareType, setShareType] = useState<ShareType>('link');
  const [recipientEmail, setRecipientEmail] = useState('');
  const [isSharing, setIsSharing] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [existingShares, setExistingShares] = useState<ShareRecord[]>([]);
  const [showExistingShares, setShowExistingShares] = useState(false);
  const [localIsPublic, setLocalIsPublic] = useState(isPublic);
  const [isUpdatingVisibility, setIsUpdatingVisibility] = useState(false);

  const loadExistingShares = async () => {
    try {
      const response = await fetch(`/api/stories/${storyId}/share`);
      if (response.ok) {
        const shares = await response.json();
        setExistingShares(shares);
      }
    } catch (error) {
      console.error('Error loading existing shares:', error);
    }
  };

  const handleShare = async () => {
    if (shareType === 'email' && (!recipientEmail || !recipientEmail.includes('@'))) {
      setError('Please enter a valid email address.');
      return;
    }

    if (shareType === 'facebook' && !localIsPublic) {
      setError('Story must be public to share on Facebook.');
      return;
    }

    setIsSharing(true);
    setError(null);
    setSuccess(null);

    try {
      if (shareType === 'facebook') {
        trackStoryShared('facebook');
        // Use the correct story URL format with UUID and slug
        const slug = createSlug(storyTitle);
        const publicUrl = `${getBaseUrl()}/stories/${storyId}/${slug}`;
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(publicUrl)}`;
        const popup = window.open(
          facebookUrl,
          'facebook-share',
          'width=600,height=400,scrollbars=yes,resizable=yes'
        );
        
        if (!popup) {
          throw new Error('Popup blocked');
        }
        
        setSuccess('Facebook share dialog opened!');
        setIsSharing(false);
        return;
      }

      const response = await fetch(`/api/stories/${storyId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: shareType,
          recipient_email: shareType === 'email' ? recipientEmail : null,
          expires_in_days: 30
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create share');
      }

      setShareUrl(data.share_url);
      
      if (shareType === 'email') {
        trackStoryShared('email');
        setSuccess(`Share link sent to ${recipientEmail}!`);
        setRecipientEmail('');
      } else {
        trackStoryShared('link');
        setSuccess('Share link generated successfully!');
      }

      await loadExistingShares();

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create share');
    } finally {
      setIsSharing(false);
    }
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setSuccess('Link copied to clipboard!');
      setTimeout(() => setSuccess(null), 3000);
    } catch {
      setError('Failed to copy link to clipboard');
    }
  };

  const updateStoryVisibility = async (makePublic: boolean) => {
    setIsUpdatingVisibility(true);
    setError(null);

    try {
      const response = await fetch(`/api/stories/${storyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_public: makePublic
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update story visibility');
      }

      setLocalIsPublic(makePublic);
      setSuccess(makePublic ? 'Story is now public!' : 'Story is now private!');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update story visibility');
    } finally {
      setIsUpdatingVisibility(false);
    }
  };

  return {
    shareType,
    setShareType,
    recipientEmail,
    setRecipientEmail,
    isSharing,
    shareUrl,
    error,
    success,
    existingShares,
    showExistingShares,
    setShowExistingShares,
    localIsPublic,
    isUpdatingVisibility,
    loadExistingShares,
    handleShare,
    copyToClipboard,
    updateStoryVisibility,
  };
} 