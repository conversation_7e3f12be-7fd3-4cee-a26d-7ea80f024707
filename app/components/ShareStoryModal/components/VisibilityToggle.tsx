interface VisibilityToggleProps {
  isPublic: boolean;
  isUpdating: boolean;
  onToggle: (makePublic: boolean) => void;
}

export default function VisibilityToggle({ 
  isPublic, 
  isUpdating, 
  onToggle 
}: VisibilityToggleProps) {
  return (
    <div className="bg-slate-700 rounded-lg p-4">
      <div className="flex items-center justify-between mb-2">
        <span className="text-white font-medium">Story Visibility</span>
        <span className={`px-2 py-1 rounded text-xs font-medium ${
          isPublic 
            ? 'bg-green-600/20 text-green-300' 
            : 'bg-gray-600/20 text-gray-300'
        }`}>
          {isPublic ? '🌐 Public' : '🔒 Private'}
        </span>
      </div>
      <p className="text-gray-300 text-sm mb-3">
        {isPublic 
          ? 'This story is visible to everyone and can be shared on social media.'
          : 'This story is private. Make it public to enable Facebook sharing.'
        }
      </p>
      <button
        onClick={() => onToggle(!isPublic)}
        disabled={isUpdating}
        className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors"
      >
        {isUpdating 
          ? 'Updating...' 
          : isPublic 
            ? 'Make Private' 
            : 'Make Public'
        }
      </button>
    </div>
  );
} 