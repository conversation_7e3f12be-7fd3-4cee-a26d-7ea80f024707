import { ShareRecord } from '../types';

interface ExistingSharesProps {
  shares: ShareRecord[];
  showShares: boolean;
  onToggleShares: () => void;
  onCopyToClipboard: (url: string) => void;
}

export default function ExistingShares({ 
  shares, 
  showShares, 
  onToggleShares, 
  onCopyToClipboard 
}: ExistingSharesProps) {
  return (
    <div className="border-t border-slate-700 pt-4">
      <button
        onClick={onToggleShares}
        className="text-blue-400 hover:text-blue-300 text-sm"
      >
        {showShares ? 'Hide' : 'Show'} Existing Shares ({shares.length})
      </button>

      {showShares && (
        <div className="mt-4 space-y-3">
          {shares.length === 0 ? (
            <p className="text-gray-400 text-sm">No existing shares</p>
          ) : (
            shares.map((share) => (
              <div key={share.id} className="bg-slate-700 rounded-lg p-3">
                <div className="flex justify-between items-start mb-2">
                  <span className="text-white text-sm font-medium">
                    {share.type === 'email' ? '📧 Email' : '🔗 Link'} Share
                  </span>
                  <span className={`text-xs px-2 py-1 rounded ${
                    share.is_expired 
                      ? 'bg-red-600/20 text-red-300' 
                      : 'bg-green-600/20 text-green-300'
                  }`}>
                    {share.is_expired ? 'Expired' : 'Active'}
                  </span>
                </div>
                {share.recipient_email && (
                  <p className="text-gray-300 text-sm mb-2">To: {share.recipient_email}</p>
                )}
                <div className="flex gap-2">
                  <input
                    type="text"
                    value={share.share_url}
                    readOnly
                    className="flex-1 px-2 py-1 bg-slate-600 border border-slate-500 rounded text-white text-xs"
                  />
                  <button
                    onClick={() => onCopyToClipboard(share.share_url)}
                    className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
                  >
                    Copy
                  </button>
                </div>
                <p className="text-gray-400 text-xs mt-2">
                  Created: {new Date(share.created_at).toLocaleDateString()} | 
                  Expires: {new Date(share.expires_at).toLocaleDateString()}
                </p>
              </div>
            ))
          )}
        </div>
      )}
    </div>
  );
} 