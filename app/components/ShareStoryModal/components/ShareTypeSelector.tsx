import { ShareType } from '../types';

interface ShareTypeSelectorProps {
  shareType: ShareType;
  onShareTypeChange: (type: ShareType) => void;
  isPublic: boolean;
}

export default function ShareTypeSelector({ 
  shareType, 
  onShareTypeChange, 
  isPublic 
}: ShareTypeSelectorProps) {
  return (
    <div className="mb-6">
      <label className="block text-white font-medium mb-3">Share Method</label>
      <div className="flex flex-col gap-3">
        <label className="flex items-center">
          <input
            type="radio"
            value="link"
            checked={shareType === 'link'}
            onChange={(e) => onShareTypeChange(e.target.value as ShareType)}
            className="mr-2"
          />
          <span className="text-gray-300">🔗 Generate Link</span>
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            value="email"
            checked={shareType === 'email'}
            onChange={(e) => onShareTypeChange(e.target.value as ShareType)}
            className="mr-2"
          />
          <span className="text-gray-300">📧 Send Email</span>
        </label>
        <label className="flex items-center">
          <input
            type="radio"
            value="facebook"
            checked={shareType === 'facebook'}
            onChange={(e) => onShareTypeChange(e.target.value as ShareType)}
            className="mr-2"
            disabled={!isPublic}
          />
          <span className={`${!isPublic ? 'text-gray-500' : 'text-gray-300'}`}>
            📘 Share on Facebook {!isPublic && '(requires public story)'}
          </span>
        </label>
      </div>
    </div>
  );
} 