'use client';

import { ShareStoryModalProps } from './types';
import { useShareModal } from './hooks/useShareModal';
import ShareTypeSelector from './components/ShareTypeSelector';
import VisibilityToggle from './components/VisibilityToggle';
import ExistingShares from './components/ExistingShares';
import ErrorMessage from '../ui/ErrorMessage';
import SuccessMessage from '../ui/SuccessMessage';

export default function ShareStoryModal({ 
  isOpen, 
  onClose, 
  storyId, 
  storyTitle, 
  isPublic 
}: ShareStoryModalProps) {
  const {
    shareType,
    setShareType,
    recipientEmail,
    setRecipientEmail,
    isSharing,
    shareUrl,
    error,
    success,
    existingShares,
    showExistingShares,
    setShowExistingShares,
    localIsPublic,
    isUpdatingVisibility,
    loadExistingShares,
    handleShare,
    copyToClipboard,
    updateStoryVisibility,
  } = useShareModal(storyId, storyTitle, isPublic);

  const handleModalClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleToggleExistingShares = () => {
    setShowExistingShares(!showExistingShares);
    if (!showExistingShares) {
      loadExistingShares();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
      onClick={handleModalClick}
    >
      <div className="bg-slate-800 rounded-lg border-4 border-slate-600/50 p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Share Story</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        <div className="mb-6">
          <h3 className="text-white font-medium mb-4">"{storyTitle}"</h3>
          
          <VisibilityToggle
            isPublic={localIsPublic}
            isUpdating={isUpdatingVisibility}
            onToggle={updateStoryVisibility}
          />
        </div>

        {error && <ErrorMessage message={error} className="mb-4" />}
        {success && <SuccessMessage message={success} className="mb-4" />}

        <ShareTypeSelector
          shareType={shareType}
          onShareTypeChange={setShareType}
          isPublic={localIsPublic}
        />

        {shareType === 'email' && (
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">
              Recipient Email
            </label>
            <input
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder="Enter email address"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>
        )}

        {shareUrl && (
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">Share Link</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm"
              />
              <button
                onClick={() => copyToClipboard(shareUrl)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
              >
                Copy
              </button>
            </div>
          </div>
        )}

        <div className="flex gap-3 mb-6">
          <button
            onClick={handleShare}
            disabled={isSharing}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {isSharing ? 'Creating...' : shareType === 'email' ? 'Send Email' : shareType === 'facebook' ? 'Share on Facebook' : 'Generate Link'}
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
          >
            Close
          </button>
        </div>

        <ExistingShares
          shares={existingShares}
          showShares={showExistingShares}
          onToggleShares={handleToggleExistingShares}
          onCopyToClipboard={copyToClipboard}
        />
      </div>
    </div>
  );
} 