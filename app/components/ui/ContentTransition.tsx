'use client';

import { ReactNode, useState, useEffect } from 'react';

interface ContentTransitionProps {
  children: ReactNode;
  isLoading?: boolean;
  skeleton: ReactNode;
  className?: string;
}

export default function ContentTransition({ 
  children, 
  isLoading = false, 
  skeleton, 
  className = '' 
}: ContentTransitionProps) {
  const [showContent, setShowContent] = useState(false);

  useEffect(() => {
    if (!isLoading) {
      // Small delay to ensure smooth transition
      const timer = setTimeout(() => setShowContent(true), 100);
      return () => clearTimeout(timer);
    } else {
      setShowContent(false);
    }
  }, [isLoading]);

  return (
    <div className={className}>
      <div 
        className={`transition-opacity duration-300 ${
          showContent ? 'opacity-0 pointer-events-none absolute' : 'opacity-100'
        }`}
      >
        {skeleton}
      </div>
      <div 
        className={`transition-opacity duration-300 ${
          showContent ? 'opacity-100' : 'opacity-0'
        }`}
      >
        {children}
      </div>
    </div>
  );
} 