'use client';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

export default function LoadingSpinner({ size = 'md', className = '' }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  return (
    <div className={`animate-spin rounded-full border-2 border-slate-600 border-t-blue-500 ${sizeClasses[size]} ${className}`} />
  );
}

// Page loading component with spinner
export function PageLoadingSpinner({ message = 'Loading...' }: { message?: string }) {
  return (
    <div className="flex flex-col items-center justify-center py-24">
      <LoadingSpinner size="lg" className="mb-4" />
      <p className="text-gray-400 text-lg">{message}</p>
    </div>
  );
}

// Inline loading for smaller components
export function InlineLoadingSpinner({ message }: { message?: string }) {
  return (
    <div className="flex items-center justify-center gap-3 py-8">
      <LoadingSpinner size="md" />
      {message && <span className="text-gray-400">{message}</span>}
    </div>
  );
} 