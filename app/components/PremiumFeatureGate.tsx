'use client';

import { ReactNode } from 'react';
import Link from 'next/link';
import { event } from './analytics';
import { usePremiumFeatures } from '../hooks/usePremiumFeatures';

interface PremiumFeatureGateProps {
  children: ReactNode;
  fallbackMessage?: string;
  featureName?: string;
  showUpgradeButton?: boolean;
  className?: string;
}

export default function PremiumFeatureGate({ 
  children, 
  fallbackMessage,
  featureName = 'This feature',
  showUpgradeButton = true,
  className = ''
}: PremiumFeatureGateProps) {
  const { premiumAccess, isLoading } = usePremiumFeatures();
  
  const handleUpgradeClick = () => {
    event({
      action: 'premium_feature_upgrade_click',
      category: 'conversion',
      label: featureName.toLowerCase().replace(/\s+/g, '_')
    });
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-slate-700 rounded-lg p-4 ${className}`}>
        <div className="h-4 bg-slate-600 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-slate-600 rounded w-1/2"></div>
      </div>
    );
  }

  if (!premiumAccess?.hasAccess) {
    const message = fallbackMessage || `${featureName} is only available for Starter and Family plan subscribers.`;
    
    return (
      <div className={`bg-slate-700 border border-slate-600 rounded-lg p-4 ${className}`}>
        <div className="flex items-start gap-3">
          <div className="flex-shrink-0">
            <svg className="w-5 h-5 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="flex-1">
            <h3 className="text-sm font-medium text-white mb-1">Premium Feature</h3>
            <p className="text-sm text-gray-300 mb-3">{message}</p>
            <p className="text-xs text-gray-400 mb-3">
              Current plan: <span className="font-medium">{premiumAccess?.planName || 'Free'}</span>
            </p>
            {showUpgradeButton && (
              <Link href="/pricing">
                <button 
                  onClick={handleUpgradeClick}
                  className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
                  </svg>
                  Upgrade Plan
                </button>
              </Link>
            )}
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}