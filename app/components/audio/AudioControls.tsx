import { TranscriptionResult } from '../../types/audio';

interface AudioControlsProps {
  hasAudio: boolean;
  audioUrl: string | null;
  isPlaying: boolean;
  isGenerating: boolean;
  isTranscribing: boolean;
  transcriptionStatus: string;
  transcription: TranscriptionResult | null;
  onGenerateAudio: () => void;
  onLoadExistingAudio: () => void;
  onTogglePlayback: () => void;
  onRegenerateAudio: () => void;
  onGenerateTranscription: () => void;
  onRegenerateTranscription: () => void;
}

export default function AudioControls({
  hasAudio,
  audioUrl,
  isPlaying,
  isGenerating,
  isTranscribing,
  transcriptionStatus,
  transcription,
  onGenerateAudio,
  onLoadExistingAudio,
  onTogglePlayback,
  onRegenerateAudio,
  onGenerateTranscription,
  onRegenerateTranscription,
}: AudioControlsProps) {
  return (
    <div className="flex flex-col sm:flex-row gap-2">
      {!hasAudio ? (
        <button
          onClick={onGenerateAudio}
          disabled={isGenerating}
          className="px-6 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
        >
          Listen to Story
        </button>
      ) : !audioUrl ? (
        <button
          onClick={onLoadExistingAudio}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2"
        >
          <div className="w-0 h-0 border-l-4 border-l-white border-y-2 border-y-transparent"></div>
          Play Story
        </button>
      ) : (
        <button
          onClick={onTogglePlayback}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2"
        >
          {isPlaying ? (
            <>
              <div className="w-4 h-4 bg-white rounded-sm"></div>
              Pause
            </>
          ) : (
            <>
              <div className="w-0 h-0 border-l-4 border-l-white border-y-2 border-y-transparent"></div>
              Play Story
            </>
          )}
        </button>
      )}
      
      {hasAudio && !isGenerating && (
        <button
          onClick={onRegenerateAudio}
          disabled={isGenerating}
          className="px-6 py-2 border border-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
        >
          Regenerate Audio
        </button>
      )}

      {hasAudio && transcriptionStatus === 'not_started' && !isTranscribing && (
        <button
          onClick={onGenerateTranscription}
          disabled={isTranscribing}
          className="px-6 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          Read Along
        </button>
      )}

      {hasAudio && transcriptionStatus === 'failed' && !isTranscribing && (
        <button
          onClick={onGenerateTranscription}
          disabled={isTranscribing}
          className="px-6 py-2 bg-red-600 hover:bg-red-700 disabled:bg-red-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          Retry Transcription
        </button>
      )}

      {hasAudio && transcriptionStatus === 'generating' && (
        <button
          disabled
          className="px-6 py-2 bg-purple-800 cursor-not-allowed text-white rounded-lg transition-colors flex items-center gap-2"
        >
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          Generating...
        </button>
      )}

      {transcription && transcriptionStatus === 'complete' && !isTranscribing && (
        <button
          onClick={onRegenerateTranscription}
          disabled={isTranscribing}
          className="px-6 py-2 border border-purple-600 text-purple-400 rounded-lg hover:bg-purple-700 hover:text-white transition-colors"
        >
          Regenerate Read Along
        </button>
      )}
    </div>
  );
}