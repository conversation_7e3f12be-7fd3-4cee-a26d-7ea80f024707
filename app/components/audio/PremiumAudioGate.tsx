import Link from 'next/link';

interface PremiumAudioGateProps {
  planName?: string;
}

export default function PremiumAudioGate({ planName }: PremiumAudioGateProps) {
  return (
    <div className="bg-slate-700 border border-slate-600 rounded-lg p-4">
      <div className="flex items-center gap-2 mb-2">
        <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
          <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
        </svg>
        <span className="text-sm font-medium text-white">Premium Feature</span>
      </div>
      <p className="text-sm text-gray-300 mb-2">
        Audio generation is available for Starter and Family plan subscribers. Transcription features are only available for Family plan subscribers.
      </p>
      <p className="text-xs text-gray-400 mb-3">
        Current plan: <span className="font-medium">{planName || 'Free'}</span>
      </p>
      <Link href="/pricing">
        <button className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
          </svg>
          Upgrade Plan
        </button>
      </Link>
    </div>
  );
}