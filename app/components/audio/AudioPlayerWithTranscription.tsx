'use client';

import { useEffect, useState } from 'react';
import { usePremiumFeatures } from '../../hooks/usePremiumFeatures';
import { useAudioPlayer } from '../../hooks/audio/useAudioPlayer';
import { useTranscription } from '../../hooks/audio/useTranscription';
import { AudioPlayerWithTranscriptionProps } from '../../types/audio';
import PremiumAudioGate from './PremiumAudioGate';
import AudioControls from './AudioControls';
import TranscriptionDisplay from './TranscriptionDisplay';
import GeneratingDialog from '../GeneratingDialog';

export default function AudioPlayerWithTranscription({ 
  storyId, 
  storyText,
  hasExistingAudio = false,
  shareToken
}: AudioPlayerWithTranscriptionProps) {
  const { premiumAccess, isLoading } = usePremiumFeatures();
  const [error, setError] = useState<string | null>(null);
  
  const {
    state: audioState,
    audioRef,
    loadExistingAudio,
    generateAudio,
    togglePlayback,
    handleTimeUpdate,
    handleAudioEnded,
    handleAudioError,
    handleAudioLoaded,
    seekToTime,
    setCurrentWordIndex,
    setHasAudio,
  } = useAudioPlayer();

  const {
    transcription,
    status: transcriptionStatus,
    isTranscribing,
    generateTranscription,
    getCurrentWordIndex,
    getWordStartTime,
  } = useTranscription(storyId, audioState.hasAudio, shareToken);

  // Initialize hasAudio state
  useEffect(() => {
    setHasAudio(hasExistingAudio);
  }, [hasExistingAudio, setHasAudio]);

  // Update current word highlighting based on audio time
  useEffect(() => {
    if (transcription && audioState.isPlaying) {
      const currentWordIndex = getCurrentWordIndex(audioState.currentTime);
      setCurrentWordIndex(currentWordIndex);
    }
  }, [audioState.currentTime, transcription, audioState.isPlaying, getCurrentWordIndex, setCurrentWordIndex]);

  const handleGenerateAudio = async () => {
    try {
      setError(null);
      await generateAudio(storyId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to generate audio');
    }
  };

  const handleLoadExistingAudio = async () => {
    try {
      setError(null);
      await loadExistingAudio(storyId, true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load audio');
    }
  };

  const handleRegenerateAudio = async () => {
    try {
      setError(null);
      await generateAudio(storyId, true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to regenerate audio');
    }
  };

  const handleGenerateTranscription = async () => {
    try {
      setError(null);
      await generateTranscription();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create read along');
    }
  };

  const handleRegenerateTranscription = async () => {
    try {
      setError(null);
      await generateTranscription(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to regenerate read along');
    }
  };

  const handleWordClick = (wordIndex: number) => {
    const startTime = getWordStartTime(wordIndex);
    seekToTime(startTime);
  };

  return (
    <div className="flex flex-col gap-4">
      {audioState.audioUrl && (
        <audio
          ref={audioRef}
          src={audioState.audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleAudioEnded}
          onError={handleAudioError}
          onLoadedData={handleAudioLoaded}
          preload="metadata"
          controls={false}
        />
      )}
      
      {(error || audioState.error) && (
        <div className="text-red-400 text-sm mb-2">
          {error || audioState.error}
        </div>
      )}

      {/* Audio Controls */}
      {isLoading ? (
        <div className="animate-pulse bg-slate-700 rounded-lg h-10 w-32"></div>
      ) : !premiumAccess?.hasAccess ? (
        <PremiumAudioGate planName={premiumAccess?.planName} />
      ) : (
        <AudioControls
          hasAudio={audioState.hasAudio}
          audioUrl={audioState.audioUrl}
          isPlaying={audioState.isPlaying}
          isGenerating={audioState.isGenerating}
          isTranscribing={isTranscribing}
          transcriptionStatus={transcriptionStatus}
          transcription={transcription}
          onGenerateAudio={handleGenerateAudio}
          onLoadExistingAudio={handleLoadExistingAudio}
          onTogglePlayback={togglePlayback}
          onRegenerateAudio={handleRegenerateAudio}
          onGenerateTranscription={handleGenerateTranscription}
          onRegenerateTranscription={handleRegenerateTranscription}
        />
      )}

      {/* Transcription Text with Word Highlighting */}
      <TranscriptionDisplay
        storyText={storyText}
        transcription={transcription}
        currentWordIndex={audioState.currentWordIndex}
        onWordClick={handleWordClick}
      />
      
      {/* Loading Dialogs */}
      <GeneratingDialog isOpen={audioState.isGenerating} type="audio" />
      <GeneratingDialog isOpen={isTranscribing} type="transcription" />
    </div>
  );
}