import { TranscriptionResult } from '../../types/audio';

interface TranscriptionDisplayProps {
  storyText: string;
  transcription: TranscriptionResult | null;
  currentWordIndex: number;
  onWordClick: (wordIndex: number) => void;
}

export default function TranscriptionDisplay({
  storyText,
  transcription,
  currentWordIndex,
  onWordClick,
}: TranscriptionDisplayProps) {
  const renderTranscriptionText = () => {
    if (!transcription) {
      return <p className="text-slate-300 leading-relaxed">{storyText}</p>;
    }

    return (
      <div className="text-slate-300 leading-relaxed">
        {transcription.words.map((word, index: number) => (
          <span
            key={index}
            className={`cursor-pointer transition-colors duration-200 ${
              index === currentWordIndex 
                ? 'bg-blue-500 text-white px-1 rounded' 
                : 'hover:bg-slate-700 px-1 rounded'
            }`}
            onClick={() => onWordClick(index)}
          >
            {word.word}
            {index < transcription.words.length - 1 ? ' ' : ''}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
      <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
        Story Text
        {transcription && (
          <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
            Interactive
          </span>
        )}
      </h3>
      {renderTranscriptionText()}
      {transcription && (
        <p className="text-xs text-slate-500 mt-3">
          Click on any word to jump to that part of the audio
        </p>
      )}
    </div>
  );
}