'use client';

import { ReactNode } from 'react';
import Navigation from '../Navigation';
import Footer from '../Footer';
import PageTransition from '../ui/PageTransition';
import { PageLoadingSpinner } from '../ui/LoadingSpinner';

interface PageLayoutProps {
  children: ReactNode;
  currentPage?: string;
  showNavigation?: boolean;
  showFooter?: boolean;
  className?: string;
}

export default function PageLayout({ 
  children, 
  currentPage,
  showNavigation = true,
  showFooter = true,
  className = ''
}: PageLayoutProps) {
  return (
    <div className={`min-h-screen ${className}`}>
      {showNavigation && <Navigation currentPage={currentPage} />}
      
      <PageTransition>
        <main className="flex-1">
          {children}
        </main>
      </PageTransition>
      
      {showFooter && <Footer />}
    </div>
  );
}

// Loading layout for consistent loading states
export function LoadingPageLayout({ 
  currentPage,
  showNavigation = true,
  showFooter = true 
}: {
  currentPage?: string;
  showNavigation?: boolean;
  showFooter?: boolean;
}) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      {showNavigation && <Navigation currentPage={currentPage} />}
      
      <main className="flex-1">
        <div className="max-w-7xl mx-auto px-4">
          <PageLoadingSpinner message="Loading..." />
        </div>
      </main>
      
      {showFooter && <Footer />}
    </div>
  );
} 