'use client'

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import AccessCodeInput from './AccessCodeInput';

interface SubscriptionData {
  userId: string;
  currentPlan: string;
  hasPremium: boolean;
  subscription: {
    id: number;
    status: string;
    current_period_start: string;
    current_period_end: string;
    cancel_at_period_end: boolean;
    stripe_subscription_id?: string | null;
    stripe_customer_id?: string | null;
    stripe_price_id?: string | null;
    plan: {
      display_name: string;
      price_monthly: number;
      price_yearly: number;
    };
  } | null;
}

export default function SubscriptionManager() {
  const { data: session } = useSession();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState(false);
  const [isAccessCodeSubscription, setIsAccessCodeSubscription] = useState(false);

  useEffect(() => {
    if (session?.user) {
      fetchSubscriptionData();
    }
  }, [session]);

  const fetchSubscriptionData = async () => {
    try {
      const response = await fetch('/api/subscription/sync');
      if (response.ok) {
        const data = await response.json();
        setSubscriptionData(data);
        
        // Check if this is an access code subscription (no Stripe billing)
        if (data.subscription) {
          const isAccessCode = !data.subscription.stripe_subscription_id && 
                              !data.subscription.stripe_customer_id && 
                              !data.subscription.stripe_price_id;
          setIsAccessCodeSubscription(isAccessCode);
        } else {
          setIsAccessCodeSubscription(false);
        }
      }
    } catch (error) {
      console.error('Error fetching subscription data:', error);
    } finally {
      setLoading(false);
    }
  };

  const openBillingPortal = async () => {
    try {
      setActionLoading(true);
      
      const response = await fetch('/api/stripe/billing-portal', {
        method: 'POST',
      });

      const data = await response.json();

      if (data.success) {
        window.location.href = data.portalUrl;
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error opening billing portal:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setActionLoading(false);
    }
  };

  if (!session?.user) {
    return (
      <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-8 text-center">
        <div className="text-6xl mb-4">🔐</div>
        <p className="text-gray-300 mb-4">Please sign in to view your subscription.</p>
        <a
          href="/sign-in"
          className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
          </svg>
          Sign In
        </a>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-8">
        <div className="flex flex-col items-center justify-center py-8">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
          <p className="text-gray-300">Loading subscription details...</p>
        </div>
      </div>
    );
  }

  if (!subscriptionData) {
    return (
      <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-8 text-center">
        <div className="text-6xl mb-4">⚠️</div>
        <p className="text-gray-300 mb-4">Unable to load subscription data.</p>
        <button
          onClick={fetchSubscriptionData}
          className="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          Retry
        </button>
      </div>
    );
  }

  const { currentPlan, subscription } = subscriptionData;

  // Get plan details for display
  const getPlanIcon = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'premium':
        return '👑';
      case 'pro':
        return '⭐';
      case 'basic':
        return '📝';
      default:
        return '🆓';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'premium':
        return 'from-yellow-600/20 to-yellow-800/20 border-yellow-500/30';
      case 'pro':
        return 'from-purple-600/20 to-purple-800/20 border-purple-500/30';
      case 'basic':
        return 'from-blue-600/20 to-blue-800/20 border-blue-500/30';
      default:
        return 'from-gray-600/20 to-gray-800/20 border-gray-500/30';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return '✅';
      case 'canceled':
        return '❌';
      case 'past_due':
        return '⚠️';
      default:
        return '⏸️';
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Plan Card */}
      <div className={`bg-gradient-to-br ${getPlanColor(currentPlan)} border rounded-xl p-6`}>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="text-3xl">{getPlanIcon(currentPlan)}</div>
            <div>
              <h3 className="text-xl font-bold text-white">
                {subscription?.plan?.display_name || `${currentPlan.charAt(0).toUpperCase() + currentPlan.slice(1)} Plan`}
              </h3>
              <p className="text-gray-300 text-sm">Your current subscription</p>
            </div>
          </div>
          {subscription && (
            <div className="text-right">
              <div className="flex items-center gap-2 text-lg font-semibold text-white">
                <span className="text-2xl">{getStatusIcon(subscription.status)}</span>
                <span className="capitalize">{subscription.status}</span>
              </div>
            </div>
          )}
        </div>

        {/* Access Code Subscription Notice */}
        {isAccessCodeSubscription && subscription && (
          <div className="bg-green-900/40 border border-green-600/50 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="text-2xl">🎁</div>
              <div>
                <p className="text-green-200 font-medium mb-1">Complimentary Access</p>
                <p className="text-green-300 text-sm">
                  You're using a special access code - no billing will occur. Your access expires on{' '}
                  <span className="font-medium">
                    {new Date(subscription.current_period_end).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </span>
                </p>
              </div>
            </div>
          </div>
        )}

        {subscription && !isAccessCodeSubscription && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {/* Billing Info */}
            <div className="bg-slate-800/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <svg className="w-5 h-5 text-emerald-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 11-4 0 2 2 0 014 0zM6 7h12l-1 9H7L6 7z" />
                </svg>
                <span className="text-emerald-300 font-medium">Next Billing</span>
              </div>
              <div className="ml-7">
                <p className="text-white font-semibold">
                  {new Date(subscription.current_period_end).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </p>
              </div>
            </div>

            {/* Plan Period */}
            <div className="bg-slate-800/30 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <svg className="w-5 h-5 text-blue-400 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                </svg>
                <span className="text-blue-300 font-medium">Billing Cycle</span>
              </div>
              <div className="ml-7">
                <p className="text-white font-semibold">
                  {subscription.plan?.price_yearly ? 'Yearly' : 'Monthly'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Cancellation Notice */}
        {subscription?.cancel_at_period_end && (
          <div className="bg-yellow-900/40 border border-yellow-600/50 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-3">
              <div className="text-2xl">⚠️</div>
              <div>
                <p className="text-yellow-200 font-medium mb-1">Subscription Ending</p>
                <p className="text-yellow-300 text-sm">
                  Your subscription will be cancelled at the end of the current billing period.
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        {subscription ? (
          <div className="flex flex-col sm:flex-row gap-3">
            {!isAccessCodeSubscription && (
              <button
                onClick={openBillingPortal}
                disabled={actionLoading}
                className="flex-1 inline-flex items-center justify-center gap-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                {actionLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    Loading...
                  </>
                ) : (
                  <>
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    Manage Subscription
                  </>
                )}
              </button>
            )}
            {!isAccessCodeSubscription && (
              <a
                href="/pricing"
                className="inline-flex items-center justify-center gap-2 bg-emerald-600 hover:bg-emerald-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
                Upgrade Plan
              </a>
            )}
          </div>
        ) : (
          <div className="text-center">
            <a
              href="/pricing"
              className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-8 rounded-lg transition-all transform hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              Upgrade to Premium
            </a>
          </div>
        )}

        <div className="mt-4 text-center">
          <p className="text-gray-400 text-sm">
            {isAccessCodeSubscription
              ? "Enjoy your complimentary access - no billing or payment required"
              : subscription 
              ? "Update payment method, view invoices, or manage your subscription"
              : "Unlock premium features and unlimited story creation"
            }
          </p>
        </div>
      </div>

      {/* Free Plan Benefits */}
      {currentPlan === 'free' && (
        <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-6">
          <div className="flex items-center gap-3 mb-4">
            <div className="text-2xl">🎁</div>
            <h3 className="text-lg font-semibold text-white">What You Get with Free</h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">3 stories per month</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">2 image regenerations per story</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">Story customization</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">All themes</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">Share via link, email or Facebook</span>
            </div>
            <div className="flex items-center gap-3">
              <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-4 h-4 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
              <span className="text-gray-300">Community access</span>
            </div>
          </div>
        </div>
      )}

      {/* Access Code Section - Only for free plan users */}
      {currentPlan === 'free' && (
        <AccessCodeInput onCodeRedeemed={fetchSubscriptionData} />
      )}
    </div>
  );
} 