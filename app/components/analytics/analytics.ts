// Analytics utility functions for Google Analytics tracking
declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void
  }
}

export const GA_MEASUREMENT_ID = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

// Track page views
export const pageview = (url: string) => {
  if (typeof window !== 'undefined' && window.gtag && GA_MEASUREMENT_ID) {
    window.gtag('config', GA_MEASUREMENT_ID, {
      page_path: url,
    })
  }
}

// Track custom events
export const event = ({
  action,
  category,
  label,
  value,
}: {
  action: string
  category: string
  label?: string
  value?: number
}) => {
  if (typeof window !== 'undefined' && window.gtag) {
    window.gtag('event', action, {
      event_category: category,
      event_label: label,
      value: value,
    })
  }
}

// Track story creation events
export const trackStoryCreated = (storyType?: string) => {
  event({
    action: 'story_created',
    category: 'engagement',
    label: storyType || 'unknown',
  })
}

// Track story sharing events
export const trackStoryShared = (platform: string) => {
  event({
    action: 'story_shared',
    category: 'engagement',
    label: platform,
  })
}

// Track subscription events
export const trackSubscription = (planType: string) => {
  event({
    action: 'subscription',
    category: 'conversion',
    label: planType,
  })
}

// Track user signup
export const trackSignup = (method?: string) => {
  event({
    action: 'sign_up',
    category: 'conversion',
    label: method || 'email',
  })
}

// Track user login
export const trackLogin = (method?: string) => {
  event({
    action: 'login',
    category: 'engagement',
    label: method || 'email',
  })
}

// Track story downloads
export const trackStoryDownload = (format: string) => {
  event({
    action: 'story_download',
    category: 'engagement',
    label: format,
  })
}