'use client'

import Image from "next/image";
import Link from "next/link";
import { useSession } from 'next-auth/react';

export default function Footer() {
  const { data: session } = useSession();
  const currentYear = new Date().getFullYear();

  return (
    <footer className="border-t border-slate-700 relative z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Mobile Layout */}
        <div className="md:hidden space-y-8">
          {/* Logo and Description */}
          <div>
            <div className="flex items-center mb-4">
              <Image
                src="/images/logo.png"
                alt="MyStoryMaker"
                width={32}
                height={32}
                className="mr-3"
              />
              <span className="text-white font-bold text-xl">MyStoryMaker</span>
            </div>
            <p className="text-gray-400 text-sm">
              Creating magical stories for children through the power of creativity and AI technology.
            </p>
          </div>
          
          {/* Explore and Account - Two Columns */}
          <div className="grid grid-cols-2 gap-8">
            <div>
              <h3 className="text-white font-semibold mb-4">EXPLORE</h3>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/" className="hover:text-white transition-colors text-base py-1 block">Home</Link></li>
                <li><Link href="/about" className="hover:text-white transition-colors text-base py-1 block">About Us</Link></li>
                <li><Link href="/pricing" className="hover:text-white transition-colors text-base py-1 block">Pricing</Link></li>
                <li><Link href="/public/stories" className="hover:text-white transition-colors text-base py-1 block">Discover Stories</Link></li>
              </ul>
            </div>
            
            <div>
              <h3 className="text-white font-semibold mb-4">ACCOUNT</h3>
              <ul className="space-y-2 text-gray-400">
                {!session ? (
                  <>
                    <li>
                      <Link href="/sign-in" className="text-gray-400 hover:text-white transition-colors text-base py-1 block">
                        Sign In
                      </Link>
                    </li>
                    <li>
                      <Link href="/sign-up" className="text-gray-400 hover:text-white transition-colors text-base py-1 block">
                        Sign Up
                      </Link>
                    </li>
                  </>
                ) : (
                  <>
                    <li><Link href="/my-stories" className="hover:text-white transition-colors text-base py-1 block">My Stories</Link></li>
                    <li><Link href="/profile" className="hover:text-white transition-colors text-base py-1 block">Preferences</Link></li>
                    <li><Link href="/account/subscription" className="hover:text-white transition-colors text-base py-1 block">Subscription</Link></li>
                    <li><Link href="/stats" className="hover:text-white transition-colors text-base py-1 block">Stats</Link></li>
                    <li><Link href="/tags" className="hover:text-white transition-colors text-base py-1 block">Tags</Link></li>
                  </>
                )}
              </ul>
            </div>
          </div>
          
          {/* Legal - Spread Across Bottom */}
          <div>
            <h3 className="text-white font-semibold mb-4">LEGAL</h3>
            <div className="flex flex-wrap gap-x-8 gap-y-2">
              <Link href="/terms" className="text-gray-400 hover:text-white transition-colors text-base py-1">Terms of Service</Link>
              <Link href="/privacy" className="text-gray-400 hover:text-white transition-colors text-base py-1">Privacy Policy</Link>
              <Link href="/contact" className="text-gray-400 hover:text-white transition-colors text-base py-1">Contact Us</Link>
            </div>
          </div>
        </div>

        {/* Desktop Layout */}
        <div className="hidden md:grid md:grid-cols-4 gap-8">
          <div>
            <div className="flex items-center mb-4">
              <Image
                src="/images/logo.png"
                alt="MyStoryMaker"
                width={32}
                height={32}
                className="mr-3"
              />
              <span className="text-white font-bold text-xl">MyStoryMaker</span>
            </div>
            <p className="text-gray-400 text-sm">
              Creating magical stories for children through the power of creativity and AI technology.
            </p>
          </div>
          
          <div>
            <h3 className="text-white font-semibold mb-4">EXPLORE</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><Link href="/" className="hover:text-white transition-colors">Home</Link></li>
              <li><Link href="/about" className="hover:text-white transition-colors">About Us</Link></li>
              <li><Link href="/pricing" className="hover:text-white transition-colors">Pricing</Link></li>
              <li><Link href="/public/stories" className="hover:text-white transition-colors">Discover Stories</Link></li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-white font-semibold mb-4">ACCOUNT</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              {!session ? (
                <>
                  <li>
                    <Link href="/sign-in" className="text-gray-400 hover:text-white transition-colors text-sm">
                      Sign In
                    </Link>
                  </li>
                  <li>
                    <Link href="/sign-up" className="text-gray-400 hover:text-white transition-colors text-sm">
                      Sign Up
                    </Link>
                  </li>
                </>
              ) : (
                <>
                  <li><Link href="/my-stories" className="hover:text-white transition-colors">My Stories</Link></li>
                  <li><Link href="/profile" className="hover:text-white transition-colors">Preferences</Link></li>
                  <li><Link href="/account/subscription" className="hover:text-white transition-colors">Subscription</Link></li>
                  <li><Link href="/stats" className="hover:text-white transition-colors">Stats</Link></li>
                  <li><Link href="/tags" className="hover:text-white transition-colors">Tags</Link></li>
                </>
              )}
            </ul>
          </div>
          
          <div>
            <h3 className="text-white font-semibold mb-4">LEGAL</h3>
            <ul className="space-y-2 text-gray-400 text-sm">
              <li><Link href="/terms" className="hover:text-white transition-colors">Terms of Service</Link></li>
              <li><Link href="/privacy" className="hover:text-white transition-colors">Privacy Policy</Link></li>
              <li><Link href="/contact" className="hover:text-white transition-colors">Contact Us</Link></li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-slate-700 mt-8 pt-8 text-center">
          <p className="text-gray-400 text-sm">
            © {currentYear} MyStoryMaker. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
} 