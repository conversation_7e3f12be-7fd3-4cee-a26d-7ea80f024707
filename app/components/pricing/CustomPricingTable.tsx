'use client'

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface PricingPlan {
  id: number;
  name: string;
  display_name: string;
  price_monthly: number;
  price_yearly: number;
  stories_per_month: number;
  image_regenerations_per_story: number;
  features: Record<string, boolean>;
  active: boolean;
}

interface CustomPricingTableProps {
  onPlanSelect?: (planName: string) => void;
}

export default function CustomPricingTable({ onPlanSelect }: CustomPricingTableProps) {
  const { data: session } = useSession();
  const [plans, setPlans] = useState<PricingPlan[]>([]);
  const [currentPlan, setCurrentPlan] = useState<string>('free');
  const [loading, setLoading] = useState(true);
  const [isYearly, setIsYearly] = useState(false);

  useEffect(() => {
    fetchPlans();
    if (session?.user) {
      fetchCurrentPlan();
    }
  }, [session]);

  const fetchPlans = async () => {
    try {
      const response = await fetch('/api/subscription/plans');
      if (response.ok) {
        const data = await response.json();
        setPlans(data);
      }
    } catch (error) {
      console.error('Error fetching plans:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentPlan = async () => {
    try {
      const response = await fetch('/api/subscription/sync');
      if (response.ok) {
        const data = await response.json();
        setCurrentPlan(data.currentPlan);
      }
    } catch (error) {
      console.error('Error fetching current plan:', error);
    }
  };

  const handlePlanSelect = async (planName: string) => {
    if (!session?.user) {
      // Redirect to sign in
      window.location.href = '/sign-in?redirect_url=/pricing';
      return;
    }

    if (onPlanSelect) {
      onPlanSelect(planName);
    }

    // Handle free plan selection
    if (planName === 'free') {
      alert('Free plan selected. Downgrade functionality coming soon!');
      return;
    }

    try {
      setLoading(true);
      
      // Create Stripe checkout session
      const response = await fetch('/api/stripe/create-checkout', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          planName,
          billingPeriod: isYearly ? 'yearly' : 'monthly',
        }),
      });

      const data = await response.json();

      if (data.success) {
        // Redirect to Stripe Checkout
        window.location.href = data.checkoutUrl;
      } else {
        alert(`Error: ${data.error}`);
      }
    } catch (error) {
      console.error('Error creating checkout session:', error);
      alert('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getFeatureList = (plan: PricingPlan) => {
    if (plan.name === 'free') {
      return [
      `${plan.stories_per_month} stories per month`,
      `${plan.image_regenerations_per_story} image regenerations per story`,
        'Story customization',
        'All themes',
        'Share via link, email or Facebook'
      ];
    }

    const features = ['All free features'];
    
    features.push(`${plan.stories_per_month} stories per month`);
    features.push(`${plan.image_regenerations_per_story} image regenerations per story`);
    
    if (plan.features.pdf_download) {
      features.push('Download as PDF');
    }
    if (plan.features.audio_generation) {
      features.push('Listen to story as audio');
    }
    if (plan.features.email_support) {
      features.push('Email Support');
    }
    if (plan.features.premium_voices) {
      features.push('Choose from 20 voices for audio');
      features.push('Highlighted words as story is read');
    }

    return features;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4">
      {/* Billing Toggle */}
      <div className="flex justify-center mb-12">
        <div className="bg-slate-800/80 border border-slate-700 rounded-xl p-1 flex items-center">
          <button
            onClick={() => setIsYearly(false)}
            className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 ${
              !isYearly
                ? 'bg-blue-600 text-white shadow-lg'
                : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
            }`}
          >
            Monthly
          </button>
          <button
            onClick={() => setIsYearly(true)}
            className={`px-6 py-3 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2 ${
              isYearly
                ? 'bg-blue-600 text-white shadow-lg'
                : 'text-gray-300 hover:text-white hover:bg-slate-700/50'
            }`}
          >
            Yearly
            <span className="text-xs bg-green-500 text-white px-2 py-1 rounded-md font-semibold">
              2 months free
            </span>
          </button>
        </div>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-3 gap-6 lg:gap-8">
        {plans.map((plan) => {
          const isCurrentPlan = currentPlan === plan.name;
          const price = isYearly ? plan.price_yearly : plan.price_monthly;
          const period = isYearly ? 'year' : 'month';
          const isPopular = plan.name === 'starter';
          
          return (
            <div
              key={plan.id}
              className={`relative bg-slate-800/50 rounded-xl border-2 transition-all hover:border-slate-600 flex flex-col h-full ${
                isPopular
                  ? 'border-blue-500 shadow-lg shadow-blue-500/20'
                  : 'border-slate-700'
              }`}
            >
              {/* Badge Container - Fixed positioning */}
              <div className="absolute -top-3 left-0 right-0 flex justify-center">
                {isCurrentPlan && isPopular ? (
                  <div className="flex gap-2">
                    <span className="bg-blue-600 text-white px-3 py-1 rounded-lg text-sm font-medium">
                      Most Popular
                    </span>
                    <span className="bg-slate-600 text-white px-3 py-1 rounded-lg text-sm font-medium">
                      Current Plan
                    </span>
                  </div>
                ) : isPopular ? (
                  <span className="bg-blue-600 text-white px-4 py-1 rounded-lg text-sm font-medium">
                    Most Popular
                  </span>
                ) : isCurrentPlan ? (
                  <span className="bg-slate-600 text-white px-4 py-1 rounded-lg text-sm font-medium">
                    Current Plan
                  </span>
                ) : null}
                </div>

              {/* Card Content */}
              <div className="p-6 lg:p-8 flex flex-col h-full">
                {/* Header */}
              <div className="text-center mb-6">
                  <h3 className="text-2xl font-bold text-white mb-3">
                  {plan.display_name}
                </h3>
                <div className="text-4xl font-bold text-white mb-1">
                  ${price}
                    <span className="text-lg text-gray-400 font-normal">/{period}</span>
                </div>
              </div>

                {/* Features - Flex grow to push button to bottom */}
                <div className="flex-grow">
                  <ul className="space-y-3">
                {getFeatureList(plan).map((feature, index) => (
                      <li key={index} className="flex items-start text-gray-300">
                    <svg
                          className="w-5 h-5 text-green-400 mr-3 flex-shrink-0 mt-0.5"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                        <span className="text-sm leading-relaxed">{feature}</span>
                  </li>
                ))}
              </ul>
                </div>

                {/* Button - Always at bottom */}
                <div className="mt-8">
              <button
                onClick={() => handlePlanSelect(plan.name)}
                disabled={isCurrentPlan}
                    className={`w-full py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                  isCurrentPlan
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                        : isPopular
                        ? 'bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl hover:scale-105'
                        : plan.name === 'free'
                        ? 'bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600 text-white shadow-lg hover:shadow-xl hover:scale-105'
                        : 'bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white shadow-lg hover:shadow-xl hover:scale-105'
                }`}
              >
                {isCurrentPlan
                  ? 'Current Plan'
                  : plan.name === 'free'
                  ? 'Get Started'
                  : 'Upgrade Now'}
              </button>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
} 