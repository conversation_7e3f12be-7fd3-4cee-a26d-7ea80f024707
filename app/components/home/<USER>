import TestimonialCard from './TestimonialCard';
import { testimonials } from '../../data/testimonials';

export default function TestimonialsSection() {
  return (
    <section className="pt-7 pb-20 relative z-10" aria-labelledby="testimonials-heading">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 id="testimonials-heading" className="text-4xl font-bold text-white mb-4">What Parents Are Saying</h2>
          <p className="text-gray-300 text-lg">Join thousands of happy families creating personalized stories</p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8" role="list">
          {testimonials.map((testimonial, index) => (
            <div key={index} role="listitem">
              <TestimonialCard
                name={testimonial.name}
                role={testimonial.role}
                image={testimonial.image}
                rating={testimonial.rating}
                testimonial={testimonial.testimonial}
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
} 