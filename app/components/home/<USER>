'use client';

import Image from "next/image";
import Link from "next/link";
import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';

interface Star {
  id: string;
  left: number;
  top: number;
  animationDelay: number;
  animationDuration: number;
}

export default function HeroSection() {
  const { data: session } = useSession();
  const [stars, setStars] = useState<{
    small: Star[];
    medium: Star[];
    large: Star[];
    xlarge: Star[];
  }>({
    small: [],
    medium: [],
    large: [],
    xlarge: []
  });
  const [imageLoaded, setImageLoaded] = useState(false);
  const [isSafariIOS, setIsSafariIOS] = useState(false);

  useEffect(() => {
    // Detect Safari iOS
    const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
    const isSafari = /Safari/.test(navigator.userAgent) && !/Chrome/.test(navigator.userAgent);
    setIsSafariIOS(isIOS && isSafari);

    // Generate stars only on client side to avoid hydration mismatch
    const generateStars = (count: number, prefix: string, minDuration: number, maxDuration: number, maxDelay: number): Star[] => {
      return Array.from({ length: count }, (_, i) => ({
        id: `${prefix}-${i}`,
        left: Math.random() * 100,
        top: Math.random() * 100,
        animationDelay: Math.random() * maxDelay,
        animationDuration: minDuration + Math.random() * (maxDuration - minDuration),
      }));
    };

    setStars({
      small: generateStars(20, 'small', 3, 6, 4),
      medium: generateStars(15, 'medium', 4, 7, 5),
      large: generateStars(8, 'large', 4, 7, 6),
      xlarge: generateStars(3, 'xlarge', 5, 9, 8),
    });
  }, []);

  return (
    <section 
      className="hero-section relative h-96 sm:h-[420px] md:h-[385px] lg:h-[462px] flex items-center justify-center overflow-hidden bg-slate-900"
      style={{
        // Force height constraints on Safari iOS to prevent full-screen image
        ...(isSafariIOS && {
          maxHeight: '462px',
          minHeight: '384px',
          height: 'clamp(384px, 50vh, 462px)'
        })
      }}
    >
      <div className="absolute inset-0">
        {/* Loading placeholder for Safari iOS */}
        {isSafariIOS && !imageLoaded && (
          <div className="absolute inset-0 bg-slate-900 flex items-center justify-center">
            <div className="text-white opacity-50">Loading...</div>
          </div>
        )}
        
        <Image
          src="/images/hero.webp"
          alt="Magical story background"
          fill
          className={`object-cover transition-opacity duration-300 ${
            isSafariIOS && !imageLoaded ? 'opacity-0' : 'opacity-100'
          }`}
          priority
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 100vw, (max-width: 1024px) 100vw, 100vw"
          quality={85}
          placeholder="blur"
          blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAAIAAoDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R//2Q=="
          fetchPriority="high"
          onLoad={() => setImageLoaded(true)}
          style={{
            // Additional Safari iOS constraints
            ...(isSafariIOS && {
              maxHeight: '462px',
              objectFit: 'cover',
              objectPosition: 'center'
            })
          }}
        />
        {/* Semi-transparent overlay */}
        <div className="absolute inset-0 bg-black/40"></div>
        
        {/* Gradient overlay for smooth transition */}
        <div className="absolute inset-x-0 bottom-0 h-32 bg-gradient-to-b from-transparent to-slate-900"></div>
        
        {/* Sparkling Stars Animation */}
        <div className="absolute inset-0 pointer-events-none">
          {/* Small stars */}
          {stars.small.map((star) => (
            <div
              key={star.id}
              className="absolute animate-pulse"
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                animationDelay: `${star.animationDelay}s`,
                animationDuration: `${star.animationDuration}s`,
              }}
            >
              <div className="relative">
                <div className="w-0.5 h-0.5 bg-white rounded-full opacity-70"></div>
                <div className="absolute top-0 left-0 w-0.5 h-0.5 bg-blue-200 rounded-full animate-ping opacity-50"></div>
              </div>
            </div>
          ))}
          
          {/* Medium stars */}
          {stars.medium.map((star) => (
            <div
              key={star.id}
              className="absolute animate-pulse"
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                animationDelay: `${star.animationDelay}s`,
                animationDuration: `${star.animationDuration}s`,
              }}
            >
              <div className="relative">
                <div className="w-1 h-1 bg-white rounded-full opacity-80"></div>
                <div className="absolute top-0 left-0 w-1 h-1 bg-blue-200 rounded-full animate-ping opacity-60"></div>
              </div>
            </div>
          ))}
          
          {/* Large emoji sparkles */}
          {stars.large.map((star) => (
            <div
              key={star.id}
              className="absolute"
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                animation: `sparkle ${star.animationDuration}s ease-in-out infinite`,
                animationDelay: `${star.animationDelay}s`,
              }}
            >
              <div className="text-white opacity-70 text-sm">✨</div>
            </div>
          ))}
          
          {/* Extra large sparkles */}
          {stars.xlarge.map((star) => (
            <div
              key={star.id}
              className="absolute"
              style={{
                left: `${star.left}%`,
                top: `${star.top}%`,
                animation: `sparkle ${star.animationDuration}s ease-in-out infinite`,
                animationDelay: `${star.animationDelay}s`,
              }}
            >
              <div className="text-white opacity-60 text-base">✨</div>
            </div>
          ))}
        </div>
        
        <style jsx>{`
          @keyframes sparkle {
            0%, 100% { 
              opacity: 0; 
              transform: scale(0.5); 
            }
            50% { 
              opacity: 0.8; 
              transform: scale(1); 
            }
          }
        `}</style>
      </div>
      
      <div className="relative z-10 text-center max-w-7xl mx-auto px-4 sm:px-6">
        <div className="mb-4 sm:mb-6">
          <span className="inline-flex items-center text-blue-300 text-xs sm:text-sm font-medium bg-blue-900/50 border border-blue-400/30 px-3 py-1.5 rounded-full">
            ✨ Bring imagination to life
          </span>
        </div>
        <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-6xl font-bold text-white mb-4 sm:mb-6 leading-tight">
          Create magical stories for your children
        </h1>
        <p className="text-base sm:text-lg md:text-xl text-gray-200 mb-6 sm:mb-8 max-w-2xl mx-auto px-2">
          Transform simple ideas into beautifully crafted stories that captivate young minds and inspire creativity
        </p>
        <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center px-4">
          {!session ? (
            <Link href="/sign-up">
              <button 
                className="border-2 border-blue-500 bg-blue-600 hover:bg-blue-700 text-white px-8 sm:px-10 py-4 sm:py-5 rounded-lg text-base sm:text-lg font-semibold transition-colors w-full sm:w-auto min-h-[48px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                aria-label="Get started with MyStoryMaker - Sign up for free"
              >
                Get Started
              </button>
            </Link>
          ) : (
            <Link href="/my-stories">
              <button 
                className="border-2 border-blue-500 bg-blue-600 hover:bg-blue-700 text-white px-8 sm:px-10 py-4 sm:py-5 rounded-lg text-base sm:text-lg font-semibold transition-colors w-full sm:w-auto min-h-[48px] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                aria-label="Go to your stories dashboard"
              >
                Go to My Stories
              </button>
            </Link>
          )}
          <Link href="/public/stories">
            <button 
              className="border-2 border-emerald-400 text-white px-8 sm:px-10 py-4 sm:py-5 rounded-lg text-base sm:text-lg font-semibold bg-emerald-800 hover:bg-emerald-900 transition-colors w-full sm:w-auto min-h-[48px] focus:outline-none focus:ring-2 focus:ring-emerald-400 focus:ring-offset-2 focus:ring-offset-slate-900"
              aria-label="Discover stories shared by the community"
            >
              Discover Stories
            </button>
          </Link>
        </div>
      </div>
    </section>
  );
} 