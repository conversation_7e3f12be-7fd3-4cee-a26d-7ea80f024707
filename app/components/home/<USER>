import Image from "next/image";
import VoteButtons from "../VoteButtons";
import { getThemeColor } from "../../../lib/utils/themeColors";

interface VoteData {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
}

interface PublicStoryCardProps {
  id: number;
  story_uuid: string;
  title: string;
  excerpt: string;
  ageRange: string;
  themes: string[];
  hasImage: boolean;
  hasAudio: boolean;
  main_character: string;
  setting: string;
  voteData?: VoteData;
}

export default function PublicStoryCard({ 
  id, 
  story_uuid,
  title, 
  excerpt, 
  ageRange, 
  themes, 
  hasImage, 
  hasAudio,
  voteData
}: PublicStoryCardProps) {
  // Create URL-friendly slug from title
  const createSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Only navigate if the click target is not a button or inside a button
    const target = e.target as HTMLElement;
    if (!target.closest('button')) {
      window.location.href = `/stories/${story_uuid}/${createSlug(title)}`;
    }
  };

  return (
    <div 
      onClick={handleCardClick}
      className="relative group bg-gradient-to-br from-slate-800 to-slate-900 rounded-lg overflow-hidden hover:from-slate-700 hover:to-slate-800 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] transform-gpu cursor-pointer border border-slate-600 hover:border-blue-500/50 h-full flex flex-col"
    >
      {/* Story Image */}
      <div className="relative h-48 bg-gradient-to-br from-slate-700 to-slate-800">
        {hasImage ? (
          <Image
            src={`/api/public/images/${id}`}
            alt={`Illustration for ${title}`}
            fill
            className="object-cover group-hover:scale-105 transition-all duration-300"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          </div>
        )}
        
        {/* Theme Bubbles - Top Right */}
        {themes.length > 0 && (
          <div className="absolute top-3 right-3 flex gap-1">
            {themes.slice(0, 2).map((theme, index) => (
              <span 
                key={index}
                className={`px-3 py-1 ${getThemeColor(theme)} text-white text-xs rounded-lg font-medium shadow-sm`}
              >
                {theme}
              </span>
            ))}
            {themes.length > 2 && (
              <span className="px-3 py-1 bg-gray-700/80 text-white text-xs rounded-lg font-medium shadow-sm">
                +{themes.length - 2}
              </span>
            )}
          </div>
        )}
        
        {/* Audio indicator - Top Left */}
        {hasAudio && (
          <div className="absolute top-3 left-3">
            <span className="px-3 py-1 bg-blue-700/80 text-white text-xs rounded-lg font-medium shadow-sm flex items-center gap-1">
              <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
              </svg>
              Audio
            </span>
          </div>
        )}
        
        {/* Public badge - Bottom Right */}
        <div className="absolute bottom-3 right-3">
          <span className="px-3 py-1 text-xs font-medium rounded-lg shadow-sm bg-green-700/80 text-white flex items-center gap-1">
            <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Public
          </span>
        </div>
      </div>
      
      {/* Story Info */}
      <div className="p-6 flex-1 flex flex-col">
        <h3 className="text-xl font-bold text-white mb-3 overflow-hidden text-ellipsis" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical'
        }}>
          {title}
        </h3>

        {/* Excerpt */}
        <p className="text-gray-300 text-sm mb-4 overflow-hidden text-ellipsis" style={{
          display: '-webkit-box',
          WebkitLineClamp: 2,
          WebkitBoxOrient: 'vertical'
        }}>
          {excerpt}
        </p>
        
        {/* Age Range */}
        <div className="mb-4">
          <span className="px-3 py-1 bg-gradient-to-r from-slate-700 to-slate-600 text-gray-300 text-xs rounded-lg border border-slate-600/50">
            Age: {ageRange}
          </span>
        </div>
        
        {/* Bottom Section with Voting and Read Button */}
        <div className="flex items-center justify-between mt-auto">
          {/* Vote Buttons - Bottom Left */}
          <div className="flex items-center">
            <VoteButtons 
              storyUuid={story_uuid} 
              size="sm" 
              showCounts={true}
              className="flex items-center gap-2"
              initialVoteData={voteData ? { counts: voteData.counts, userVote: null } : undefined}
            />
          </div>

          {/* Read Button - Bottom Right */}
          <div className="flex gap-2">
            <button
              onClick={() => {
                window.location.href = `/stories/${story_uuid}/${createSlug(title)}`;
              }}
              className="flex items-center justify-center px-4 py-1.5 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-lg transition-all duration-200 font-medium text-sm border border-blue-600/50 hover:border-blue-500 transform hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
              aria-label={`Read story: ${title}`}
            >
              Read Story
            </button>
          </div>
        </div>
      </div>
    </div>
  );
} 