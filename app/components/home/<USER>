'use client';

import Image from "next/image";
import Link from "next/link";
import { useSession } from 'next-auth/react';

export default function CTASection() {
  const { data: session } = useSession();

  return (
    <section className="py-12 sm:py-16 relative overflow-hidden">
      <div className="absolute inset-0">
        <Image
          src="/images/start_creating.webp"
          alt="Children and families engaged in creative storytelling activities"
          fill
          className="object-cover opacity-30"
          sizes="100vw"
        />
      </div>
      <div className="relative z-10 max-w-7xl mx-auto text-center px-4 sm:px-6">
        <div className="mb-4 sm:mb-6">
          <span className="inline-flex items-center text-blue-300 text-xs sm:text-sm font-medium bg-blue-900/50 border border-blue-400/30 px-3 py-1.5 rounded-full">
            ✨ Ready to begin your journey?
          </span>
        </div>
        <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 sm:mb-6 leading-tight">
          Start Creating Magical Stories Today
        </h2>
        <p className="text-base sm:text-lg md:text-xl text-gray-200 mb-6 sm:mb-8 max-w-2xl mx-auto px-2">
          Join thousands of families creating personalized stories that will be treasured for years to come
        </p>
        <div className="mb-4 sm:mb-6">
          {!session ? (
            <Link href="/sign-up">
              <button 
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-colors w-full sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                aria-label="Get started creating magical stories - Sign up for free"
              >
                Get Started
              </button>
            </Link>
          ) : (
            <Link href="/my-stories">
              <button 
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 sm:px-8 py-3 sm:py-4 rounded-lg text-base sm:text-lg font-semibold transition-colors w-full sm:w-auto focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-900"
                aria-label="Go to your stories to continue creating"
              >
                Go to My Stories
              </button>
            </Link>
          )}
        </div>
        <p className="text-green-300 text-xs sm:text-sm font-medium bg-green-900/20 border border-green-400/20 px-3 py-1 rounded-full inline-block">
          ✓ No credit card required for free plan
        </p>
      </div>
    </section>
  );
} 