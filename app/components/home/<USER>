import Image from "next/image";

interface TestimonialCardProps {
  name: string;
  role: string;
  image: string;
  rating: number;
  testimonial: string;
}

export default function TestimonialCard({ name, role, image, rating, testimonial }: TestimonialCardProps) {
  return (
    <article className="bg-slate-800/80 p-6 rounded-xl border border-slate-700/50">
      <div className="flex items-center mb-4">
        <Image
          src={image}
          alt={`Profile photo of ${name}`}
          width={60}
          height={60}
          className="rounded-full mr-4"
        />
        <div>
          <h3 className="text-white font-semibold text-lg">{name}</h3>
          <p className="text-gray-400 text-sm">{role}</p>
        </div>
      </div>
      
      <div className="flex mb-4" role="img" aria-label={`${rating} out of 5 stars`}>
        {[...Array(rating)].map((_, i) => (
          <svg key={i} className="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
      
      <blockquote className="text-gray-300 leading-relaxed">
        "{testimonial}"
      </blockquote>
    </article>
  );
}