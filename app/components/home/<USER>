import Image from "next/image";

interface Step {
  image: string;
  alt: string;
  title: string;
  description: string;
}

const steps: Step[] = [
  {
    image: "/images/share_your_idea.webp",
    alt: "Person writing story ideas on paper with creative elements around them",
    title: "Share Your Idea",
    description: "Input your story idea including theme, characters, and setting in our simple form"
  },
  {
    image: "/images/ai_magic.webp",
    alt: "Magical sparkles and AI technology transforming text into a story",
    title: "AI Magic",
    description: "Our AI technology transforms your idea into a complete, engaging story in seconds"
  },
  {
    image: "/images/read_listen.webp",
    alt: "Family reading together with audio controls and digital devices nearby",
    title: "Read & Listen",
    description: "Read, save, edit, or listen to your story anytime, on any device"
  }
];

export default function HowItWorksSection() {
  return (
    <section className="py-20 relative z-10" aria-labelledby="how-it-works-heading">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 id="how-it-works-heading" className="text-4xl font-bold text-white mb-4">How It Works</h2>
          <p className="text-gray-300 text-lg">Creating personalized stories for your children has never been easier</p>
        </div>
        
        <div className="grid md:grid-cols-3 gap-8" role="list">
          {steps.map((step, index) => (
            <div key={index} className="relative group" role="listitem">
              <div className="relative h-64 rounded-2xl overflow-hidden bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50">
                <Image
                  src={step.image}
                  alt={step.alt}
                  fill
                  className="object-cover opacity-60"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent" />
                <div className="absolute inset-0 bg-black/30" />
                <div className="absolute inset-0 flex flex-col justify-center items-center text-center p-8">
                  <h3 className="text-2xl font-bold text-white mb-4 drop-shadow-lg">{step.title}</h3>
                  <p className="text-gray-200 text-base leading-relaxed drop-shadow-md">{step.description}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}