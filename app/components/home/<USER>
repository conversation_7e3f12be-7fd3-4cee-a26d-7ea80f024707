'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import PublicStoryCard from './PublicStoryCard';

interface VoteData {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
}

interface PublicStory {
  id: number;
  story_uuid: string;
  title: string;
  main_character: string;
  setting: string;
  created_at: string;
  themes: string[];
  ageRange: string;
  hasImage: boolean;
  hasAudio: boolean;
  slug: string;
  excerpt: string;
  voteData?: VoteData;
}

export default function FeaturedStoriesSection() {
  const [stories, setStories] = useState<PublicStory[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function fetchFeaturedStories() {
      try {
        const response = await fetch('/api/public/stories/featured');
        
        if (response.ok) {
          const data = await response.json();
          setStories(data);
        }
      } catch (error) {
        console.error('Error fetching featured stories:', error);
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedStories();
  }, []);

  return (
    <section className="pt-0 pb-20 relative z-10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Featured Stories</h2>
          <p className="text-gray-300 text-lg">Discover magical stories from our community</p>
        </div>

        {loading ? (
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
            <p className="text-gray-300">Loading featured stories...</p>
          </div>
        ) : stories.length === 0 ? (
          <div className="text-center py-16">
            <div className="mb-6">
              <span className="text-6xl">📚</span>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">No featured stories yet!</h3>
            <p className="text-gray-300 mb-8">Be the first to share a story with the community</p>
            <Link href="/create">
              <button className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                Create Your First Story
              </button>
            </Link>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {stories.map((story) => (
              <PublicStoryCard
                key={story.id}
                id={story.id}
                story_uuid={story.story_uuid}
                title={story.title}
                excerpt={story.excerpt}
                ageRange={story.ageRange}
                themes={story.themes}
                hasImage={story.hasImage}
                hasAudio={story.hasAudio}
                main_character={story.main_character}
                setting={story.setting}
                voteData={story.voteData}
              />
            ))}
          </div>
        )}

        {stories.length > 0 && (
          <div className="text-center mt-12">
            <Link href="/public/stories">
              <button className="bg-slate-700 hover:bg-slate-600 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-colors">
                Discover All Stories
              </button>
            </Link>
          </div>
        )}
      </div>
    </section>
  );
}