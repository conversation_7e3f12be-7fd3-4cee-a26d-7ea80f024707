'use client';

import { useState, useRef, useEffect } from 'react';

interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
}

interface TranscriptionResult {
  text: string;
  words: WordTimestamp[];
  id: string;
}

interface PublicAudioPlayerWithTranscriptionProps {
  storyUuid: string;
  storyText: string;
  hasExistingAudio?: boolean;
}

export default function PublicAudioPlayerWithTranscription({ 
  storyUuid, 
  storyText,
  hasExistingAudio = false 
}: PublicAudioPlayerWithTranscriptionProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [hasAudio] = useState(hasExistingAudio);
  const [error, setError] = useState<string | null>(null);
  const [transcription, setTranscription] = useState<TranscriptionResult | null>(null);
  const [currentWordIndex, setCurrentWordIndex] = useState<number>(-1);
  const [currentTime, setCurrentTime] = useState<number>(0);
  
  const audioRef = useRef<HTMLAudioElement>(null);

  // Load existing transcription on component mount
  useEffect(() => {
    const loadTranscriptionData = async () => {
      try {
        const response = await fetch(`/api/public/transcriptions/${storyUuid}`);
        if (response.ok) {
          const data = await response.json();
          setTranscription(data.transcription);
        }
      } catch (error) {
        console.error('Error loading transcription:', error);
      }
    };

    if (hasAudio) {
      loadTranscriptionData();
      setAudioUrl(`/api/public/audio/${storyUuid}`);
    }
  }, [hasAudio, storyUuid]);

  // Update current word highlighting based on audio time
  useEffect(() => {
    if (transcription && isPlaying) {
      // Convert currentTime from seconds to milliseconds to match AssemblyAI timestamps
      const currentTimeMs = currentTime * 1000;
      const currentWord = transcription.words.findIndex(word => 
        currentTimeMs >= word.start && currentTimeMs <= word.end
      );
      setCurrentWordIndex(currentWord);
    }
  }, [currentTime, transcription, isPlaying]);



  const togglePlayback = async () => {
    if (!audioRef.current || !audioUrl) {
      return;
    }

    if (isPlaying) {
      audioRef.current.pause();
      setIsPlaying(false);
    } else {
      try {
        if (audioRef.current.error || isNaN(audioRef.current.duration)) {
          audioRef.current.load();
          
          await new Promise((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error('Audio load timeout')), 10000);
            
            const onCanPlay = () => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              resolve(undefined);
            };
            
            const onError = (e: Event) => {
              clearTimeout(timeout);
              audioRef.current?.removeEventListener('canplay', onCanPlay);
              audioRef.current?.removeEventListener('error', onError);
              reject(e);
            };
            
            audioRef.current?.addEventListener('canplay', onCanPlay);
            audioRef.current?.addEventListener('error', onError);
          });
        }
        
        await audioRef.current.play();
        setIsPlaying(true);
        setError(null);
      } catch (err) {
        setError(`Failed to play audio: ${err instanceof Error ? err.message : 'Unknown error'}`);
        setIsPlaying(false);
      }
    }
  };

  const handleTimeUpdate = () => {
    if (audioRef.current) {
      setCurrentTime(audioRef.current.currentTime);
    }
  };

  const handleAudioEnded = () => {
    setIsPlaying(false);
    setCurrentWordIndex(-1);
  };

  const handleAudioError = () => {
    setError(`Failed to load audio file: ${audioRef.current?.error?.message || 'Unknown error'}`);
    setIsPlaying(false);
  };

  const handleAudioLoaded = () => {
    setError(null);
  };

  const seekToWord = (wordIndex: number) => {
    if (audioRef.current && transcription && transcription.words[wordIndex]) {
      // Convert from milliseconds to seconds for HTML5 audio
      audioRef.current.currentTime = transcription.words[wordIndex].start / 1000;
    }
  };

  const renderTranscriptionText = () => {
    if (!transcription) {
      return <p className="text-slate-300 leading-relaxed">{storyText}</p>;
    }

    return (
      <div className="text-slate-300 leading-relaxed">
        {transcription.words.map((word, index) => (
          <span
            key={index}
            className={`cursor-pointer transition-colors duration-200 ${
              index === currentWordIndex 
                ? 'bg-blue-500 text-white px-1 rounded' 
                : 'hover:bg-slate-700 px-1 rounded'
            }`}
            onClick={() => seekToWord(index)}
          >
            {word.word}
            {index < transcription.words.length - 1 ? ' ' : ''}
          </span>
        ))}
      </div>
    );
  };

  if (!hasAudio) {
    return (
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-white font-semibold mb-3">Story Text</h3>
        <p className="text-slate-300 leading-relaxed">{storyText}</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col gap-4">
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleAudioEnded}
          onError={handleAudioError}
          onLoadedData={handleAudioLoaded}
          preload="metadata"
          controls={false}
        />
      )}
      
      {error && (
        <div className="text-red-400 text-sm mb-2">
          {error}
        </div>
      )}

      {/* Audio Controls */}
      <div className="flex flex-col sm:flex-row gap-2">
        <button
          onClick={togglePlayback}
          className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors flex items-center gap-2"
        >
          {isPlaying ? (
            <>
              <div className="w-4 h-4 bg-white rounded-sm"></div>
              Pause
            </>
          ) : (
            <>
              <div className="w-0 h-0 border-l-4 border-l-white border-y-2 border-y-transparent"></div>
              Play Story
            </>
          )}
        </button>
      </div>

      {/* Transcription Text with Word Highlighting */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-700">
        <h3 className="text-white font-semibold mb-3 flex items-center gap-2">
          Story Text
          {transcription && (
            <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
              Interactive
            </span>
          )}
        </h3>
        {renderTranscriptionText()}
        {transcription && (
          <p className="text-xs text-slate-500 mt-3">
            Click on any word to jump to that part of the audio
          </p>
        )}
      </div>
    </div>
  );
} 