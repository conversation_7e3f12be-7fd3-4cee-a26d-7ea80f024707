'use client';

import { useEffect } from 'react';
import { MyStoriesClientProps, Story } from '../../types/story';
import { useStoryManagement } from '../../hooks/story/useStoryManagement';
import { useStoryFiltering } from '../../hooks/story/useStoryFiltering';
import { usePagination } from '../../hooks/usePagination';
import DeleteConfirmDialog from '../DeleteConfirmDialog';
import MyStoriesHeader from './MyStoriesHeader';
import StoryCard from './StoryCard';
import EmptyStoriesState from './EmptyStoriesState';
import Pagination from '../ui/Pagination';
import { FadeIn, StaggerContainer, StaggerItem } from '../ui/PageTransition';

export default function MyStoriesClient({ initialStories }: MyStoriesClientProps) {
  const {
    stories,
    deleteDialog,
    isLoading,
    handleDeleteClick,
    handleDeleteConfirm,
    handleDeleteCancel,
  } = useStoryManagement(initialStories);

  const {
    searchQuery,
    setSearchQuery,
    selectedTheme,
    setSelectedTheme,
    selectedTags,
    setSelectedTags,
    filteredStories
  } = useStoryFiltering(stories);

  const {
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToPage,
    getPaginatedItems
  } = usePagination<Story>({
    totalItems: filteredStories.length,
    itemsPerPage: 9
  });

  const paginatedStories = getPaginatedItems(filteredStories);

  // Scroll to stories section when page changes
  useEffect(() => {
    if (currentPage > 1) {
      // Find the anchor above the stories grid and scroll to it
      const anchor = document.getElementById('stories-anchor');
      if (anchor) {
        anchor.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }
    }
  }, [currentPage]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8 sm:py-12 animate-[fadeIn_0.5s_ease-out] relative z-10">
      {/* Header with search and filters */}
      <FadeIn>
        <MyStoriesHeader
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          selectedTheme={selectedTheme}
          onThemeChange={setSelectedTheme}
          selectedTags={selectedTags}
          onTagsChange={setSelectedTags}
          totalStories={stories.length}
          filteredCount={filteredStories.length}
          isLoading={isLoading && stories.length > 0}
        />
      </FadeIn>

      {/* Invisible anchor for pagination scroll */}
      <div id="stories-anchor"></div>

      {/* Stories Grid */}
      {isLoading && stories.length === 0 ? (
        <FadeIn delay={0.3}>
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <div className="text-gray-400 text-lg">Loading your stories...</div>
          </div>
        </FadeIn>
      ) : stories.length === 0 ? (
        <FadeIn delay={0.3}>
          <EmptyStoriesState />
        </FadeIn>
      ) : filteredStories.length === 0 ? (
        <FadeIn delay={0.3}>
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg mb-4">No stories match your search criteria</div>
            <p className="text-gray-500">Try adjusting your search terms or filters</p>
          </div>
        </FadeIn>
      ) : (
        <>
          <StaggerContainer 
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 mb-8"
            delay={0.1}
            data-stories-grid
          >
            {paginatedStories.map((story) => (
              <StaggerItem key={story.id}>
                <StoryCard
                  story={story}
                  onDelete={handleDeleteClick}
                />
              </StaggerItem>
            ))}
          </StaggerContainer>

          {/* Pagination */}
          <FadeIn delay={0.4}>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={goToPage}
              hasNextPage={hasNextPage}
              hasPreviousPage={hasPreviousPage}
              className="mt-8"
            />
          </FadeIn>
        </>
      )}

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={deleteDialog.isOpen}
        storyTitle={deleteDialog.storyTitle}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        isDeleting={deleteDialog.isDeleting}
      />
    </div>
  );
}