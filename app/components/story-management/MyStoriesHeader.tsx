'use client';

import Link from 'next/link';
import { useState, useEffect } from 'react';
import { useSubscription } from '../../hooks/useSubscription';
import { InlineLoadingSpinner } from '../ui/LoadingSpinner';

interface Theme {
  id: string;
  name: string;
  description: string;
}

interface Tag {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

interface MyStoriesHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedTheme: string;
  onThemeChange: (theme: string) => void;
  selectedTags: string[];
  onTagsChange: (tags: string[]) => void;
  totalStories: number;
  filteredCount: number;
  isLoading: boolean;
}

export default function MyStoriesHeader({
  searchQuery,
  onSearchChange,
  selectedTheme,
  onThemeChange,
  selectedTags,
  onTagsChange,
  totalStories,
  filteredCount,
  isLoading: isRefetching = false
}: MyStoriesHeaderProps) {
  const { subscriptionData, isLoading } = useSubscription();
  const [themes, setThemes] = useState<Theme[]>([]);
  const [tags, setTags] = useState<Tag[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [isClient, setIsClient] = useState(false);

  // Set client flag after hydration
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Load themes and tags
  useEffect(() => {
    const loadData = async () => {
      try {
        const [themesResponse, tagsResponse] = await Promise.all([
          fetch('/api/themes'),
          fetch('/api/tags')
        ]);

        if (themesResponse.ok) {
          const themesData = await themesResponse.json();
          setThemes(themesData);
        }

        if (tagsResponse.ok) {
          const tagsData = await tagsResponse.json();
          setTags(tagsData);
        }
      } catch (error) {
        console.error('Error loading themes and tags:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    loadData();
  }, []);

  const handleTagToggle = (tagName: string) => {
    if (selectedTags.includes(tagName)) {
      onTagsChange(selectedTags.filter(tag => tag !== tagName));
    } else {
      onTagsChange([...selectedTags, tagName]);
    }
  };

  // Prevent hydration mismatch by showing loading state until client is ready
  if (!isClient || isLoading || !subscriptionData) {
    return <InlineLoadingSpinner />;
  }

  const remainingStories = subscriptionData.remainingStories;

  return (
    <div className="mb-8">
      {/* Header with title and create button */}
      <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4 sm:gap-6 mb-6">
        <div className="flex-1">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">My Stories</h1>
            
            {/* Desktop: Stories count aligned with title */}
            <div className="hidden sm:block">
              <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 px-4 rounded-lg h-12 flex items-center">
                <span className="text-white text-sm font-medium">
                  Stories: <span className="font-semibold text-blue-400">{subscriptionData.currentCount}</span> of {subscriptionData.limit}
                </span>
                <span className="text-gray-400 text-sm ml-2">
                  ({remainingStories} remaining)
                </span>
                {isRefetching && (
                  <span className="ml-2">
                    <svg className="animate-spin h-3 w-3 text-blue-400 inline" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                  </span>
                )}
              </div>
            </div>
          </div>
          
          {/* Additional info below title */}
          <div className="flex flex-wrap items-center gap-3 mt-2">
            {(searchQuery || selectedTheme || selectedTags.length > 0) && (
              <div className="bg-blue-600/20 border border-blue-500/30 px-3 py-1 rounded-lg">
                <span className="text-blue-300 text-sm font-medium">
                  Showing {filteredCount} of {totalStories} stories
                </span>
              </div>
            )}
            {subscriptionData.planName === 'Free' && (
              <Link href="/pricing" className="text-blue-400 hover:text-blue-300 text-sm font-medium hover:underline transition-colors">
                Upgrade for more stories
              </Link>
            )}
          </div>
        </div>
        
        {/* Create button */}
        <Link href="/create" className="w-full sm:w-auto">
          <button 
            className="w-full sm:w-auto bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-6 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center gap-2 hover:scale-105 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed shadow-lg h-12"
            disabled={!subscriptionData.canCreate}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            Create New Story
          </button>
        </Link>
        
        {/* Mobile: Stories count below everything */}
        <div className="sm:hidden">
          <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 px-4 py-3 rounded-lg">
            <span className="text-white text-sm font-medium">
              Stories: <span className="font-semibold text-blue-400">{subscriptionData.currentCount}</span> of {subscriptionData.limit}
            </span>
            <span className="text-gray-400 text-sm ml-2">
              ({remainingStories} remaining)
            </span>
            {isRefetching && (
              <span className="ml-2">
                <svg className="animate-spin h-3 w-3 text-blue-400 inline" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
              </span>
            )}
          </div>
        </div>
      </div>

      {/* Search and filters */}
      <div className="bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 rounded-xl p-4">
        <div className="space-y-4">
          {/* All filters on one row for desktop, stacked on mobile */}
          <div className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center">
            {/* Search input - narrower on desktop */}
            <div className="flex-1 sm:max-w-md relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <input
                type="text"
                placeholder="Search stories..."
                value={searchQuery}
                onChange={(e) => onSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
              />
            </div>

            {/* Theme and Tags filters - side by side on mobile for better use of space */}
            <div className="flex gap-3 sm:contents">
              {/* Theme filter */}
              <div className="flex-1 sm:min-w-[160px] relative">
                <div className="relative">
                <select
                  value={selectedTheme}
                  onChange={(e) => onThemeChange(e.target.value)}
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer transition-all duration-200"
                  disabled={isLoadingData}
                >
                  <option value="">All Themes</option>
                  {themes.map((theme) => (
                    <option key={theme.id} value={theme.description}>
                      {theme.description}
                    </option>
                  ))}
                </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>

              {/* Tags filter */}
              <div className="flex-1 sm:min-w-[120px] relative">
                <div className="relative">
                  <select
                    className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer transition-all duration-200"
                    disabled={isLoadingData}
                    onChange={(e) => {
                      if (e.target.value) {
                        handleTagToggle(e.target.value);
                        e.target.value = ''; // Reset selection
                      }
                    }}
                  >
                    <option value="">Tags</option>
                    {tags.map((tag) => (
                      <option key={tag.id} value={tag.name}>
                        {tag.name}
                      </option>
                    ))}
                  </select>
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                    <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </div>
              </div>
            </div>

            {/* Clear filters button */}
            {(searchQuery || selectedTheme || selectedTags.length > 0) && (
              <button
                onClick={() => {
                  onSearchChange('');
                  onThemeChange('');
                  onTagsChange([]);
                }}
                className="px-4 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors text-sm whitespace-nowrap"
              >
                Clear Filters
              </button>
            )}
          </div>

          {/* Selected tags display */}
          {selectedTags.length > 0 && (
            <div className="flex flex-wrap gap-2">
              {selectedTags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-blue-600 text-white text-sm rounded-lg"
                >
                  {tag}
                  <button
                    onClick={() => handleTagToggle(tag)}
                    className="hover:bg-blue-700 rounded-full p-0.5 ml-1"
                  >
                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 