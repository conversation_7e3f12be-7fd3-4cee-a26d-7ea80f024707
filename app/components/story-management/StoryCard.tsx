'use client';

import { useState, useRef, useCallback } from 'react';
import Image from 'next/image';
import { Story } from '../../types/story';
import ShareStoryModal from '../ShareStoryModal';
import { getThemeColor } from '../../../lib/utils/themeColors';
import { useFeatures } from '../../hooks/useFeatures';

interface StoryCardProps {
  story: Story;
  onDelete: (uuid: string, title: string) => void;
}

export default function StoryCard({ story, onDelete }: StoryCardProps) {
  const [showShareModal, setShowShareModal] = useState(false);
  const [isPlaying, setIsPlaying] = useState(false);
  const [audioUrl, setAudioUrl] = useState<string | null>(null);
  const [isLoadingAudio, setIsLoadingAudio] = useState(false);
  const [audioError, setAudioError] = useState<string | null>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const { canGenerateAudio } = useFeatures();

  const createSlug = (title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  };

  const handleEditClick = () => {
    // Navigate to the story page where editing can be done
    window.location.href = `/stories/${story.story_uuid}/${createSlug(story.title)}`;
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  const handleDeleteClick = () => {
    onDelete(story.story_uuid, story.title);
  };

  const loadAudio = useCallback(async () => {
    if (audioUrl) return; // Already loaded
    
    setIsLoadingAudio(true);
    setAudioError(null);

    try {
      const response = await fetch('/api/generate-audio', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          story_id: story.id.toString(),
          force_regenerate: false 
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          setAudioError('Premium subscription required for audio');
        } else {
          setAudioError(data.error || 'Failed to load audio');
        }
        return;
      }

      if (!data.audio_url) {
        throw new Error('No audio URL in response');
      }

      setAudioUrl(data.audio_url);
    } catch (err) {
      setAudioError(err instanceof Error ? err.message : 'Failed to load audio');
    } finally {
      setIsLoadingAudio(false);
    }
  }, [story.id, audioUrl]);

  const handlePlayPause = useCallback(async () => {
    if (!canGenerateAudio() && !story.hasAudio) {
      // Navigate to story page for non-premium users
      window.location.href = `/stories/${story.story_uuid}/${createSlug(story.title)}`;
      return;
    }

    if (!audioUrl) {
      await loadAudio();
      return;
    }

    if (!audioRef.current) return;

    try {
      if (isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch {
      setAudioError('Failed to play audio');
      setIsPlaying(false);
    }
  }, [audioUrl, isPlaying, loadAudio, canGenerateAudio, story.hasAudio, story.story_uuid, story.title]);

  const handleAudioEnded = useCallback(() => {
    setIsPlaying(false);
  }, []);

  const handleAudioError = useCallback(() => {
    setAudioError('Failed to load audio file');
    setIsPlaying(false);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  // Generate excerpt from actual story content
  const getStoryExcerpt = () => {
    if (!story.content) {
      return `A magical story about ${story.main_character} in ${story.setting}...`;
    }
    
    // Get first 150 characters of the story content
    const excerpt = story.content.substring(0, 150);
    
    // Try to end at a complete sentence or word
    const lastPeriod = excerpt.lastIndexOf('.');
    const lastSpace = excerpt.lastIndexOf(' ');
    
    if (lastPeriod > 100) {
      return excerpt.substring(0, lastPeriod + 1);
    } else if (lastSpace > 100) {
      return excerpt.substring(0, lastSpace) + '...';
    } else {
      return excerpt + '...';
    }
  };

  const handleCardClick = (e: React.MouseEvent) => {
    // Only navigate if the click target is not a button or inside a button
    const target = e.target as HTMLElement;
    if (!target.closest('button')) {
      window.location.href = `/stories/${story.story_uuid}/${createSlug(story.title)}`;
    }
  };

  const hasAccess = canGenerateAudio();
  const showAudioButton = story.hasAudio || hasAccess;

  return (
    <>
      {/* Hidden audio element */}
      {audioUrl && (
        <audio
          ref={audioRef}
          src={audioUrl}
          onEnded={handleAudioEnded}
          onError={handleAudioError}
          preload="metadata"
          controls={false}
        />
      )}

      <div 
        onClick={handleCardClick}
        className="relative group bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-600 rounded-xl overflow-hidden hover:border-blue-500/30 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-[1.02] transform-gpu cursor-pointer h-full flex flex-col"
      >
        {/* Story Image */}
        <div className="relative h-48 bg-slate-700">
          {story.imageUrl ? (
            <Image
              src={story.imageUrl}
              alt={`Illustration for ${story.title}`}
              fill
              className="object-cover group-hover:scale-105 transition-all duration-300"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              placeholder="blur"
              blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAhEAACAQMDBQAAAAAAAAAAAAABAgMABAUGIWGRkqGx0f/EABUBAQEAAAAAAAAAAAAAAAAAAAMF/8QAGhEAAgIDAAAAAAAAAAAAAAAAAAECEgMRkf/aAAwDAQACEQMRAD8AltJagyeH0AthI5xdrLcNM91BF5pX2HaH9bcfaSXWGaRmknyJckliyjqTzSlT54b6bk+h0R+Rq5TaULdtqdu9n5cRxKbdnhkVZEaNgVZWUqwBBBBBHcEV9K9Dw=="
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-slate-700 to-slate-800">
              <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center">
                <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
            </div>
          )}

          {/* Audio indicator - Top Left */}
          {story.hasAudio && (
            <div className="absolute top-3 left-3">
              <span className="px-3 py-1 bg-purple-700/80 text-white text-xs rounded-lg font-medium shadow-sm flex items-center gap-1">
                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
                </svg>
                Audio
              </span>
            </div>
          )}

          {/* Theme Bubbles - Top Right */}
          {story.themes.length > 0 && (
            <div className="absolute top-3 right-3 flex gap-1">
              {story.themes.slice(0, 2).map((theme, index) => (
                <span 
                  key={index}
                  className={`px-3 py-1 ${getThemeColor(theme)} text-white text-xs rounded-lg font-medium shadow-sm`}
                >
                  {theme}
                </span>
              ))}
              {story.themes.length > 2 && (
                <span className="px-3 py-1 bg-gray-600/80 text-white text-xs rounded-lg font-medium shadow-sm">
                  +{story.themes.length - 2}
                </span>
              )}
            </div>
          )}

          {/* Date - Bottom Left */}
          <div className="absolute bottom-3 left-3">
            <span className="px-3 py-1 bg-black/60 text-white text-xs rounded-lg">
              {formatDate(story.created_at)}
            </span>
          </div>

          {/* Private/Public Status - Bottom Right */}
          <div className="absolute bottom-3 right-3">
            <span className={`px-3 py-1 text-xs font-medium rounded-lg shadow-sm ${
              story.is_public 
                ? 'bg-green-600/80 text-white border border-green-500/30' 
                : 'bg-gray-600/80 text-white border border-gray-500/30'
            }`}>
              {story.is_public ? 'Public' : 'Private'}
            </span>
          </div>
        </div>
        
        {/* Story Content */}
        <div className="p-6 flex-1 flex flex-col">
          <h3 className="text-xl font-bold text-white mb-3 overflow-hidden text-ellipsis" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {story.title}
          </h3>
          
          <p className="text-gray-300 text-sm mb-4 overflow-hidden text-ellipsis flex-1" style={{
            display: '-webkit-box',
            WebkitLineClamp: 2,
            WebkitBoxOrient: 'vertical'
          }}>
            {getStoryExcerpt()}
          </p>

          {/* Audio Error Display */}
          {audioError && (
            <div className="text-red-400 text-xs mb-2 px-2 py-1 bg-red-900/20 rounded">
              {audioError}
            </div>
          )}
          
          {/* Bottom Section with Action Buttons */}
          <div className="flex items-center justify-between mt-auto">
            {/* Action Buttons - Bottom Left */}
            <div className="flex gap-2">
              {/* Edit Button */}
              <button
                onClick={handleEditClick}
                className="p-2 bg-slate-700/50 hover:bg-blue-600/50 text-white rounded-lg transition-all duration-200 border border-slate-600/30 hover:border-blue-500/30"
                title="Edit story"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
              </button>
              
              {/* Share Button */}
              <button
                onClick={handleShareClick}
                className="p-2 bg-slate-700/50 hover:bg-green-600/50 text-white rounded-lg transition-all duration-200 border border-slate-600/30 hover:border-green-500/30"
                title="Share story"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              </button>
              
              {/* Delete Button */}
              <button
                onClick={handleDeleteClick}
                className="p-2 bg-slate-700/50 hover:bg-red-600/50 text-white rounded-lg transition-all duration-200 border border-slate-600/30 hover:border-red-500/30"
                title="Delete story"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
              </button>
            </div>

            {/* Read Buttons - Bottom Right */}
            <div className="flex gap-1.5 flex-shrink-0">
              {/* Audio/Play Button */}
              {showAudioButton && (
                <button
                  onClick={handlePlayPause}
                  disabled={isLoadingAudio}
                  className={`flex items-center gap-1 px-2.5 py-1.5 rounded-md text-sm transition-colors whitespace-nowrap ${
                    isPlaying
                      ? 'bg-purple-600 hover:bg-purple-700 text-white border border-purple-600'
                      : !hasAccess && !story.hasAudio
                      ? 'bg-transparent border border-gray-600 text-gray-400 cursor-not-allowed'
                      : 'bg-transparent border border-purple-600 text-purple-400 hover:bg-purple-600 hover:text-white'
                  }`}
                  title={!hasAccess && !story.hasAudio ? 'Premium required' : isPlaying ? 'Pause' : 'Play audio'}
                >
                  {isLoadingAudio ? (
                    <>
                      <div className="w-3.5 h-3.5 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
                      <span className="hidden sm:inline">Loading...</span>
                    </>
                  ) : isPlaying ? (
                    <>
                      <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.75 5.25v13.5m-7.5-13.5v13.5" />
                      </svg>
                      <span className="hidden sm:inline">Pause</span>
                      <span className="sm:hidden">⏸</span>
                    </>
                  ) : (
                    <>
                      <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3l14 9-14 9V3z" />
                      </svg>
                      <span className="hidden sm:inline">
                        {!hasAccess && !story.hasAudio ? 'Premium' : 'Listen'}
                      </span>
                      <span className="sm:hidden">▶</span>
                    </>
                  )}
                </button>
              )}
              
              {/* Read Button */}
              <button
                onClick={() => {
                  window.location.href = `/stories/${story.story_uuid}/${createSlug(story.title)}`;
                }}
                className="flex items-center justify-center px-3.5 py-1.5 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition-colors font-medium text-sm border border-blue-600 whitespace-nowrap"
              >
                Read
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Share Modal */}
      <ShareStoryModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        storyId={story.story_uuid}
        storyTitle={story.title}
        isPublic={story.is_public}
      />
    </>
  );
}