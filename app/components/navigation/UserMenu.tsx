'use client'

import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { ChevronDownIcon, UserIcon, ChartBarIcon, TagIcon, ArrowRightOnRectangleIcon, KeyIcon, CreditCardIcon } from '@heroicons/react/24/outline';
import { useState } from 'react';

interface UserMenuProps {
  isDropdownOpen: boolean;
  onToggleDropdown: () => void;
  onCloseDropdown: () => void;
  dropdownRef: React.RefObject<HTMLDivElement | null>;
}

export default function UserMenu({ 
  isDropdownOpen, 
  onToggleDropdown, 
  onCloseDropdown,
  dropdownRef 
}: UserMenuProps) {
  const { data: session } = useSession();
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Check if user has password authentication (not OAuth-only)
  // OAuth users typically have an image from their provider and no password
  const hasPasswordAuth = session?.user?.email && !session?.user?.image;

  const handleSignOut = async () => {
    if (isSigningOut) return; // Prevent multiple clicks
    
    try {
      setIsSigningOut(true);
      onCloseDropdown(); // Close dropdown immediately
      
      await signOut({
        callbackUrl: '/', // Redirect to home page
        redirect: true,   // Force redirect
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
      // Fallback: try again with different approach
      try {
        await signOut({ redirect: false });
        window.location.href = '/';
      } catch (fallbackError) {
        console.error('Fallback sign out error:', fallbackError);
        // Last resort: manual redirect
        window.location.href = '/';
      }
    }
  };

  return (
    <div className="hidden lg:flex items-center space-x-4">
      {!session ? (
        <div className="flex items-center space-x-4">
          <Link href="/sign-in">
            <button className="text-gray-300 hover:text-white transition-colors">
              Log In
            </button>
          </Link>
          <Link href="/sign-up">
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
              Sign Up
            </button>
          </Link>
        </div>
      ) : (
        <>
          {/* Account Dropdown */}
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={onToggleDropdown}
              className="flex items-center gap-2 px-3 py-2 text-gray-300 hover:text-white hover:bg-slate-700/50 rounded-lg transition-all duration-200 group"
              aria-label="Account menu"
            >
              <UserIcon className="h-4 w-4" />
              <span className="hidden sm:inline font-medium">Account</span>
              <ChevronDownIcon className={`h-4 w-4 transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
            </button>
            
            {isDropdownOpen && (
              <div className="absolute right-0 mt-3 w-56 bg-slate-800/95 backdrop-blur-sm border border-slate-600/50 rounded-xl shadow-2xl z-[9999] overflow-hidden">
                {/* User Info Header */}
                <div className="px-4 py-3 bg-slate-700/30 border-b border-slate-600/30">
                  <p className="text-sm font-medium text-white truncate">
                    {session.user?.name || session.user?.email || 'User'}
                  </p>
                  <p className="text-xs text-gray-400 truncate">
                    {session.user?.email}
                  </p>
                </div>
                
                {/* Menu Items */}
                <div className="py-2">
                  <Link
                    href="/profile"
                    className="flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-150 group"
                    onClick={onCloseDropdown}
                  >
                    <UserIcon className="h-4 w-4 text-gray-400 group-hover:text-blue-400 transition-colors" />
                    <span className="font-medium">Preferences</span>
                  </Link>
                  <Link
                    href="/account/subscription"
                    className="flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-150 group"
                    onClick={onCloseDropdown}
                  >
                    <CreditCardIcon className="h-4 w-4 text-gray-400 group-hover:text-emerald-400 transition-colors" />
                    <span className="font-medium">Subscription</span>
                  </Link>
                  {hasPasswordAuth && (
                    <Link
                      href="/account/password"
                      className="flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-150 group"
                      onClick={onCloseDropdown}
                    >
                      <KeyIcon className="h-4 w-4 text-gray-400 group-hover:text-yellow-400 transition-colors" />
                      <span className="font-medium">Update Password</span>
                    </Link>
                  )}
                  <Link
                    href="/stats"
                    className="flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-150 group"
                    onClick={onCloseDropdown}
                  >
                    <ChartBarIcon className="h-4 w-4 text-gray-400 group-hover:text-green-400 transition-colors" />
                    <span className="font-medium">Stats</span>
                  </Link>
                  <Link
                    href="/tags"
                    className="flex items-center gap-3 px-4 py-2.5 text-gray-300 hover:text-white hover:bg-slate-700/50 transition-all duration-150 group"
                    onClick={onCloseDropdown}
                  >
                    <TagIcon className="h-4 w-4 text-gray-400 group-hover:text-purple-400 transition-colors" />
                    <span className="font-medium">Tags</span>
                  </Link>
                  
                  {/* Divider */}
                  <div className="my-2 border-t border-slate-600/30"></div>
                  
                  <button
                    onClick={handleSignOut}
                    disabled={isSigningOut}
                    className="flex items-center gap-3 w-full px-4 py-2.5 text-gray-300 hover:text-red-400 hover:bg-red-900/20 transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed group"
                  >
                    <ArrowRightOnRectangleIcon className="h-4 w-4 text-gray-400 group-hover:text-red-400 transition-colors" />
                    <span className="font-medium">
                      {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                    </span>
                  </button>
                </div>
              </div>
            )}
          </div>


        </>
      )}
    </div>
  );
}