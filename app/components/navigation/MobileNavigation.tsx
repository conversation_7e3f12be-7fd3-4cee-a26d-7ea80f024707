'use client';

import Link from 'next/link';
import { useSession, signOut } from 'next-auth/react';
import { useState } from 'react';

interface MobileNavigationProps {
  currentPage?: string;
  isOpen: boolean;
  onClose: () => void;
}

export default function MobileNavigation({ currentPage, isOpen, onClose }: MobileNavigationProps) {
  const { data: session } = useSession();
  const [isSigningOut, setIsSigningOut] = useState(false);

  // Check if user has password authentication (not OAuth-only)
  const hasPasswordAuth = session?.user?.email && !session?.user?.image;
  
  const getLinkClassName = (page: string) => 
    `block px-4 py-3 rounded-md text-base font-medium ${
      currentPage === page 
        ? 'text-white bg-slate-700' 
        : 'text-gray-300 hover:text-white hover:bg-slate-700'
    } transition-colors`;

  const getTabletLinkClassName = (page: string) => 
    `px-4 py-2 rounded-lg text-base font-medium ${
      currentPage === page 
        ? 'text-white bg-slate-700 border border-slate-600' 
        : 'text-gray-300 hover:text-white hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500'
    } transition-all duration-200`;

  const handleSignOut = async () => {
    if (isSigningOut) return; // Prevent multiple clicks
    
    try {
      setIsSigningOut(true);
      onClose(); // Close mobile menu immediately
      
      await signOut({
        callbackUrl: '/', // Redirect to home page
        redirect: true,   // Force redirect
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setIsSigningOut(false);
      // Fallback: try again with different approach
      try {
        await signOut({ redirect: false });
        window.location.href = '/';
      } catch (fallbackError) {
        console.error('Fallback sign out error:', fallbackError);
        // Last resort: manual redirect
        window.location.href = '/';
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="lg:hidden border-t border-slate-700">
      {/* Mobile Layout (< 768px) */}
      <div className="md:hidden px-3 pt-4 pb-5">
        {/* Main Navigation Links */}
        <div className="space-y-1">
          <Link href="/" className={getLinkClassName('home')} onClick={onClose}>
            Home
          </Link>
          <Link href="/about" className={getLinkClassName('about')} onClick={onClose}>
            About Us
          </Link>
          <Link href="/pricing" className={getLinkClassName('pricing')} onClick={onClose}>
            Pricing
          </Link>
          <Link href="/public/stories" className={getLinkClassName('public-stories')} onClick={onClose}>
            Discover Stories
          </Link>
          
          {session && (
            <>
              <Link href="/my-stories" className={getLinkClassName('my-stories')} onClick={onClose} prefetch={true}>
                My Stories
              </Link>
              <Link 
                href="/create" 
                className={`block px-4 py-3 rounded-lg text-base font-semibold transition-all duration-200 ${
                  currentPage === 'create' 
                    ? 'bg-blue-600/20 border border-blue-500/40 text-white' 
                    : 'bg-blue-600/10 border border-blue-500/30 hover:bg-blue-600/20 hover:border-blue-500/50 text-blue-200 hover:text-white'
                }`}
                onClick={onClose} 
                prefetch={true}
              >
                Create Story
              </Link>
            </>
          )}
        </div>

        {session && (
          <>
            {/* Mobile Account Section */}
            <div className="border-t border-slate-700/50 mt-6 pt-4">
              <div className="px-4 py-2 text-xs font-semibold text-gray-400 uppercase tracking-wider">
                Account
              </div>
              <div className="space-y-1">
                <Link href="/profile" className={getLinkClassName('profile')} onClick={onClose}>
                  Preferences
                </Link>
                <Link href="/account/subscription" className={getLinkClassName('subscription')} onClick={onClose}>
                  Subscription
                </Link>
                {hasPasswordAuth && (
                  <Link href="/account/password" className={getLinkClassName('password')} onClick={onClose}>
                    Update Password
                  </Link>
                )}
                <Link href="/stats" className={getLinkClassName('stats')} onClick={onClose}>
                  Stats
                </Link>
                <Link href="/tags" className={getLinkClassName('tags')} onClick={onClose}>
                  Tags
                </Link>
                <button
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  className="block w-full text-left px-4 py-3 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-slate-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                </button>
              </div>
            </div>
          </>
        )}

        {!session && (
          <div className="border-t border-slate-700/50 mt-6 pt-4 space-y-2">
            <Link
              href="/sign-in"
              className="block px-4 py-3 rounded-md text-base font-medium text-gray-300 hover:text-white hover:bg-slate-700 transition-colors"
              onClick={onClose}
            >
              Log In
            </Link>
            <Link
              href="/sign-up"
              className="block px-4 py-3 rounded-md text-base font-medium bg-blue-600 text-white hover:bg-blue-700 transition-colors"
              onClick={onClose}
            >
              Sign Up
            </Link>
          </div>
        )}
      </div>

      {/* Tablet Layout (768px - 1023px) */}
      <div className="hidden md:block lg:hidden px-6 py-6">
        {/* Main Navigation - Horizontal */}
        <div className="flex flex-wrap gap-3 justify-center mb-6">
          <Link 
            href="/" 
            className={getTabletLinkClassName('home')} 
            onClick={onClose}
          >
            Home
          </Link>
          <Link 
            href="/about" 
            className={getTabletLinkClassName('about')} 
            onClick={onClose}
          >
            About Us
          </Link>
          <Link 
            href="/pricing" 
            className={getTabletLinkClassName('pricing')} 
            onClick={onClose}
          >
            Pricing
          </Link>
          <Link 
            href="/public/stories" 
            className={getTabletLinkClassName('public-stories')} 
            onClick={onClose}
          >
            Discover Stories
          </Link>
        </div>
        
        {session && (
          <>
            {/* Tablet User-specific Navigation */}
            <div className="flex flex-wrap gap-3 justify-center mb-6">
              <Link 
                href="/my-stories" 
                className={getTabletLinkClassName('my-stories')} 
                onClick={onClose} 
                prefetch={true}
              >
                My Stories
              </Link>
              <Link 
                href="/create" 
                className={`px-6 py-2 rounded-lg text-base font-semibold transition-all duration-200 ${
                  currentPage === 'create' 
                    ? 'bg-blue-600/20 border border-blue-500/40 text-white' 
                    : 'bg-blue-600/10 border border-blue-500/30 hover:bg-blue-600/20 hover:border-blue-500/50 text-blue-200 hover:text-white'
                }`}
                onClick={onClose} 
                prefetch={true}
              >
                Create Story
              </Link>
            </div>

            {/* Tablet Account Section */}
            <div className="border-t border-slate-700/50 pt-4">
              <div className="text-center mb-4">
                <span className="text-xs font-semibold text-gray-400 uppercase tracking-wider">Account</span>
              </div>
              <div className="flex flex-wrap gap-3 justify-center">
                <Link href="/profile" className={getTabletLinkClassName('profile')} onClick={onClose}>
                  Preferences
                </Link>
                <Link href="/account/subscription" className={getTabletLinkClassName('subscription')} onClick={onClose}>
                  Subscription
                </Link>
                {hasPasswordAuth && (
                  <Link href="/account/password" className={getTabletLinkClassName('password')} onClick={onClose}>
                    Update Password
                  </Link>
                )}
                <Link href="/stats" className={getTabletLinkClassName('stats')} onClick={onClose}>
                  Stats
                </Link>
                <Link href="/tags" className={getTabletLinkClassName('tags')} onClick={onClose}>
                  Tags
                </Link>
                <button
                  onClick={handleSignOut}
                  disabled={isSigningOut}
                  className="px-4 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-white hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSigningOut ? 'Signing Out...' : 'Sign Out'}
                </button>
              </div>
            </div>
          </>
        )}

        {!session && (
          <div className="border-t border-slate-700/50 pt-6">
            <div className="flex gap-4 justify-center">
              <Link
                href="/sign-in"
                className="px-6 py-2 rounded-lg text-base font-medium text-gray-300 hover:text-white hover:bg-slate-700/50 border border-slate-600/30 hover:border-slate-500 transition-all duration-200"
                onClick={onClose}
              >
                Log In
              </Link>
              <Link
                href="/sign-up"
                className="px-6 py-2 rounded-lg text-base font-medium bg-blue-600 text-white hover:bg-blue-700 border border-blue-600 hover:border-blue-700 transition-all duration-200"
                onClick={onClose}
              >
                Sign Up
              </Link>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}