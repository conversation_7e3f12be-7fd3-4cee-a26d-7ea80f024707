'use client';

import Link from 'next/link';
import { useSession } from 'next-auth/react';

interface DesktopNavigationProps {
  currentPage?: string;
}

export default function DesktopNavigation({ currentPage }: DesktopNavigationProps) {
  const { data: session } = useSession();
  
  const getLinkClassName = (page: string) => 
    currentPage === page 
      ? 'text-white font-semibold relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-blue-500 after:rounded-full' 
      : 'text-gray-300 hover:text-white transition-all duration-200 relative hover:scale-105 after:absolute after:bottom-0 after:left-0 after:w-0 after:h-0.5 after:bg-blue-500 after:rounded-full after:transition-all after:duration-200 hover:after:w-full';

  return (
    <div className="hidden lg:flex items-center space-x-4">
      <Link href="/" className={getLinkClassName('home')} prefetch={true}>
        Home
      </Link>
      <div className="h-4 w-px bg-slate-600"></div>
      <Link href="/about" className={getLinkClassName('about')} prefetch={true}>
        About Us
      </Link>
      <div className="h-4 w-px bg-slate-600"></div>
      <Link href="/pricing" className={getLinkClassName('pricing')} prefetch={true}>
        Pricing
      </Link>
      <div className="h-4 w-px bg-slate-600"></div>
      <Link href="/public/stories" className={getLinkClassName('public-stories')} prefetch={true}>
        Discover Stories
      </Link>
      {session && (
        <>
          <div className="h-4 w-px bg-slate-600"></div>
          <Link href="/my-stories" className={getLinkClassName('my-stories')} prefetch={true}>
            My Stories
          </Link>
          <div className="h-4 w-px bg-slate-600"></div>
          <Link 
            href="/create" 
            className={`${
              currentPage === 'create'
                ? 'bg-blue-600/20 border border-blue-500/40 text-white px-3 py-1.5 rounded-lg font-semibold'
                : 'bg-blue-600/10 border border-blue-500/30 hover:bg-blue-600/20 hover:border-blue-500/50 text-blue-200 hover:text-white px-3 py-1.5 rounded-lg font-semibold transition-all duration-200 hover:scale-105'
            }`}
            prefetch={true}
          >
            Create Story
          </Link>
        </>
      )}
    </div>
  );
}