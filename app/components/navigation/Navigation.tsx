'use client';

import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { Bars3Icon, XMarkIcon } from '@heroicons/react/24/outline';
import { useClickOutside } from '../../hooks/useClickOutside';
import DesktopNavigation from './DesktopNavigation';
import MobileNavigation from './MobileNavigation';
import UserMenu from './UserMenu';

interface NavigationProps {
  currentPage?: string;
}

export default function Navigation({ currentPage }: NavigationProps) {
  const [isAccountDropdownOpen, setIsAccountDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const dropdownRef = useClickOutside<HTMLDivElement>(() => {
    setIsAccountDropdownOpen(false);
  });

  const toggleAccountDropdown = () => {
    setIsAccountDropdownOpen(!isAccountDropdownOpen);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  const closeDropdown = () => {
    setIsAccountDropdownOpen(false);
  };

  return (
    <nav className="bg-slate-800/95 backdrop-blur-md border-b border-slate-600/50 shadow-xl relative z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center group">
              <Image
                src="/images/logo.png"
                alt="Home"
                width={32}
                height={32}
                className="mr-3"
                priority
              />
              <span className="text-white font-bold text-xl group-hover:text-blue-200 transition-colors duration-200">MyStoryMaker</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <DesktopNavigation currentPage={currentPage} />

          {/* Right side - Auth & Account */}
          <div className="flex items-center gap-3">
            {/* Mobile/Tablet menu button */}
            <button
              onClick={toggleMobileMenu}
              className="lg:hidden bg-slate-700/80 hover:bg-slate-600/90 text-gray-300 hover:text-white p-3 rounded-xl transition-all duration-200 border border-slate-500/40 hover:border-slate-400/60 shadow-lg backdrop-blur-sm"
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <XMarkIcon className="h-7 w-7" />
              ) : (
                <Bars3Icon className="h-7 w-7" />
              )}
            </button>

            <UserMenu
              isDropdownOpen={isAccountDropdownOpen}
              onToggleDropdown={toggleAccountDropdown}
              onCloseDropdown={closeDropdown}
              dropdownRef={dropdownRef}
            />
          </div>
        </div>

        {/* Mobile Menu */}
        <MobileNavigation
          currentPage={currentPage}
          isOpen={isMobileMenuOpen}
          onClose={closeMobileMenu}
        />
      </div>
    </nav>
  );
}