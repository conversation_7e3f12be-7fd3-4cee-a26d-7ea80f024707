import { useEffect, useState } from 'react';

interface GeneratingDialogProps {
  isOpen: boolean;
  type: 'story' | 'audio' | 'transcription';
}

const storyMessages = [
  "Weaving your magical tale...",
  "Sprinkling some story dust...",
  "Gathering the perfect words...",
  "Creating something wonderful...",
  "Painting your adventure...",
  "Adding a touch of magic...",
  "Bringing your story to life...",
  "Crafting your unique adventure..."
];

const audioMessages = [
  "Giving your story a voice...",
  "Recording the perfect narration...",
  "Adding magical sound waves...",
  "Bringing words to life...",
  "Composing your audio adventure...",
  "Tuning the perfect storytelling...",
  "Rehearsing the perfect performance...",
  "Enchanting your ears with magic..."
];

const transcriptionMessages = [
  "Analyzing your audio magic...",
  "Finding every perfect word...",
  "Mapping words to time...",
  "Creating interactive highlights...",
  "Processing speech patterns...",
  "Adding word-level precision...",
  "Preparing the interactive show...",
  "Syncing words with audio..."
];

export default function GeneratingDialog({ isOpen, type }: GeneratingDialogProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [dots, setDots] = useState('');

  const messages = type === 'story' ? storyMessages : type === 'audio' ? audioMessages : transcriptionMessages;

  useEffect(() => {
    if (!isOpen) return;

    // Cycle through messages every 3 seconds
    const messageInterval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
    }, 3000);

    // Animate dots every 500ms
    const dotsInterval = setInterval(() => {
      setDots((prev) => {
        if (prev === '...') return '';
        return prev + '.';
      });
    }, 500);

    return () => {
      clearInterval(messageInterval);
      clearInterval(dotsInterval);
    };
  }, [isOpen, messages.length]);

  if (!isOpen) return null;

  const getIconAndGradient = () => {
    switch (type) {
      case 'story':
        return {
          icon: (
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          ),
          gradient: 'from-purple-400 to-pink-500',
          ringColor: 'ring-purple-400/30'
        };
      case 'audio':
        return {
          icon: (
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 12a3 3 0 106 0v5a3 3 0 11-6 0v-5z" />
            </svg>
          ),
          gradient: 'from-blue-400 to-green-500',
          ringColor: 'ring-blue-400/30'
        };
      case 'transcription':
        return {
          icon: (
            <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          ),
          gradient: 'from-purple-400 to-orange-500',
          ringColor: 'ring-purple-400/30'
        };
    }
  };

  const { icon, gradient, ringColor } = getIconAndGradient();

  const getTitle = () => {
    switch (type) {
      case 'story':
        return 'Creating Your Story';
      case 'audio':
        return 'Generating Audio';
      case 'transcription':
        return 'Creating Transcription';
    }
  };

  const getFunFact = () => {
    switch (type) {
      case 'story':
        return "Did you know? Every great story starts with a single word!";
      case 'audio':
        return "Fun fact: Your story will sound even more magical when narrated!";
      case 'transcription':
        return "Amazing: We're mapping every word to its exact timing in the audio!";
    }
  };

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-3xl p-8 max-w-md w-full text-center shadow-2xl border border-slate-200 dark:border-slate-700">
        {/* Main Icon with Elegant Animation */}
        <div className="mb-8">
          <div className={`w-24 h-24 mx-auto bg-gradient-to-br ${gradient} rounded-full flex items-center justify-center shadow-lg ring-8 ${ringColor} animate-pulse`}>
            {icon}
          </div>
          
          {/* Subtle floating elements */}
          <div className="relative -mt-12">
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-32 h-32 border-2 border-slate-200 dark:border-slate-600 rounded-full animate-spin opacity-20" style={{ animationDuration: '8s' }}></div>
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="w-40 h-40 border border-slate-300 dark:border-slate-500 rounded-full animate-spin opacity-10" style={{ animationDuration: '12s', animationDirection: 'reverse' }}></div>
            </div>
          </div>
        </div>

        {/* Title */}
        <h3 className="text-2xl font-bold text-slate-800 dark:text-white mb-4">
          {getTitle()}
        </h3>
        
        {/* Friendly Message */}
        <div className="mb-8 min-h-[3rem] flex items-center justify-center">
          <p className="text-slate-600 dark:text-slate-300 text-lg transition-all duration-500 ease-in-out">
            {messages[currentMessageIndex]}{dots}
          </p>
        </div>

        {/* Elegant Progress Bar */}
        <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-2 mb-6 overflow-hidden">
          <div className={`bg-gradient-to-r ${gradient} h-2 rounded-full transition-all duration-1000 ease-out`} 
               style={{ 
                 width: '60%',
                 animation: 'progress-pulse 2s ease-in-out infinite alternate'
               }}>
          </div>
        </div>

        {/* Fun Fact */}
        <div className="p-4 bg-slate-50 dark:bg-slate-700/50 rounded-2xl border border-slate-200 dark:border-slate-600">
          <p className="text-slate-600 dark:text-slate-400 text-sm font-medium">
            {getFunFact()}
          </p>
        </div>
      </div>
      
      <style jsx>{`
        @keyframes progress-pulse {
          0% { opacity: 0.6; }
          100% { opacity: 1; }
        }
      `}</style>
    </div>
  );
} 