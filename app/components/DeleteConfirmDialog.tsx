import { useEffect, useState } from 'react';

interface DeleteConfirmDialogProps {
  isOpen: boolean;
  storyTitle: string;
  onConfirm: () => void;
  onCancel: () => void;
  isDeleting?: boolean;
}

const confirmMessages = [
  "Are you really sure about this?",
  "Take a moment to think it over...",
  "This story has been on quite an adventure!",
  "Every story deserves a second chance...",
  "Stories are like old friends...",
  "Once deleted, the magic fades away...",
  "Even magical creatures would miss this tale!"
];

const deletingMessages = [
  "Carefully packing up your story...",
  "Moving it to the story vault...",
  "Saying goodbye with sparkles...",
  "Tucking it away for safekeeping...",
  "Sending it to story dreamland...",
  "The curtain falls on this tale...",
  "Closing this chapter gently...",
  "Preserving the memories..."
];

export default function DeleteConfirmDialog({ 
  isOpen, 
  storyTitle, 
  onConfirm, 
  onCancel, 
  isDeleting = false 
}: DeleteConfirmDialogProps) {
  const [currentMessageIndex, setCurrentMessageIndex] = useState(0);
  const [dots, setDots] = useState('');

  const messages = isDeleting ? deletingMessages : confirmMessages;

  useEffect(() => {
    if (!isOpen) return;

    // Cycle through messages every 3 seconds
    const messageInterval = setInterval(() => {
      setCurrentMessageIndex((prev) => (prev + 1) % messages.length);
    }, 3000);

    // Animate dots every 500ms when deleting
    let dotsInterval: NodeJS.Timeout;
    if (isDeleting) {
      dotsInterval = setInterval(() => {
        setDots((prev) => {
          if (prev === '...') return '';
          return prev + '.';
        });
      }, 500);
    }

    return () => {
      clearInterval(messageInterval);
      if (dotsInterval) clearInterval(dotsInterval);
    };
  }, [isOpen, messages.length, isDeleting]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-slate-800 rounded-3xl p-8 max-w-md w-full text-center shadow-2xl border border-slate-200 dark:border-slate-700 relative overflow-hidden">
        
        {/* Magical Animation at Top */}
        <div className="absolute top-0 left-0 right-0 h-20 overflow-hidden">
          {/* Floating magical sparkles with different sizes and colors */}
          <div className="absolute top-3 left-12 w-3 h-3 bg-gradient-to-r from-yellow-300 to-yellow-500 rounded-full animate-ping opacity-75" style={{ animationDelay: '0s', animationDuration: '2s' }}></div>
          <div className="absolute top-8 left-20 w-2 h-2 bg-gradient-to-r from-pink-300 to-pink-500 rounded-full animate-ping opacity-60" style={{ animationDelay: '0.3s', animationDuration: '2.5s' }}></div>
          <div className="absolute top-2 right-16 w-4 h-4 bg-gradient-to-r from-purple-300 to-purple-500 rounded-full animate-ping opacity-80" style={{ animationDelay: '0.6s', animationDuration: '1.8s' }}></div>
          <div className="absolute top-10 right-24 w-2 h-2 bg-gradient-to-r from-blue-300 to-blue-500 rounded-full animate-ping opacity-70" style={{ animationDelay: '0.9s', animationDuration: '2.2s' }}></div>
          <div className="absolute top-5 left-1/2 w-3 h-3 bg-gradient-to-r from-green-300 to-green-500 rounded-full animate-ping opacity-65" style={{ animationDelay: '1.2s', animationDuration: '2.8s' }}></div>
          
          {/* Magical star shapes */}
          <div className="absolute top-4 left-8 text-yellow-400 animate-pulse opacity-80" style={{ animationDelay: '0.5s' }}>✦</div>
          <div className="absolute top-12 right-8 text-pink-400 animate-pulse opacity-70" style={{ animationDelay: '1s' }}>✧</div>
          <div className="absolute top-6 right-32 text-purple-400 animate-pulse opacity-90" style={{ animationDelay: '1.5s' }}>✦</div>
          
          {/* Magical wave effect */}
          <div className="absolute top-4 left-0 w-full h-2 bg-gradient-to-r from-transparent via-purple-200 to-transparent opacity-40 animate-pulse" style={{ animationDelay: '0s' }}></div>
          <div className="absolute top-12 left-0 w-full h-1 bg-gradient-to-r from-transparent via-pink-200 to-transparent opacity-30 animate-pulse" style={{ animationDelay: '1s' }}></div>
        </div>

        {/* Main Icon - Magical and Fun */}
        <div className="mb-6 mt-6">
          {isDeleting ? (
            <div className="relative">
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-purple-400 via-pink-500 to-red-500 rounded-full flex items-center justify-center shadow-2xl animate-pulse">
                <svg className="w-12 h-12 text-white animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24" style={{ animationDuration: '3s' }}>
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                </svg>
              </div>
              {/* Magical particles around deleting icon */}
              <div className="absolute -top-2 -right-2 w-4 h-4 bg-yellow-400 rounded-full animate-bounce opacity-80" style={{ animationDelay: '0s' }}></div>
              <div className="absolute -bottom-2 -left-2 w-3 h-3 bg-pink-400 rounded-full animate-bounce opacity-70" style={{ animationDelay: '0.5s' }}></div>
              <div className="absolute top-0 -left-4 w-2 h-2 bg-purple-400 rounded-full animate-bounce opacity-60" style={{ animationDelay: '1s' }}></div>
            </div>
          ) : (
            <div className="relative">
              <div className="w-24 h-24 mx-auto bg-gradient-to-br from-amber-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-2xl">
                <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12.5 2L15.09 8.26L22 9L17 14.14L18.18 21.02L12.5 17.77L6.82 21.02L8 14.14L3 9L9.91 8.26L12.5 2Z" />
                </svg>
              </div>
              {/* Magical sparkles around main icon */}
              <div className="absolute -top-3 -right-3 text-yellow-400 text-xl animate-pulse opacity-90" style={{ animationDelay: '0s' }}>✨</div>
              <div className="absolute -bottom-3 -left-3 text-pink-400 text-lg animate-pulse opacity-80" style={{ animationDelay: '0.7s' }}>⭐</div>
              <div className="absolute top-2 -left-5 text-purple-400 text-sm animate-pulse opacity-70" style={{ animationDelay: '1.4s' }}>✦</div>
              <div className="absolute bottom-2 -right-5 text-blue-400 text-sm animate-pulse opacity-75" style={{ animationDelay: '2.1s' }}>✧</div>
            </div>
          )}
        </div>

        {/* Title */}
        <h3 className="text-2xl font-bold text-slate-800 dark:text-white mb-3">
          {isDeleting ? 'Saying Goodbye...' : 'Delete Story?'}
        </h3>
        
        {/* Story Title Card */}
        <div className="bg-slate-100 dark:bg-slate-700 rounded-2xl p-4 mb-6 border border-slate-200 dark:border-slate-600">
          <p className="text-blue-600 dark:text-blue-400 font-semibold">"{storyTitle}"</p>
        </div>

        {/* Friendly Message */}
        <div className="mb-6 min-h-[3rem] flex items-center justify-center">
          <p className="text-slate-600 dark:text-slate-300 text-lg transition-all duration-500 ease-in-out">
            {messages[currentMessageIndex]}{isDeleting ? dots : ''}
          </p>
        </div>

        {/* Progress Bar (only when deleting) */}
        {isDeleting && (
          <div className="w-full bg-slate-200 dark:bg-slate-700 rounded-full h-3 mb-6 overflow-hidden">
            <div className="bg-gradient-to-r from-orange-400 to-red-500 h-3 rounded-full transition-all duration-1000 ease-out animate-pulse" 
                 style={{ width: '75%' }}></div>
          </div>
        )}

        {/* Action Buttons (only when not deleting) */}
        {!isDeleting && (
          <div className="flex gap-4 justify-center">
            <button
              onClick={onCancel}
              className="px-8 py-3 bg-slate-100 hover:bg-slate-200 dark:bg-slate-700 dark:hover:bg-slate-600 text-slate-700 dark:text-slate-300 rounded-2xl font-semibold transition-all duration-200 flex items-center gap-3 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
              Keep Story
            </button>
            <button
              onClick={onConfirm}
              className="px-8 py-3 bg-red-500 hover:bg-red-600 text-white rounded-2xl font-semibold transition-all duration-200 flex items-center gap-3 shadow-md hover:shadow-lg transform hover:scale-105"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              Delete
            </button>
          </div>
        )}
      </div>
    </div>
  );
} 