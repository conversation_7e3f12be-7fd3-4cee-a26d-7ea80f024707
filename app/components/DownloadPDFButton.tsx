'use client';

import { useState } from 'react';
import { usePremiumFeatures } from '../hooks/usePremiumFeatures';
import Link from 'next/link';
import { trackStoryDownload } from './analytics';

interface StoryData {
  id: number;
  title: string;
  content: string;
  themes: string[];
  ageRange: string;
  createdAt: string;
  imageUrl?: string;
}

interface DownloadPDFButtonProps {
  story: StoryData;
  className?: string;
}

export default function DownloadPDFButton({ story, className = '' }: DownloadPDFButtonProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const { premiumAccess, isLoading } = usePremiumFeatures();

  const handleDownload = async () => {
    setIsGenerating(true);
    
    try {
      // Dynamic import to prevent server-side bundling issues
      const { StoryPDFGenerator } = await import('../../lib/pdf/storyPdfGenerator');
      const generator = new StoryPDFGenerator();
      const pdfBlob = await generator.generateStoryPDF(story);
      
      // Create download link
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Track PDF download
      trackStoryDownload('pdf');
      
      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setIsGenerating(false);
    }
  };

  if (isLoading) {
    return (
      <div className={`animate-pulse bg-slate-700 rounded-lg h-10 w-32 ${className}`}></div>
    );
  }

  if (!premiumAccess?.hasAccess) {
    return (
      <div className={`bg-slate-700 border border-slate-600 rounded-lg p-3 ${className}`}>
        <div className="flex items-center gap-2 mb-2">
          <svg className="w-4 h-4 text-amber-400" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <span className="text-sm font-medium text-white">Premium Feature</span>
        </div>
        <p className="text-sm text-gray-300 mb-2">
          PDF downloads are only available for Starter and Family plan subscribers.
        </p>
        <p className="text-xs text-gray-400 mb-3">
          Current plan: <span className="font-medium">{premiumAccess?.planName || 'Free'}</span>
        </p>
        <Link href="/pricing">
          <button className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-lg transition-colors">
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z" clipRule="evenodd" />
            </svg>
            Upgrade Plan
          </button>
        </Link>
      </div>
    );
  }

  return (
    <button
      onClick={handleDownload}
      disabled={isGenerating}
      className={`flex items-center gap-2 px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors ${className}`}
    >
      {isGenerating ? (
        <>
          <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
          Generating...
        </>
      ) : (
        <>
          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          Download PDF
        </>
      )}
    </button>
  );
}