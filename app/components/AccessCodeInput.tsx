'use client'

import { useState } from 'react';

interface AccessCodeInputProps {
  onCodeRedeemed: () => void;
}

export default function AccessCodeInput({ onCodeRedeemed }: AccessCodeInputProps) {
  const [code, setCode] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!code.trim()) {
      setMessage('Please enter an access code');
      setMessageType('error');
      return;
    }

    setLoading(true);
    setMessage('');
    setMessageType('');

    try {
      const response = await fetch('/api/access-code/redeem', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code.trim() }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setMessage(data.message);
        setMessageType('success');
        setCode('');
        // Refresh subscription data
        setTimeout(() => {
          onCodeRedeemed();
        }, 1500);
      } else {
        setMessage(data.error || 'Failed to redeem access code');
        setMessageType('error');
      }
    } catch (error) {
      console.error('Error redeeming access code:', error);
      setMessage('An error occurred. Please try again.');
      setMessageType('error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-slate-800/50 border border-slate-700/30 rounded-lg p-4">
      <form onSubmit={handleSubmit} className="space-y-3">
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <input
              type="text"
              value={code}
              onChange={(e) => setCode(e.target.value.toUpperCase())}
              placeholder="Have an access code?"
              className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-md text-white placeholder-gray-400 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-transparent"
              disabled={loading}
            />
          </div>
          <button
            type="submit"
            disabled={loading || !code.trim()}
            className="px-4 py-2 bg-blue-600/80 hover:bg-blue-600 disabled:bg-slate-600 disabled:cursor-not-allowed text-white text-sm font-medium rounded-md transition-colors"
          >
            {loading ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-3 w-3 border-b border-white"></div>
                <span>Applying...</span>
              </div>
            ) : (
              'Apply'
            )}
          </button>
        </div>
        
        {message && (
          <div className={`text-sm ${
            messageType === 'success' ? 'text-green-400' : 'text-red-400'
          }`}>
            {message}
          </div>
        )}
      </form>
    </div>
  );
} 