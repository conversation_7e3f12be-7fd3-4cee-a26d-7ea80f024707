'use client';

import { useState } from 'react';
import { getBaseUrl } from '../../lib/utils';
import { trackStoryShared } from './analytics';

interface ShareStoryModalProps {
  isOpen: boolean;
  onClose: () => void;
  storyId: string;
  storyTitle: string;
  isPublic: boolean;
}

interface ShareRecord {
  id: number;
  type: string;
  recipient_email: string | null;
  share_url: string;
  expires_at: string;
  created_at: string;
  is_expired: boolean;
}

export default function ShareStoryModal({ 
  isOpen, 
  onClose, 
  storyId, 
  storyTitle, 
  isPublic 
}: ShareStoryModalProps) {
  const [shareType, setShareType] = useState<'email' | 'link' | 'facebook'>('link');
  const [recipientEmail, setRecipientEmail] = useState('');
  const [isSharing, setIsSharing] = useState(false);
  const [shareUrl, setShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [existingShares, setExistingShares] = useState<ShareRecord[]>([]);
  const [showExistingShares, setShowExistingShares] = useState(false);
  const [localIsPublic, setLocalIsPublic] = useState(isPublic);
  const [isUpdatingVisibility, setIsUpdatingVisibility] = useState(false);

  // Load existing shares when modal opens
  const loadExistingShares = async () => {
    try {
      const response = await fetch(`/api/stories/${storyId}/share`);
      if (response.ok) {
        const shares = await response.json();
        setExistingShares(shares);
      }
    } catch (error) {
      console.error('Error loading existing shares:', error);
    }
  };

  const handleShare = async () => {
    if (shareType === 'email' && (!recipientEmail || !recipientEmail.includes('@'))) {
      setError('Please enter a valid email address.');
      return;
    }

    if (shareType === 'facebook' && !localIsPublic) {
      setError('Story must be public to share on Facebook.');
      return;
    }

    setIsSharing(true);
    setError(null);
    setSuccess(null);

    try {
      if (shareType === 'facebook') {
        // Track Facebook share
        trackStoryShared('facebook');
        
        // Use public story URL for Facebook sharing - no need to create share record
        const publicUrl = `${getBaseUrl()}/public/stories/${storyId}`;
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(publicUrl)}`;
        window.open(facebookUrl, '_blank', 'width=600,height=400');
        setSuccess('Facebook share dialog opened!');
        setIsSharing(false);
        return;
      }

      // For email and link sharing, create a share record
      const response = await fetch(`/api/stories/${storyId}/share`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type: shareType,
          recipient_email: shareType === 'email' ? recipientEmail : null,
          expires_in_days: 30
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create share');
      }

      setShareUrl(data.share_url);
      
      // Track successful sharing
      if (shareType === 'email') {
        trackStoryShared('email');
        setSuccess(`Share link sent to ${recipientEmail}!`);
        setRecipientEmail('');
      } else {
        trackStoryShared('link');
        setSuccess('Share link generated successfully!');
      }

      // Reload existing shares
      await loadExistingShares();

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to create share');
    } finally {
      setIsSharing(false);
    }
  };

  const copyToClipboard = async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      setSuccess('Link copied to clipboard!');
      setTimeout(() => setSuccess(null), 3000);
    } catch {
      setError('Failed to copy link to clipboard');
    }
  };

  const updateStoryVisibility = async (makePublic: boolean) => {
    setIsUpdatingVisibility(true);
    setError(null);

    try {
      const response = await fetch(`/api/stories/${storyId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          is_public: makePublic
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update story visibility');
      }

      setLocalIsPublic(makePublic);
      setSuccess(makePublic ? 'Story is now public!' : 'Story is now private!');
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to update story visibility');
    } finally {
      setIsUpdatingVisibility(false);
    }
  };

  const handleModalClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={handleModalClick}
    >
      <div className="bg-slate-800 rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-bold text-white">Share Story</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        <div className="mb-6">
          <h3 className="text-white font-medium mb-4">"{storyTitle}"</h3>
          
          {/* Story Visibility Toggle */}
          <div className="bg-slate-700 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-white font-medium">Story Visibility</span>
              <span className={`px-2 py-1 rounded text-xs font-medium ${
                localIsPublic 
                  ? 'bg-green-600/20 text-green-300' 
                  : 'bg-gray-600/20 text-gray-300'
              }`}>
                {localIsPublic ? '🌐 Public' : '🔒 Private'}
              </span>
            </div>
            <p className="text-gray-300 text-sm mb-3">
              {localIsPublic 
                ? 'This story is visible to everyone and can be shared on social media.'
                : 'This story is private. Make it public to enable Facebook sharing.'
              }
            </p>
            <button
              onClick={() => updateStoryVisibility(!localIsPublic)}
              disabled={isUpdatingVisibility}
              className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg text-sm transition-colors"
            >
              {isUpdatingVisibility 
                ? 'Updating...' 
                : localIsPublic 
                  ? 'Make Private' 
                  : 'Make Public'
              }
            </button>
          </div>
        </div>

        {error && (
          <div className="mb-4 p-3 bg-red-600/20 border border-red-600 rounded-lg">
            <p className="text-red-200 text-sm">{error}</p>
          </div>
        )}

        {success && (
          <div className="mb-4 p-3 bg-green-600/20 border border-green-600 rounded-lg">
            <p className="text-green-200 text-sm">{success}</p>
          </div>
        )}

        {/* Share Type Selection */}
        <div className="mb-6">
          <label className="block text-white font-medium mb-3">Share Method</label>
          <div className="flex flex-col gap-3">
            <label className="flex items-center">
              <input
                type="radio"
                value="link"
                checked={shareType === 'link'}
                onChange={(e) => setShareType(e.target.value as 'link')}
                className="mr-2"
              />
              <span className="text-gray-300">🔗 Generate Link</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="email"
                checked={shareType === 'email'}
                onChange={(e) => setShareType(e.target.value as 'email')}
                className="mr-2"
              />
              <span className="text-gray-300">📧 Send Email</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                value="facebook"
                checked={shareType === 'facebook'}
                onChange={(e) => setShareType(e.target.value as 'facebook')}
                className="mr-2"
                disabled={!localIsPublic}
              />
                              <span className={`${!localIsPublic ? 'text-gray-500' : 'text-gray-300'}`}>
                  📘 Share on Facebook {!localIsPublic && '(requires public story)'}
                </span>
            </label>
          </div>
        </div>

        {/* Email Input */}
        {shareType === 'email' && (
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">
              Recipient Email
            </label>
            <input
              type="email"
              value={recipientEmail}
              onChange={(e) => setRecipientEmail(e.target.value)}
              placeholder="Enter email address"
              className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
            />
          </div>
        )}

        {/* Share URL Display */}
        {shareUrl && (
          <div className="mb-6">
            <label className="block text-white font-medium mb-2">Share Link</label>
            <div className="flex gap-2">
              <input
                type="text"
                value={shareUrl}
                readOnly
                className="flex-1 px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white text-sm"
              />
              <button
                onClick={() => copyToClipboard(shareUrl)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm transition-colors"
              >
                Copy
              </button>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-3 mb-6">
          <button
            onClick={handleShare}
            disabled={isSharing}
            className="flex-1 px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
          >
            {isSharing ? 'Creating...' : shareType === 'email' ? 'Send Email' : shareType === 'facebook' ? 'Share on Facebook' : 'Generate Link'}
          </button>
          <button
            onClick={onClose}
            className="px-4 py-2 border border-slate-600 text-white rounded-lg hover:bg-slate-700 transition-colors"
          >
            Close
          </button>
        </div>

        {/* Existing Shares Toggle */}
        <div className="border-t border-slate-700 pt-4">
          <button
            onClick={() => {
              setShowExistingShares(!showExistingShares);
              if (!showExistingShares) {
                loadExistingShares();
              }
            }}
            className="text-blue-400 hover:text-blue-300 text-sm"
          >
            {showExistingShares ? 'Hide' : 'Show'} Existing Shares ({existingShares.length})
          </button>

          {showExistingShares && (
            <div className="mt-4 space-y-3">
              {existingShares.length === 0 ? (
                <p className="text-gray-400 text-sm">No existing shares</p>
              ) : (
                existingShares.map((share) => (
                  <div key={share.id} className="bg-slate-700 rounded-lg p-3">
                    <div className="flex justify-between items-start mb-2">
                      <span className="text-white text-sm font-medium">
                        {share.type === 'email' ? '📧 Email' : '🔗 Link'} Share
                      </span>
                      <span className={`text-xs px-2 py-1 rounded ${
                        share.is_expired 
                          ? 'bg-red-600/20 text-red-300' 
                          : 'bg-green-600/20 text-green-300'
                      }`}>
                        {share.is_expired ? 'Expired' : 'Active'}
                      </span>
                    </div>
                    {share.recipient_email && (
                      <p className="text-gray-300 text-sm mb-2">To: {share.recipient_email}</p>
                    )}
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={share.share_url}
                        readOnly
                        className="flex-1 px-2 py-1 bg-slate-600 border border-slate-500 rounded text-white text-xs"
                      />
                      <button
                        onClick={() => copyToClipboard(share.share_url)}
                        className="px-2 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-xs transition-colors"
                      >
                        Copy
                      </button>
                    </div>
                    <p className="text-gray-400 text-xs mt-2">
                      Created: {new Date(share.created_at).toLocaleDateString()} | 
                      Expires: {new Date(share.expires_at).toLocaleDateString()}
                    </p>
                  </div>
                ))
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 