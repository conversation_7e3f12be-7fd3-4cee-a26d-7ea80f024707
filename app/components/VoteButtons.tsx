'use client';

import { useState, useEffect, useCallback, useMemo, memo } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { storyApi, handleApiError } from '@/lib/utils/api';
import { useApiCall } from '@/app/hooks/api/useApiCall';

interface VoteData {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
  userVote: 'up' | 'down' | null;
}

interface VoteButtonsProps {
  storyId?: number;
  storyUuid?: string;
  initialVoteData?: VoteData;
  size?: 'sm' | 'md' | 'lg';
  showCounts?: boolean;
  className?: string;
}

const VoteButtons = memo(function VoteButtons({ 
  storyId, 
  storyUuid,
  initialVoteData,
  size = 'md',
  showCounts = true,
  className = ''
}: VoteButtonsProps) {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [voteData, setVoteData] = useState<VoteData>(
    initialVoteData || {
      counts: { upvotes: 0, downvotes: 0, totalVotes: 0, score: 0 },
      userVote: null
    }
  );
  
  const voteCall = useApiCall<{
    counts: {
      upvotes: number;
      downvotes: number;
      totalVotes: number;
      score: number;
    };
    voteType: 'up' | 'down' | null;
  }>();

  const fetchVoteData = useCallback(async () => {
    if (!storyId && !storyUuid) return;
    
    try {
      const data = await storyApi.getVotes(
        storyUuid || storyId!, 
        !!storyUuid
      );
      setVoteData(data);
    } catch (error) {
      console.error('Error fetching vote data:', handleApiError(error));
    }
  }, [storyId, storyUuid]);

  // Fetch vote data on component mount
  useEffect(() => {
    if (!initialVoteData) {
      fetchVoteData();
    }
  }, [fetchVoteData, initialVoteData]);

  const handleVote = useCallback(async (voteType: 'up' | 'down') => {
    if (status === 'loading') return;
    
    if (!session) {
      router.push('/sign-in');
      return;
    }

    try {
      const result = await voteCall.execute(() => 
        storyApi.vote(storyUuid || storyId!, voteType, !!storyUuid)
      );
      
      setVoteData({
        counts: result.counts,
        userVote: voteType
      });
    } catch (error) {
      console.error('Error voting:', handleApiError(error));
    }
  }, [status, session, router, storyUuid, storyId, voteCall]);

  const handleUpvote = useCallback(() => handleVote('up'), [handleVote]);
  const handleDownvote = useCallback(() => handleVote('down'), [handleVote]);

  const sizeClasses = useMemo(() => ({
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }), []);

  const buttonSizeClasses = useMemo(() => ({
    sm: 'px-3 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base'
  }), []);

  const upvoteButtonClass = useMemo(() => `
    flex items-center gap-1 rounded-lg transition-colors
    ${buttonSizeClasses[size]}
    ${voteData.userVote === 'up' 
      ? 'bg-green-600 text-white' 
      : 'bg-slate-700 text-gray-300 hover:bg-green-600/20 hover:text-green-400'
    }
    ${voteCall.loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
  `, [buttonSizeClasses, size, voteData.userVote, voteCall.loading]);

  const downvoteButtonClass = useMemo(() => `
    flex items-center gap-1 rounded-lg transition-colors
    ${buttonSizeClasses[size]}
    ${voteData.userVote === 'down' 
      ? 'bg-red-600 text-white' 
      : 'bg-slate-700 text-gray-300 hover:bg-red-600/20 hover:text-red-400'
    }
    ${voteCall.loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
  `, [buttonSizeClasses, size, voteData.userVote, voteCall.loading]);

  const scoreColor = useMemo(() => {
    if (voteData.counts.score > 0) return 'text-green-400';
    if (voteData.counts.score < 0) return 'text-red-400';
    return 'text-gray-400';
  }, [voteData.counts.score]);

  return (
    <div className={`flex items-center gap-2 ${sizeClasses[size]} ${className}`}>
      {/* Upvote Button */}
      <button
        onClick={handleUpvote}
        disabled={voteCall.loading}
        className={upvoteButtonClass}
        title="Upvote this story"
        aria-label={`Upvote story${showCounts ? ` (${voteData.counts.upvotes} upvotes)` : ''}`}
        aria-pressed={voteData.userVote === 'up'}
      >
        <svg className={`w-5 h-5 ${voteData.userVote === 'up' ? 'text-white' : 'text-green-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M5 15l7-7 7 7" />
        </svg>
        {showCounts && <span aria-hidden="true">{voteData.counts.upvotes}</span>}
      </button>

      {/* Downvote Button */}
      <button
        onClick={handleDownvote}
        disabled={voteCall.loading}
        className={downvoteButtonClass}
        title="Downvote this story"
        aria-label={`Downvote story${showCounts ? ` (${voteData.counts.downvotes} downvotes)` : ''}`}
        aria-pressed={voteData.userVote === 'down'}
      >
        <svg className={`w-5 h-5 ${voteData.userVote === 'down' ? 'text-white' : 'text-red-400'}`} fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M19 9l-7 7-7-7" />
        </svg>
        {showCounts && <span aria-hidden="true">{voteData.counts.downvotes}</span>}
      </button>

      {/* Score Display */}
      {showCounts && (
        <div className="flex items-center gap-1 text-gray-400" role="status" aria-label={`Story score: ${voteData.counts.score > 0 ? '+' : ''}${voteData.counts.score}`}>
          <span className="text-sm" aria-hidden="true">Score:</span>
          <span className={`font-semibold ${scoreColor}`} aria-hidden="true">
            {voteData.counts.score > 0 ? '+' : ''}{voteData.counts.score}
          </span>
        </div>
      )}
    </div>
  );
});

export default VoteButtons; 