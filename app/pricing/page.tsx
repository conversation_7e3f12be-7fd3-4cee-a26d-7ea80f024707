'use client'
import Image from "next/image"
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import { event } from '../components/analytics';
import CustomPricingTable from '../components/pricing/CustomPricingTable';

// Note: metadata moved to layout due to 'use client'

export default function PricingPage() {
  const handlePricingInteraction = (action: string, label?: string) => {
    event({
      action,
      category: 'pricing',
      label: label || 'pricing_page'
    });
  };
  return (
    <div className="min-h-screen">
      <Navigation currentPage="pricing" />

      {/* Hero Section */}
      <section className="relative h-72 md:h-[350px] lg:h-[420px] flex items-center justify-center overflow-hidden">
        <div className="absolute inset-0">
          <Image
            src="/images/hero.webp"
            alt="Pricing background"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/50"></div>
        </div>
        
        <div className="relative z-10 text-center max-w-7xl mx-auto px-4">
          <h1 className="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight">
            Choose Your Story Plan
          </h1>
          <p className="text-xl text-gray-200 max-w-2xl mx-auto">
            Unlock unlimited creativity with our flexible pricing plans. 
            Create magical stories that captivate young minds and inspire imagination.
          </p>
        </div>
      </section>

      {/* Pricing Introduction Section */}
      <section className="py-16 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            Choose Your Storytelling Adventure
          </h2>
          <p className="text-lg text-gray-300 max-w-2xl mx-auto">
            Simple, transparent pricing for everyone.
          </p>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="pb-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

          {/* Pricing Table */}
          <div className="mb-16">
            <div onClick={() => handlePricingInteraction('pricing_table_viewed')}>
              <CustomPricingTable 
                onPlanSelect={(planName) => {
                  handlePricingInteraction('plan_selected', planName);
                }}
              />
            </div>
          </div>
        </div>
      </section>

      {/* Why Choose MyStoryMaker Section */}
      <section className="pb-20 -mt-8 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Why Choose MyStoryMaker?</h2>
            <p className="text-gray-300 text-lg">Everything you need to create magical stories for your children</p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 text-center group hover:border-blue-500/30 transition-all duration-300">
              <div className="w-16 h-16 bg-blue-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Unlimited Stories</h3>
              <p className="text-gray-300">Create as many magical stories as your imagination allows with no limits</p>
            </div>
            
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 text-center group hover:border-purple-500/30 transition-all duration-300">
              <div className="w-16 h-16 bg-purple-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">AI-Powered</h3>
              <p className="text-gray-300">Advanced AI technology brings your stories to life with creativity and intelligence</p>
            </div>
            
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 text-center group hover:border-green-500/30 transition-all duration-300">
              <div className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg className="w-8 h-8 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-white mb-4">Child-Safe</h3>
              <p className="text-gray-300">All content is carefully curated and designed for young audiences</p>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="pt-2 pb-20 relative z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h2>
            <p className="text-gray-300 text-lg">Everything you need to know about our pricing plans</p>
          </div>
          
          <div className="space-y-6">
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 group hover:border-blue-500/30 transition-all duration-300">
              <h3 className="text-xl font-semibold text-white mb-4">
                Can I cancel my subscription anytime?
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Yes, you can cancel your subscription at any time. You'll continue to have access to your plan features until the end of your billing period.
              </p>
            </div>
            
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 group hover:border-purple-500/30 transition-all duration-300">
              <h3 className="text-xl font-semibold text-white mb-4">
                Are there any setup fees?
              </h3>
              <p className="text-gray-300 leading-relaxed">
                No, there are no setup fees or hidden costs. You only pay the subscription fee for your chosen plan.
              </p>
            </div>
            
            <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-xl p-8 group hover:border-green-500/30 transition-all duration-300">
              <h3 className="text-xl font-semibold text-white mb-4">
                Can I change my plan later?
              </h3>
              <p className="text-gray-300 leading-relaxed">
                Absolutely! You can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
} 