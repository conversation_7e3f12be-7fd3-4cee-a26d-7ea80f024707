import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '../../../auth';
import UpdatePasswordForm from '../../components/UpdatePasswordForm';
import Navigation from '../../components/navigation/Navigation';
import Footer from '../../components/Footer';

export const metadata: Metadata = {
  title: 'Update Password - MyStoryMaker',
  description: 'Update your account password securely',
};

export default async function UpdatePasswordPage() {
  const session = await auth();

  if (!session) {
    redirect('/sign-in');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="password" />
      
      <main className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-yellow-500/20 rounded-xl">
              <svg className="w-8 h-8 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
              </svg>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white whitespace-nowrap">
              Update Password
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Keep your account secure by updating your password regularly with a strong, unique combination
          </p>
        </div>

        {/* Password Update Form */}
        <div className="max-w-4xl mx-auto">
          <UpdatePasswordForm />
        </div>
      </main>

      <Footer />
    </div>
  );
} 