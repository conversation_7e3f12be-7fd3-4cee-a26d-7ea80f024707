import { Metadata } from 'next';
import { redirect } from 'next/navigation';
import { auth } from '../../../auth';
import SubscriptionManager from '../../components/SubscriptionManager';
import Navigation from '../../components/navigation/Navigation';
import Footer from '../../components/Footer';

export const metadata: Metadata = {
  title: 'Subscription - MyStoryMaker',
  description: 'Manage your subscription and billing settings',
};

export default async function SubscriptionPage() {
  const session = await auth();

  if (!session) {
    redirect('/sign-in');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="subscription" />
      
      <main className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-3 mb-4">
            <div className="p-3 bg-emerald-500/20 rounded-xl">
              <svg className="w-8 h-8 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white whitespace-nowrap">
              Your Subscription
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Manage your subscription plan, billing settings, and unlock premium features for unlimited creativity
          </p>
        </div>

        {/* Subscription Management */}
        <div className="max-w-4xl mx-auto">
          <SubscriptionManager />
        </div>
      </main>

      <Footer />
    </div>
  );
} 