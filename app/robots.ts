import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  const baseUrl = 'https://mystorymaker.app'
  
  return {
    rules: [
      {
        userAgent: '*',
        allow: [
          '/',
          '/about',
          '/pricing',
          '/contact',
          '/terms',
          '/privacy',
          '/public/stories',
          '/stories/*'
        ],
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/sso-callback',
          '/my-stories',
          '/create',
          '/profile',
          '/stats',
          '/tags',
          '/account/*',
          '/sign-in',
          '/sign-up',
          '/forgot-password'
        ],
        crawlDelay: 1
      },
      {
        userAgent: 'Googlebot',
        allow: [
          '/',
          '/about',
          '/pricing',
          '/contact',
          '/terms',
          '/privacy',
          '/public/stories',
          '/stories/*'
        ],
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/sso-callback',
          '/my-stories',
          '/create',
          '/profile',
          '/stats',
          '/tags',
          '/account/*',
          '/sign-in',
          '/sign-up',
          '/forgot-password'
        ]
      }
    ],
    sitemap: `${baseUrl}/sitemap.xml`,
    host: baseUrl
  }
} 