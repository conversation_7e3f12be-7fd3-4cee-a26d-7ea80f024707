import { MetadataRoute } from 'next'
import { DatabaseService } from '../lib/services/databaseService'

interface PublicStory {
  story_uuid: string;
  title: string;
  created_at: string;
  updated_at?: string;
}

// Force dynamic generation - don't cache the sitemap
export const dynamic = 'force-dynamic'
export const revalidate = 0

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://mystorymaker.app'
  
  // Static pages
  const staticPages = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 1,
    },
    {
      url: `${baseUrl}/about`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.8,
    },
    {
      url: `${baseUrl}/pricing`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/public/stories`,
      lastModified: new Date(),
      changeFrequency: 'daily' as const,
      priority: 0.9,
    },
    {
      url: `${baseUrl}/contact`,
      lastModified: new Date(),
      changeFrequency: 'monthly' as const,
      priority: 0.7,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'yearly' as const,
      priority: 0.3,
    },
  ]

  // Dynamic pages - public stories
  let publicStories: PublicStory[] = []
  try {
    const databaseService = new DatabaseService()
    publicStories = await databaseService.getPublicStoriesForSitemap()
  } catch (error) {
    console.error('Sitemap: Error fetching public stories:', error)
    // Continue with empty stories array if database query fails
  }

  const storyPages = publicStories.map((story) => ({
    url: `${baseUrl}/stories/${story.story_uuid}/${createSlug(story.title)}`,
    lastModified: new Date(story.updated_at || story.created_at),
    changeFrequency: 'weekly' as const,
    priority: 0.6,
  }))
  
  return [...staticPages, ...storyPages]
}

// Helper function to create URL-friendly slug
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .trim()
} 