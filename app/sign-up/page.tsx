'use client';

import { useState, useEffect } from 'react';
import { signIn, getProviders } from 'next-auth/react';
import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import Image from 'next/image';

// Define proper types for NextAuth providers
interface AuthProvider {
  id: string;
  name: string;
  type: string;
  signinUrl: string;
  callbackUrl: string;
}

type ProvidersMap = Record<string, AuthProvider> | null;

export default function SignUpPage() {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [name, setName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [agreeToTerms, setAgreeToTerms] = useState(false);
  const [providers, setProviders] = useState<ProvidersMap>(null);
  const searchParams = useSearchParams();
  const [redirectUrl, setRedirectUrl] = useState<string>('/my-stories');

  useEffect(() => {
    // Check for redirect URL in search params
    const redirect = searchParams.get('redirect_url');
    if (redirect) {
      setRedirectUrl(redirect);
    }

    // Fetch available providers
    const fetchAuthData = async () => {
      try {
        const providersRes = await getProviders();
        setProviders(providersRes);
      } catch (error) {
        console.error('Failed to fetch auth data:', error);
      }
    };

    fetchAuthData();
  }, [searchParams]);

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) return;
    if (!agreeToTerms) {
      setError('You must agree to the Terms of Service and Privacy Policy to continue.');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Password registration
      const registerResponse = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email,
          password,
          name: name || null,
          agreeToTerms,
        }),
      });

      const registerData = await registerResponse.json();

      if (!registerResponse.ok) {
        setError(registerData.error || 'Registration failed. Please try again.');
        return;
      }

      // Registration successful, redirect to sign-in with verification message
      const signInUrl = new URL('/sign-in', window.location.origin);
      signInUrl.searchParams.set('message', registerData.message || 'Account created! Please check your email to verify your account.');
      if (redirectUrl !== '/my-stories') {
        signInUrl.searchParams.set('redirect_url', redirectUrl);
      }
      
      window.location.href = signInUrl.toString();
    } catch {
      setError('An error occurred. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleOAuthSignIn = async (providerId: string) => {
    try {
      await signIn(providerId, { 
        callbackUrl: redirectUrl,
        redirect: true 
      });
    } catch (error) {
      console.error('OAuth sign-in error:', error);
      setError('Social sign-in failed. Please try again.');
    }
  };

  // Get redirect URL to preserve it in sign-in link
  const getSignInUrl = () => {
    const redirect = searchParams.get('redirect_url');
    return redirect ? `/sign-in?redirect_url=${encodeURIComponent(redirect)}` : '/sign-in';
  };

  return (
    <div className="min-h-screen">
      <Navigation />
      
      <div className="relative flex items-center justify-center min-h-[calc(100vh-280px)] py-12 px-6">
        {/* Hero Background */}
        <div className="absolute inset-0">
          <Image
            src="/images/hero.webp"
            alt="Magical story background"
            fill
            className="object-cover"
            priority
            sizes="100vw"
          />
          <div className="absolute inset-0 bg-black/60"></div>
        </div>

        <div className="relative z-10 max-w-md w-full">
          <div className="bg-slate-800/90 backdrop-blur-sm p-6 sm:p-8 rounded-2xl shadow-2xl border border-slate-700">
            <div className="text-center mb-6 sm:mb-8">
              <h1 className="text-2xl sm:text-3xl font-bold text-white mb-2">Join MyStoryMaker</h1>
              <p className="text-gray-300">Create your account and start your storytelling journey</p>
            </div>

            {error && (
              <div className="mb-4 sm:mb-6 p-3 sm:p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-start gap-3">
                  <div className="flex-shrink-0">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5 text-red-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <p className="text-red-800 text-xs sm:text-sm font-medium">{error}</p>
                </div>
              </div>
            )}

            <form onSubmit={handleFormSubmit} className="space-y-4 sm:space-y-6">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-white mb-1 sm:mb-2">
                  Full Name (Optional)
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 text-sm sm:text-base"
                  placeholder="Enter your full name"
                />
              </div>

              <div>
                <label htmlFor="email" className="block text-sm font-medium text-white mb-1 sm:mb-2">
                  Email Address
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 text-sm sm:text-base"
                  placeholder="Enter your email"
                />
              </div>

              <div>
                <label htmlFor="password" className="block text-sm font-medium text-white mb-1 sm:mb-2">
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 text-sm sm:text-base"
                  placeholder="Create a strong password"
                />
              </div>

              <div className="flex items-start gap-3">
                <input
                  id="agree-terms"
                  type="checkbox"
                  checked={agreeToTerms}
                  onChange={(e) => setAgreeToTerms(e.target.checked)}
                  className="mt-1 w-4 h-4 text-blue-600 bg-slate-700 border-slate-600 rounded focus:ring-blue-500 focus:ring-2"
                />
                <label htmlFor="agree-terms" className="text-sm text-gray-300">
                  I agree to the{' '}
                  <Link href="/terms" className="text-blue-400 hover:text-blue-300 underline">
                    Terms of Service
                  </Link>{' '}
                  and{' '}
                  <Link href="/privacy" className="text-blue-400 hover:text-blue-300 underline">
                    Privacy Policy
                  </Link>
                </label>
              </div>

              <button
                type="submit"
                disabled={isLoading || !agreeToTerms}
                className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white py-2.5 sm:py-3 px-4 rounded-lg font-semibold transition-colors text-sm sm:text-base"
              >
                {isLoading ? 'Creating Account...' : 'Create Account'}
              </button>
            </form>

            <div className="mt-4 sm:mt-6">
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-slate-600"></div>
                </div>
                <div className="relative flex justify-center text-sm">
                  <span className="px-2 bg-slate-800 text-gray-400">Or continue with</span>
                </div>
              </div>

              <div className="mt-4">
                {providers && Object.values(providers).filter((p: AuthProvider) => p.id !== 'credentials').length > 1 ? (
                  // Multiple providers - show side by side
                  <div className="grid grid-cols-2 gap-3">
                    {Object.values(providers).map((provider: AuthProvider) => {
                      // Skip credentials provider (password auth)
                      if (provider.id === 'credentials') return null;
                      
                      return (
                        <button
                          key={provider.name}
                          type="button"
                          onClick={() => handleOAuthSignIn(provider.id)}
                          className="flex items-center justify-center gap-2 bg-white hover:bg-gray-50 text-gray-900 py-2.5 px-3 rounded-lg font-medium transition-colors border border-gray-300 text-sm"
                        >
                          {provider.id === 'google' && (
                            <svg className="w-4 h-4" viewBox="0 0 24 24">
                              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                          )}
                          {provider.id === 'facebook' && (
                            <svg className="w-4 h-4" fill="#1877F2" viewBox="0 0 24 24">
                              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                          )}
                          <span className="hidden sm:inline">{provider.name}</span>
                          <span className="sm:hidden">{provider.name.charAt(0)}</span>
                        </button>
                      );
                    })}
                  </div>
                ) : (
                  // Single provider - show full width
                  <div className="grid grid-cols-1 gap-3">
                    {providers && Object.values(providers).map((provider: AuthProvider) => {
                      // Skip credentials provider (password auth)
                      if (provider.id === 'credentials') return null;
                      
                      return (
                        <button
                          key={provider.name}
                          type="button"
                          onClick={() => handleOAuthSignIn(provider.id)}
                          className="w-full flex items-center justify-center gap-3 bg-white hover:bg-gray-50 text-gray-900 py-2.5 px-4 rounded-lg font-medium transition-colors border border-gray-300"
                        >
                          {provider.id === 'google' && (
                            <svg className="w-5 h-5" viewBox="0 0 24 24">
                              <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                              <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                              <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                              <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                            </svg>
                          )}
                          {provider.id === 'facebook' && (
                            <svg className="w-5 h-5" fill="#1877F2" viewBox="0 0 24 24">
                              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                            </svg>
                          )}
                          Sign up with {provider.name}
                        </button>
                      );
                    })}
                  </div>
                )}
                
                {providers && Object.values(providers).filter((p: AuthProvider) => p.id !== 'credentials').length === 0 && (
                  <div className="text-center text-gray-400 text-sm">
                    <p>Configure OAuth providers in environment variables to enable social login.</p>
                  </div>
                )}
              </div>
            </div>

            <div className="mt-4 sm:mt-6 text-center">
              <p className="text-gray-400 text-sm">
                Already have an account?{' '}
                <Link href={getSignInUrl()} className="text-blue-400 hover:text-blue-300 font-medium">
                  Sign in
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
} 