export default function CompletionMessage() {
  return (
    <div className="text-center">
      <div className="mb-8">
        <div className="mx-auto w-16 h-16 bg-green-600 rounded-full flex items-center justify-center mb-4">
          <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h1 className="text-3xl font-bold text-white mb-2">Welcome to MyStoryMaker! 🎉</h1>
        <p className="text-gray-400">Your account has been created successfully</p>
      </div>

      <div className="bg-slate-800 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold text-white mb-4">What's Next?</h2>
        <div className="space-y-3 text-left">
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-sm font-bold">1</span>
            </div>
            <div>
              <h3 className="text-white font-medium">Create Your First Story</h3>
              <p className="text-gray-400 text-sm">Start crafting magical tales for your children</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-sm font-bold">2</span>
            </div>
            <div>
              <h3 className="text-white font-medium">Customize Characters</h3>
              <p className="text-gray-400 text-sm">Add personal touches to make stories unique</p>
            </div>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
              <span className="text-white text-sm font-bold">3</span>
            </div>
            <div>
              <h3 className="text-white font-medium">Share & Enjoy</h3>
              <p className="text-gray-400 text-sm">Share your stories with family and friends</p>
            </div>
          </div>
        </div>
      </div>

      <p className="text-gray-400 text-sm">
        Redirecting you to your story dashboard...
      </p>
    </div>
  );
} 