
import { useFormState } from '../../hooks/form/useFormState';
import ErrorMessage from '../../components/ui/ErrorMessage';

interface VerificationFormProps {
  onSubmit: (e: React.FormEvent) => Promise<void>;
  verificationCode: string;
  onVerificationCodeChange: (code: string) => void;
}

export default function VerificationForm({ 
  onSubmit, 
  verificationCode, 
  onVerificationCodeChange 
}: VerificationFormProps) {
  const { isLoading, error, setLoading, setError } = useFormState();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    
    try {
      await onSubmit(e);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Verification failed');
    }
  };

  return (
    <>
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-white mb-2">Check Your Email</h1>
        <p className="text-gray-400">We've sent a verification code to your email address</p>
      </div>

      {error && <ErrorMessage message={error} className="mb-6" />}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <label htmlFor="verificationCode" className="block text-sm font-medium text-white mb-2">
            Verification Code
          </label>
          <input
            id="verificationCode"
            type="text"
            value={verificationCode}
            onChange={(e) => onVerificationCodeChange(e.target.value)}
            required
            className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 text-center text-lg tracking-widest"
            placeholder="Enter 6-digit code"
            maxLength={6}
          />
        </div>

        <button
          type="submit"
          disabled={isLoading}
          className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
        >
          {isLoading ? 'Verifying...' : 'Verify Email'}
        </button>
      </form>

      <div className="mt-6 text-center">
        <p className="text-gray-400 text-sm">
          Didn't receive the code?{' '}
          <button className="text-blue-400 hover:text-blue-300 underline">
            Resend verification email
          </button>
        </p>
      </div>
    </>
  );
} 