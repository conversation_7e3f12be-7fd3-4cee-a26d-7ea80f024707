export interface StoryFormData {
  mainCharacter: string;
  setting: string;
  themes: string[];
  storyDetails: string;
  ageRange: string;
  tags: string[];
}

export interface Theme {
  id: string;
  name: string;
  description?: string;
  ideas?: string;
}

export interface AgeRange {
  id: string;
  range: string;
}

export interface FormState {
  formData: StoryFormData;
  tagInput: string;
  themes: Theme[];
  ageRanges: AgeRange[];
  isLoading: boolean;
  isLoadingData: boolean;
}

export interface UserPreferences {
  primaryTheme?: string;
  secondaryTheme?: string;
  preferredAgeRange?: string;
}

export interface SubscriptionLimits {
  canCreate: boolean;
  limit: number;
  planName: string;
}