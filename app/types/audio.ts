export interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
}

export interface TranscriptionResult {
  text: string;
  words: WordTimestamp[];
  id: string;
}

export type TranscriptionStatus = 'not_started' | 'generating' | 'complete' | 'failed';

export interface AudioPlayerState {
  isPlaying: boolean;
  isGenerating: boolean;
  isTranscribing: boolean;
  hasAudio: boolean;
  audioUrl: string | null;
  error: string | null;
  currentTime: number;
  currentWordIndex: number;
}

export interface TranscriptionState {
  transcription: TranscriptionResult | null;
  status: TranscriptionStatus;
}

export interface AudioPlayerWithTranscriptionProps {
  storyId: string;
  storyText: string;
  hasExistingAudio?: boolean;
  shareToken?: string;
}