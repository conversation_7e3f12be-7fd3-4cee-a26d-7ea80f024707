export interface ImageData {
  id: number;
  storage_path: string;
  default: boolean;
  original_prompt: string | null;
  prompt: string | null;
  created_at: string;
  url: string;
}

export interface ImageLimits {
  canRegenerate: boolean;
  currentCount: number;
  limit: number;
  planName: string;
  remaining: number;
}

export interface PictureMagicModalProps {
  storyUuid: string;
  storyTitle: string;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export interface ImageModalState {
  images: ImageData[];
  limits: ImageLimits | null;
  userPrompt: string;
  isLoading: boolean;
  isLoadingImages: boolean;
  error: string | null;
  isRefreshingPlan: boolean;
}