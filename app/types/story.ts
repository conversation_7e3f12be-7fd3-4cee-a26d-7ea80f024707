export interface Story {
  id: number;
  story_uuid: string;
  title: string;
  content: string;
  main_character: string;
  setting: string;
  created_at: string;
  themes: string[];
  tags: string[];
  ageRange: string;
  imageUrl: string | null;
  slug: string;
  is_public: boolean;
  hasAudio: boolean;
}

export interface MyStoriesClientProps {
  initialStories: Story[];
}

export interface DeleteDialogState {
  isOpen: boolean;
  storyUuid: string | null;
  storyTitle: string;
  isDeleting: boolean;
}

export interface StoryManagementState {
  stories: Story[];
  deleteDialog: DeleteDialogState;
}