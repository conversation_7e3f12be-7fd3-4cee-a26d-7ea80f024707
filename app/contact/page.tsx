'use client'

import { useState } from 'react';
import Script from 'next/script';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import { contactApi, handleApiError } from '@/lib/utils/api';
import { useApiCall } from '@/app/hooks/api/useApiCall';

interface TurnstileWindow extends Window {
  turnstile?: {
    reset: () => void;
  };
  onTurnstileCallback?: (token: string) => void;
}

declare let window: TurnstileWindow;

export default function ContactPage() {
  const [formData, setFormData] = useState({
    fullName: '',
    email: '',
    subject: '',
    message: ''
  });
  const [submitStatus, setSubmitStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [turnstileToken, setTurnstileToken] = useState<string | null>(null);
  
  const contactCall = useApiCall();

  const siteKey = process.env.NEXT_PUBLIC_TURNSTILE_SITE_KEY;
  


  // Fallback for development if no key is set
  const isDevelopment = process.env.NODE_ENV === 'development';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!turnstileToken) {
      setSubmitStatus('error');
      setErrorMessage('Please complete the security verification before submitting.');
      return;
    }

    setSubmitStatus('idle');
    setErrorMessage('');

    try {
      await contactCall.execute(() => 
        contactApi.sendMessage({
          ...formData,
          turnstileToken,
        })
      );

      setSubmitStatus('success');
      setFormData({ fullName: '', email: '', subject: '', message: '' });
      setTurnstileToken(null);
      // Reset Turnstile widget
      if (typeof window !== 'undefined' && window.turnstile) {
        window.turnstile.reset();
      }
      setErrorMessage('');
    } catch (error) {
      setSubmitStatus('error');
      setErrorMessage(handleApiError(error));
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="contact" />

      {/* Cloudflare Turnstile Script */}
      <Script
        src="https://challenges.cloudflare.com/turnstile/v0/api.js"
        strategy="lazyOnload"
        onLoad={() => {
          if (typeof window !== 'undefined') {
            window.onTurnstileCallback = (token: string) => {
              setTurnstileToken(token);
              if (submitStatus === 'error' && errorMessage.includes('security verification')) {
                setSubmitStatus('idle');
                setErrorMessage('');
              }
            };
          }
        }}
      />

      {/* Main Content */}
      <div className="relative z-10 max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-slate-800/50 rounded-2xl p-8 md:p-12">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-4">Contact Us</h1>
          <p className="text-gray-400 mb-8">Fill out the form below to send us a message</p>

          {submitStatus === 'success' && (
            <div className="mb-6 p-4 bg-green-600/20 border border-green-600/30 rounded-lg">
              <p className="text-green-400">Thank you for your message! We'll get back to you soon.</p>
            </div>
          )}

          {submitStatus === 'error' && (
            <div className="mb-6 p-4 bg-red-600/20 border border-red-600/30 rounded-lg">
              <p className="text-red-400">{errorMessage}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Full Name */}
            <div>
              <label htmlFor="fullName" className="block text-white font-medium mb-2">
                Full Name <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                id="fullName"
                name="fullName"
                value={formData.fullName}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your full name"
              />
            </div>

            {/* Email Address */}
            <div>
              <label htmlFor="email" className="block text-white font-medium mb-2">
                Email Address <span className="text-red-400">*</span>
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your email address"
              />
            </div>

            {/* Subject */}
            <div>
              <label htmlFor="subject" className="block text-white font-medium mb-2">
                Subject <span className="text-red-400">*</span>
              </label>
              <input
                type="text"
                id="subject"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                required
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Enter your subject"
              />
            </div>

            {/* Message */}
            <div>
              <label htmlFor="message" className="block text-white font-medium mb-2">
                Message <span className="text-red-400">*</span>
              </label>
              <textarea
                id="message"
                name="message"
                value={formData.message}
                onChange={handleChange}
                required
                rows={6}
                className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                placeholder="Enter your message"
              />
            </div>

            {/* Cloudflare Turnstile */}
            {siteKey && (
              <div className="flex justify-center">
                <div
                  className="cf-turnstile"
                  data-sitekey={siteKey}
                  data-callback="onTurnstileCallback"
                  data-theme="dark"
                ></div>
              </div>
            )}

            {/* Development mode notice */}
            {!siteKey && isDevelopment && (
              <div className="text-yellow-400 text-sm text-center p-3 bg-yellow-900/20 border border-yellow-600/30 rounded-lg">
                <p>Development mode: Security verification disabled</p>
              </div>
            )}

            {/* Submit Button */}
            <button
              type="submit"
              disabled={contactCall.loading || (!turnstileToken && !!siteKey) || (!siteKey && !isDevelopment)}
              className="w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-600/50 text-white font-semibold py-3 px-6 rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
            >
              {contactCall.loading ? 'Sending Message...' : 'Send Message'}
            </button>

            {!siteKey && !isDevelopment && (
              <div className="text-red-400 text-sm text-center p-4 bg-red-900/20 border border-red-600/30 rounded-lg">
                <p className="font-semibold mb-2">Security verification not configured</p>
                <p className="text-xs">
                  Environment: {process.env.NODE_ENV}<br />
                  Site key available: {siteKey ? 'Yes' : 'No'}<br />
                  Please configure NEXT_PUBLIC_TURNSTILE_SITE_KEY in production.
                </p>
              </div>
            )}
          </form>
        </div>
      </div>

      <Footer />
    </div>
  );
}