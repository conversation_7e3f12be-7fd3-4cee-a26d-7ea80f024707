'use client';

import Link from 'next/link';
import { useSubscription } from '../../hooks/useSubscription';
import { getDaysUntilReset, formatResetDate, getNextMonthResetDate } from '../../../lib/utils/dateUtils';
import { event } from '../../components/analytics';
import { InlineLoadingSpinner } from '../../components/ui/LoadingSpinner';

export default function SubscriptionStatus() {
  const { subscriptionData, isLoading } = useSubscription();

  if (isLoading || !subscriptionData) {
    return <InlineLoadingSpinner />;
  }

  const progressPercentage = (subscriptionData.currentCount / subscriptionData.limit) * 100;
  const isNearLimit = progressPercentage >= 80;
  const isAtLimit = !subscriptionData.canCreate;
  const daysUntilReset = getDaysUntilReset();

  const handleUpgradeClick = () => {
    event({
      action: 'upgrade_button_click',
      category: 'conversion',
      label: `subscription_status_${subscriptionData.planName.toLowerCase()}`
    });
  };
  const resetDate = getNextMonthResetDate();

  return (
    <div className={`bg-slate-800/50 backdrop-blur-sm rounded-lg p-4 border ${
      isAtLimit ? 'border-amber-500/50' : isNearLimit ? 'border-yellow-500/50' : 'border-slate-700'
    }`}>
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-4">
          <div>
            <span className="text-white font-medium">{subscriptionData.planName} Plan</span>
            <span className="text-gray-400 text-sm ml-3">
              {subscriptionData.currentCount}/{subscriptionData.limit} stories used
            </span>
          </div>
          <div className="flex-1 max-w-32">
            <div className="w-full bg-slate-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-500 ${
                  isAtLimit ? 'bg-amber-500' : isNearLimit ? 'bg-yellow-500' : 'bg-blue-500'
                }`}
                style={{ width: `${Math.min(progressPercentage, 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
        {(isAtLimit || subscriptionData.planName === 'Free') && (
          <Link href="/pricing">
            <button 
              onClick={handleUpgradeClick}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded text-sm font-medium transition-colors"
            >
              Upgrade
            </button>
          </Link>
        )}
      </div>

      <div className="text-sm text-gray-300">
        {isAtLimit ? (
          <span>All stories used this month! Resets {formatResetDate(resetDate)} ({daysUntilReset} {daysUntilReset === 1 ? 'day' : 'days'} remaining)</span>
        ) : (
          <span>{subscriptionData.remainingStories} {subscriptionData.remainingStories === 1 ? 'story' : 'stories'} remaining this month</span>
        )}
      </div>
    </div>
  );
}