import { redirect } from 'next/navigation';
import { Metadata } from 'next';
import { auth } from '../../auth';
import { Suspense } from 'react';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import { getSafeImageUrl } from '../../lib/r2-storage-utils';
import { DatabaseService } from '../../lib/services/databaseService';
import MyStoriesClient from './MyStoriesClient';
import { PageLoadingSpinner } from '../components/ui/LoadingSpinner';

export const metadata: Metadata = {
  title: 'My Stories - MyStoryMaker',
  description: 'View and manage your personalized children\'s stories. Edit, share, and organize all your magical tales in one place.',
};

interface Story {
  id: number;
  story_uuid: string;
  title: string;
  content: string;
  main_character: string;
  setting: string;
  created_at: string;
  themes: string[];
  tags: string[];
  ageRange: string;
  imageUrl: string | null;
  slug: string;
  is_public: boolean;
  hasAudio: boolean;
}

// Function to create URL-friendly slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Fetch user's stories from PostgreSQL database (server-side) - Optimized version
async function getUserStories(userId: string): Promise<Story[]> {
  try {
    const databaseService = new DatabaseService();
    const stories = await databaseService.getUserStoriesOptimized(userId);

    if (!stories || stories.length === 0) return [];

    // Process stories and add image URLs and slugs
    const processedStories: Story[] = stories.map((story) => {
      // Extract theme descriptions for display
      const themes = story.stories_themes?.map((st: { themes: { description: string } }) => st.themes.description) || [];
      
      // Extract tag names for display
      const tags = story.story_tags?.map((st: { tags: { name: string } }) => st.tags.name) || [];

      // Get default image URL from the joined data
      let imageUrl = null;
      const defaultImage = story.images?.find((img: { default: boolean; updated_at: string; storage_path: string }) => img.default === true);
      if (defaultImage) {
        const timestamp = new Date(defaultImage.updated_at).getTime().toString();
        // Use safe UUID-based endpoint that doesn't expose user IDs
        imageUrl = getSafeImageUrl(story.story_uuid, timestamp);
      }

      return {
        id: story.id,
        story_uuid: story.story_uuid,
        title: story.title,
        content: story.content,
        main_character: story.main_character,
        setting: story.setting,
        created_at: story.created_at,
        themes: themes,
        tags: tags,
        ageRange: story.age_ranges?.range || 'Unknown',
        imageUrl: imageUrl,
        slug: createSlug(story.title),
        is_public: story.is_public || false,
        hasAudio: story.hasAudio || false
      };
    });

    return processedStories;
  } catch (error) {
    console.error('Error fetching user stories:', error);
    return [];
  }
}

// Separate component for stories content to enable Suspense
async function StoriesContent() {
  const session = await auth();

  if (!session?.user?.id) {
    redirect('/');
  }

  const stories = await getUserStories(session.user.id);
  return <MyStoriesClient initialStories={stories} />;
}

export default async function MyStoriesPage() {
  // Check if user is authenticated (server-side)
  const session = await auth();

  if (!session?.user?.id) {
    redirect('/');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="my-stories" />
      <div className="relative z-10">
        <Suspense fallback={
          <div className="max-w-7xl mx-auto px-4">
            <PageLoadingSpinner message="Loading your stories..." />
          </div>
        }>
          <StoriesContent />
        </Suspense>
      </div>
      <Footer />
    </div>
  );
}