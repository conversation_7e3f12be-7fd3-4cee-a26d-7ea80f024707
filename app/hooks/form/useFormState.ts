import { useState } from 'react';

export interface FormState {
  isLoading: boolean;
  error: string | null;
  success: string | null;
}

export function useFormState(initialState?: Partial<FormState>) {
  const [isLoading, setIsLoading] = useState(initialState?.isLoading ?? false);
  const [error, setError] = useState<string | null>(initialState?.error ?? null);
  const [success, setSuccess] = useState<string | null>(initialState?.success ?? null);

  const setLoading = (loading: boolean) => {
    setIsLoading(loading);
    if (loading) {
      setError(null);
      setSuccess(null);
    }
  };

  const setErrorMessage = (message: string | null) => {
    setError(message);
    setIsLoading(false);
  };

  const setSuccessMessage = (message: string | null) => {
    setSuccess(message);
    setIsLoading(false);
  };

  const reset = () => {
    setIsLoading(false);
    setError(null);
    setSuccess(null);
  };

  return {
    isLoading,
    error,
    success,
    setLoading,
    setError: setErrorMessage,
    setSuccess: setSuccessMessage,
    reset,
  };
} 