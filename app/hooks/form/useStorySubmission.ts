import { useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { StoryFormData, SubscriptionLimits } from '../../types/form';
import { trackStoryCreated, event } from '../../components/analytics';

export function useStorySubmission() {
  const { data: session } = useSession();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const createSlug = useCallback((title: string): string => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();
  }, []);

  const checkSubscriptionLimits = useCallback(async (): Promise<boolean> => {
    try {
      const limitsResponse = await fetch('/api/subscription/limits');
      if (limitsResponse.ok) {
        const limitsData: SubscriptionLimits = await limitsResponse.json();
        if (!limitsData.canCreate) {
          const shouldUpgrade = confirm(
            `Wow! You've created ${limitsData.limit} amazing stories this month on your ${limitsData.planName} plan!\n\nYour creative powers will refresh next month, or you can unlock unlimited storytelling right now!\n\nWould you like to explore our upgrade options?`
          );
          
          // Track story limit reached
          event({
            action: 'story_limit_reached',
            category: 'conversion',
            label: limitsData.planName || 'unknown'
          });
          
          if (shouldUpgrade) {
            event({
              action: 'upgrade_prompted',
              category: 'conversion',
              label: 'story_limit'
            });
            router.push('/pricing');
          }
          return false;
        }
      }
      return true;
    } catch (error) {
      console.error('Error checking subscription limits:', error);
      return true; // Allow submission if check fails
    }
  }, [router]);

  const submitStory = useCallback(async (formData: StoryFormData): Promise<void> => {
    if (!session) {
      router.push('/sign-in');
      return;
    }

    if (!formData.mainCharacter || !formData.setting || !formData.storyDetails || !formData.ageRange) {
      alert('Please fill in all required fields');
      return;
    }

    setIsLoading(true);

    try {
      const canSubmit = await checkSubscriptionLimits();
      if (!canSubmit) {
        setIsLoading(false);
        return;
      }

      const requestBody = {
        main_character: formData.mainCharacter,
        setting: formData.setting,
        themes: formData.themes,
        story_details: formData.storyDetails,
        age_range: formData.ageRange,
        tags: formData.tags
      };

      const response = await fetch('/api/stories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorData = await response.json();
        
        if (response.status === 403 && errorData.error === 'Story limit reached') {
          // Handle subscription limit error
          const details = errorData.details;
          const message = details?.message || 'You have reached your monthly story limit.';
          
          const shouldUpgrade = confirm(
            `${message}\n\nWould you like to explore our upgrade options?`
          );
          
          // Track subscription limit error
          event({
            action: 'subscription_limit_error',
            category: 'conversion',
            label: 'story_creation'
          });
          
          if (shouldUpgrade) {
            event({
              action: 'upgrade_prompted',
              category: 'conversion',
              label: 'subscription_limit'
            });
            router.push('/pricing');
          }
          return;
        }
        
        throw new Error(errorData.error || 'Failed to create story');
      }

      const result = await response.json();
      const storySlug = createSlug(result.title || 'untitled-story');
      
      // Track story creation success
      trackStoryCreated(formData.themes.join(', ') || 'unknown');
      
      // Track additional story creation details
      event({
        action: 'story_created_detailed',
        category: 'content_creation',
        label: `age_range_${formData.ageRange}`,
        value: formData.tags.length
      });
      
      router.push(`/stories/${result.uuid}/${storySlug}`);
    } catch (error) {
      console.error('Error creating story:', error);
      alert('Failed to create story. Please try again.');
    } finally {
      setIsLoading(false);
    }
  }, [session, router, checkSubscriptionLimits, createSlug]);

  return {
    isLoading,
    submitStory,
  };
}