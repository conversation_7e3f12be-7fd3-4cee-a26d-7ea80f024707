import { useState, useEffect, useCallback } from 'react';
import { Theme, AgeRange, UserPreferences, StoryFormData } from '../../types/form';

export function useFormDataLoader() {
  const [themes, setThemes] = useState<Theme[]>([]);
  const [ageRanges, setAgeRanges] = useState<AgeRange[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  const loadUserPreferences = useCallback(async (themesData: Theme[]): Promise<Partial<StoryFormData>> => {
    try {
      const preferencesResponse = await fetch('/api/user/preferences');
      if (!preferencesResponse.ok || themesData.length === 0) {
        return {};
      }

      const userPrefs: UserPreferences = await preferencesResponse.json();
      const preferredThemes: string[] = [];
      
      // Map descriptions to theme names
      if (userPrefs.primaryTheme) {
        const primaryTheme = themesData.find((theme: Theme) => 
          theme.description === userPrefs.primaryTheme ||
          (theme.description && userPrefs.primaryTheme && theme.description.toLowerCase() === userPrefs.primaryTheme.toLowerCase()) ||
          (theme.name && userPrefs.primaryTheme && theme.name.toLowerCase() === userPrefs.primaryTheme.toLowerCase())
        );
        
        if (primaryTheme) {
          preferredThemes.push(primaryTheme.name);
        }
      }
      
      if (userPrefs.secondaryTheme && userPrefs.secondaryTheme !== userPrefs.primaryTheme) {
        const secondaryTheme = themesData.find((theme: Theme) => 
          theme.description === userPrefs.secondaryTheme ||
          (theme.description && userPrefs.secondaryTheme && theme.description.toLowerCase() === userPrefs.secondaryTheme.toLowerCase()) ||
          (theme.name && userPrefs.secondaryTheme && theme.name.toLowerCase() === userPrefs.secondaryTheme.toLowerCase())
        );
        
        if (secondaryTheme) {
          preferredThemes.push(secondaryTheme.name);
        }
      }

      return {
        themes: preferredThemes,
        ageRange: userPrefs.preferredAgeRange || ''
      };
    } catch (error) {
      console.error('Error loading user preferences:', error);
      return {};
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [themesResponse, ageRangesResponse] = await Promise.all([
          fetch('/api/themes'),
          fetch('/api/age-ranges')
        ]);

        let themesData: Theme[] = [];
        let ageRangesData: AgeRange[] = [];

        if (themesResponse.ok) {
          themesData = await themesResponse.json();
          setThemes(themesData);
        }

        if (ageRangesResponse.ok) {
          ageRangesData = await ageRangesResponse.json();
          setAgeRanges(ageRangesData);
        }

        return themesData;
      } catch (error) {
        console.error('Error fetching data:', error);
        return [];
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchData();
  }, []);

  return {
    themes,
    ageRanges,
    isLoadingData,
    loadUserPreferences,
  };
}