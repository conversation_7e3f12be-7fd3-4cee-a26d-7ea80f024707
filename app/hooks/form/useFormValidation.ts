import { useCallback } from 'react';
import { StoryFormData } from '../../types/form';

export function useFormValidation() {
  const validateFormData = useCallback((formData: StoryFormData): string[] => {
    const errors: string[] = [];

    if (!formData.mainCharacter.trim()) {
      errors.push('Main character is required');
    }

    if (!formData.setting.trim()) {
      errors.push('Setting is required');
    }

    if (!formData.storyDetails.trim()) {
      errors.push('Story details are required');
    }

    if (formData.storyDetails.length > 500) {
      errors.push('Story details must be 500 characters or less');
    }

    if (!formData.ageRange) {
      errors.push('Age range is required');
    }

    if (formData.themes.length === 0) {
      errors.push('At least one theme is required');
    }

    if (formData.themes.length > 2) {
      errors.push('Maximum 2 themes allowed');
    }

    return errors;
  }, []);

  const validateField = useCallback((field: keyof StoryFormData, value: unknown): string | null => {
    switch (field) {
      case 'mainCharacter':
        return !value || typeof value !== 'string' || !value.trim() ? 'Main character is required' : null;
      
      case 'setting':
        return !value || typeof value !== 'string' || !value.trim() ? 'Setting is required' : null;
      
      case 'storyDetails':
        if (!value || typeof value !== 'string' || !value.trim()) return 'Story details are required';
        if (value.length > 500) return 'Story details must be 500 characters or less';
        return null;
      
      case 'ageRange':
        return !value ? 'Age range is required' : null;
      
      case 'themes':
        if (!Array.isArray(value) || value.length === 0) {
          return 'At least one theme is required';
        }
        if (value.length > 2) {
          return 'Maximum 2 themes allowed';
        }
        return null;
      
      default:
        return null;
    }
  }, []);

  const isFormValid = useCallback((formData: StoryFormData): boolean => {
    return validateFormData(formData).length === 0;
  }, [validateFormData]);

  return {
    validateFormData,
    validateField,
    isFormValid,
  };
}