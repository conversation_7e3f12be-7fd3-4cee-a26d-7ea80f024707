import { useState, useEffect, useCallback } from 'react';
import { StoryFormData } from '../../types/form';
import { useFormDataLoader } from './useFormDataLoader';
import { useStorySubmission } from './useStorySubmission';
import { useFormValidation } from './useFormValidation';

export function useStoryForm() {
  const [formData, setFormData] = useState<StoryFormData>({
    mainCharacter: '',
    setting: '',
    themes: [],
    storyDetails: '',
    ageRange: '',
    tags: []
  });
  const [tagInput, setTagInput] = useState('');

  const {
    themes,
    ageRanges,
    isLoadingData,
    loadUserPreferences,
  } = useFormDataLoader();

  const {
    isLoading,
    submitStory,
  } = useStorySubmission();

  const {
    validateField,
    isFormValid,
  } = useFormValidation();

  // Load user preferences when themes are available
  useEffect(() => {
    if (themes.length > 0) {
      loadUserPreferences(themes).then(preferences => {
        setFormData(prev => ({
          ...prev,
          ...preferences
        }));
      });
    }
  }, [themes, loadUserPreferences]);

  const handleThemeToggle = useCallback((themeName: string) => {
    setFormData(prev => ({
      ...prev,
      themes: prev.themes.includes(themeName)
        ? prev.themes.filter(t => t !== themeName)
        : prev.themes.length < 2
        ? [...prev.themes, themeName]
        : prev.themes
    }));
  }, []);

  const handleAddTag = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && tagInput.trim()) {
      e.preventDefault();
      if (!formData.tags.includes(tagInput.trim())) {
        setFormData(prev => ({
          ...prev,
          tags: [...prev.tags, tagInput.trim()]
        }));
      }
      setTagInput('');
    }
  }, [formData.tags, tagInput]);

  const handleRemoveTag = useCallback((tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  }, []);

  const handleAddTagDirect = useCallback((tagName: string) => {
    if (tagName.trim() && !formData.tags.includes(tagName.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagName.trim()]
      }));
    }
  }, [formData.tags]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    await submitStory(formData);
  }, [formData, submitStory]);

  return {
    formData,
    setFormData,
    tagInput,
    setTagInput,
    themes,
    ageRanges,
    isLoading,
    isLoadingData,
    handleThemeToggle,
    handleAddTag,
    handleAddTagDirect,
    handleRemoveTag,
    handleSubmit,
    validateField,
    isFormValid: isFormValid(formData),
  };
}