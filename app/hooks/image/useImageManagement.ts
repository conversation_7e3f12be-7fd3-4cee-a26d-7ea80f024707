import { useState, useCallback, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { ImageData, ImageLimits, ImageModalState } from '../../types/image';

export function useImageManagement(storyUuid: string, isOpen: boolean) {
  const router = useRouter();
  const [state, setState] = useState<ImageModalState>({
    images: [],
    limits: null,
    userPrompt: '',
    isLoading: false,
    isLoadingImages: false,
    error: null,
    isRefreshingPlan: false,
  });

  const setImages = useCallback((images: ImageData[]) => {
    setState(prev => ({ ...prev, images }));
  }, []);

  const setLimits = useCallback((limits: ImageLimits | null) => {
    setState(prev => ({ ...prev, limits }));
  }, []);

  const setUserPrompt = useCallback((userPrompt: string) => {
    setState(prev => ({ ...prev, userPrompt }));
  }, []);

  const setIsLoadingImages = useCallback((isLoadingImages: boolean) => {
    setState(prev => ({ ...prev, isLoadingImages }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error }));
  }, []);

  const setIsRefreshingPlan = useCallback((isRefreshingPlan: boolean) => {
    setState(prev => ({ ...prev, isRefreshingPlan }));
  }, []);

  const fetchImages = useCallback(async () => {
    try {
      setIsLoadingImages(true);
      const response = await fetch(`/api/stories/${storyUuid}/images?t=${Date.now()}`, {
        cache: 'no-store'
      });
      if (response.ok) {
        const data = await response.json();
        setImages(data.images);
      } else {
        console.error('Failed to fetch images');
      }
    } catch (error) {
      console.error('Error fetching images:', error);
    } finally {
      setIsLoadingImages(false);
    }
  }, [storyUuid, setIsLoadingImages, setImages]);

  const fetchLimits = useCallback(async () => {
    try {
      const response = await fetch(`/api/subscription/image-limits?storyUuid=${storyUuid}`);
      if (response.ok) {
        const data = await response.json();
        setLimits(data);
      } else {
        console.error('Failed to fetch limits');
      }
    } catch (error) {
      console.error('Error fetching limits:', error);
    }
  }, [storyUuid, setLimits]);

  const handleSetDefault = useCallback(async (imageId: number, onSuccess?: () => void) => {
    try {
      const response = await fetch(`/api/stories/${storyUuid}/images`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ imageId }),
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          images: prev.images.map(img => ({
            ...img,
            default: img.id === imageId
          }))
        }));
        
        if (onSuccess) {
          onSuccess();
        }
        
        router.refresh();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to set default image');
      }
    } catch (error) {
      console.error('Error setting default image:', error);
      setError('Failed to set default image');
    }
  }, [storyUuid, setError, router]);

  const handleDeleteImage = useCallback(async (imageId: number) => {
    try {
      const response = await fetch(`/api/stories/${storyUuid}/images/${imageId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        // Remove the deleted image from state
        setState(prev => ({
          ...prev,
          images: prev.images.filter(img => img.id !== imageId)
        }));
        
        // Refresh the page to update the story image if needed
        router.refresh();
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to delete image');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      setError('Failed to delete image');
    }
  }, [storyUuid, setError, router]);

  const handleRegenerateImage = async (onSuccess?: () => void) => {
    if (!state.userPrompt.trim()) {
      return;
    }

    if (!state.limits?.canRegenerate) {
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const response = await fetch(`/api/stories/${storyUuid}/regenerate-image`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userPrompt: state.userPrompt,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: 'Failed to regenerate image' }));
        throw new Error(errorData.error || 'Failed to regenerate image');
      }

      await response.json();

      // Refresh the data
      await Promise.all([
        fetchImages(),
        fetchLimits(),
        handleRefreshPlan()
      ]);

      // Clear the prompt
      setState(prev => ({ ...prev, userPrompt: '' }));

      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }

    } catch (error) {
      console.error('useImageManagement: Image regeneration error:', error);
      setState(prev => ({ 
        ...prev, 
        error: error instanceof Error ? error.message : 'Failed to regenerate image' 
      }));
    } finally {
      setState(prev => ({ ...prev, isLoading: false }));
    }
  };

  const handleRefreshPlan = useCallback(async () => {
    setIsRefreshingPlan(true);
    try {
      const response = await fetch('/api/subscription/sync', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        await fetchLimits();
        setError(null);
      } else {
        setError('Failed to refresh plan. Please try again.');
      }
    } catch (error) {
      console.error('Error refreshing plan:', error);
      setError('Failed to refresh plan. Please try again.');
    } finally {
      setIsRefreshingPlan(false);
    }
  }, [setIsRefreshingPlan, fetchLimits, setError]);

  useEffect(() => {
    if (isOpen) {
      fetchImages();
      fetchLimits();
    }
  }, [isOpen, fetchImages, fetchLimits]);

  return {
    state,
    handleSetDefault,
    handleDeleteImage,
    handleRegenerateImage,
    handleRefreshPlan,
    setUserPrompt,
  };
}