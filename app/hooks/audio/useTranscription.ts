import { useState, useCallback, useEffect } from 'react';
import { TranscriptionResult, TranscriptionState, TranscriptionStatus } from '../../types/audio';
import { useFeatures } from '../useFeatures';

export function useTranscription(
  storyId: string, 
  hasAudio: boolean, 
  shareToken?: string
) {
  const { canGenerateTranscription, isLoading: featuresLoading } = useFeatures();
  const [state, setState] = useState<TranscriptionState>({
    transcription: null,
    status: 'not_started'
  });
  const [isTranscribing, setIsTranscribing] = useState(false);

  const setTranscription = useCallback((transcription: TranscriptionResult | null) => {
    setState(prev => ({ ...prev, transcription }));
  }, []);

  const setStatus = useCallback((status: TranscriptionStatus) => {
    setState(prev => ({ ...prev, status }));
  }, []);

  const loadTranscriptionStatus = useCallback(async () => {
    // Don't load transcription status if user doesn't have access
    if (!canGenerateTranscription() && !shareToken) {
      return;
    }

    try {
      let url: string;
      
      if (shareToken) {
        // Use share token endpoint
        url = `/api/transcriptions/${storyId}/status?token=${encodeURIComponent(shareToken)}`;
      } else {
        // Use private endpoint (requires authentication)
        url = `/api/transcriptions/${storyId}/status`;
      }
      
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setStatus(data.status || 'not_started');
      }
    } catch (error) {
      console.error('Error loading transcription status:', error);
      setStatus('not_started');
    }
  }, [storyId, shareToken, setStatus, canGenerateTranscription]);

  const loadTranscription = useCallback(async () => {
    // Don't load transcription if user doesn't have access
    if (!canGenerateTranscription() && !shareToken) {
      return;
    }

    try {
      let url: string;
      
      if (shareToken) {
        // Use share token endpoint
        url = `/api/transcriptions/${storyId}?token=${encodeURIComponent(shareToken)}`;
      } else {
        // Use private endpoint (requires authentication)
        url = `/api/transcriptions/${storyId}`;
      }
      
      const response = await fetch(url);
      if (response.ok) {
        const data = await response.json();
        setTranscription(data.transcription);
        setStatus('complete');
      } else {
        await loadTranscriptionStatus();
      }
    } catch (error) {
      console.error('Error loading transcription:', error);
      setStatus('failed');
    }
  }, [storyId, shareToken, setTranscription, setStatus, loadTranscriptionStatus, canGenerateTranscription]);

  const generateTranscription = useCallback(async (forceRegenerate = false) => {
    setIsTranscribing(true);

    try {
      const response = await fetch('/api/transcribe', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          story_id: storyId,
          force_regenerate: forceRegenerate 
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        if (response.status === 403 && data.upgradeRequired) {
          throw new Error(data.message || 'Premium subscription required for Read Along');
        } else {
          throw new Error(data.error || 'Failed to create read along');
        }
      }

      setTranscription(data.transcription);
      setStatus('complete');
      
    } catch (err) {
      setStatus('failed');
      throw err;
    } finally {
      setIsTranscribing(false);
    }
  }, [storyId, setTranscription, setStatus]);

  const getCurrentWordIndex = useCallback((currentTime: number) => {
    if (!state.transcription) return -1;
    
    // Convert currentTime from seconds to milliseconds to match AssemblyAI timestamps
    const currentTimeMs = currentTime * 1000;
    
    // Find the last word that has started (more reliable than exact timing matches)
    let currentWordIndex = -1;
    for (let i = 0; i < state.transcription.words.length; i++) {
      const word = state.transcription.words[i];
      if (currentTimeMs >= word.start) {
        currentWordIndex = i;
      } else {
        break;
      }
    }
    
    return currentWordIndex;
  }, [state.transcription]);

  const getWordStartTime = useCallback((wordIndex: number) => {
    if (!state.transcription || !state.transcription.words[wordIndex]) {
      return 0;
    }
    // Convert from milliseconds to seconds for HTML5 audio
    return state.transcription.words[wordIndex].start / 1000;
  }, [state.transcription]);

  useEffect(() => {
    // Only load transcription if user has access and features are loaded
    if (hasAudio && !featuresLoading) {
      loadTranscription();
    }
  }, [hasAudio, loadTranscription, featuresLoading]);

  return {
    transcription: state.transcription,
    status: state.status,
    isTranscribing,
    generateTranscription,
    getCurrentWordIndex,
    getWordStartTime,
  };
}