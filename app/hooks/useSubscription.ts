'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface SubscriptionData {
  canCreate: boolean;
  currentCount: number;
  limit: number;
  planName: string;
  remainingStories: number;
}

export function useSubscription() {
  const { data: session, status } = useSession();
  const [subscriptionData, setSubscriptionData] = useState<SubscriptionData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchSubscriptionData = useCallback(async () => {
    if (!session) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/subscription/limits');
      if (!response.ok) {
        throw new Error('Failed to fetch subscription data');
      }
      
      const data = await response.json();
      setSubscriptionData(data);
    } catch (err) {
      console.error('Error fetching subscription data:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription data');
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  useEffect(() => {
    if (status !== 'loading' && session) {
      fetchSubscriptionData();
    }
  }, [status, session, fetchSubscriptionData]);

  const refetch = () => {
    fetchSubscriptionData();
  };

  return {
    subscriptionData,
    isLoading: status === 'loading' || isLoading,
    error,
    refetch
  };
}