'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface Tag {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

interface UseTagsReturn {
  tags: Tag[];
  loading: boolean;
  error: string | null;
  createTag: (tagText: string) => Promise<boolean>;
  updateTag: (id: number, tagText: string) => Promise<boolean>;
  deleteTag: (id: number) => Promise<boolean>;
  refreshTags: () => Promise<void>;
}

export function useTags(): UseTagsReturn {
  const { data: session } = useSession();
  const [tags, setTags] = useState<Tag[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTags = useCallback(async () => {
    if (!session) {
      setTags([]);
      setLoading(false);
      return;
    }

    try {
      setError(null);
      const response = await fetch('/api/tags');
      
      if (!response.ok) {
        throw new Error('Failed to fetch tags');
      }

      const data = await response.json();
      setTags(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching tags:', err);
    } finally {
      setLoading(false);
    }
  }, [session]);

  const createTag = useCallback(async (tagText: string): Promise<boolean> => {
    if (!session || !tagText.trim()) return false;

    try {
      setError(null);
      const response = await fetch('/api/tags', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name: tagText }),
      });

      if (!response.ok) {
        throw new Error('Failed to create tag');
      }

      const createdTag = await response.json();
      setTags(prev => [createdTag, ...prev]);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create tag');
      console.error('Error creating tag:', err);
      return false;
    }
  }, [session]);

  const updateTag = useCallback(async (id: number, tagText: string): Promise<boolean> => {
    if (!session || !tagText.trim()) return false;

    try {
      setError(null);
      const response = await fetch('/api/tags', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ id, name: tagText }),
      });

      if (!response.ok) {
        throw new Error('Failed to update tag');
      }

      const updatedTag = await response.json();
      setTags(prev => prev.map(tag => tag.id === id ? updatedTag : tag));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update tag');
      console.error('Error updating tag:', err);
      return false;
    }
  }, [session]);

  const deleteTag = useCallback(async (id: number): Promise<boolean> => {
    if (!session) return false;

    try {
      setError(null);
      const response = await fetch(`/api/tags?id=${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete tag');
      }

      setTags(prev => prev.filter(tag => tag.id !== id));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete tag');
      console.error('Error deleting tag:', err);
      return false;
    }
  }, [session]);

  const refreshTags = useCallback(async () => {
    setLoading(true);
    await fetchTags();
  }, [fetchTags]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  return {
    tags,
    loading,
    error,
    createTag,
    updateTag,
    deleteTag,
    refreshTags,
  };
}