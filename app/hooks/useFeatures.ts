'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { Feature, FeatureAccess } from '../../lib/types/features';

interface FeatureState {
  plan: string;
  features: Record<string, FeatureAccess>;
  authenticated: boolean;
}

export function useFeatures() {
  const { data: session, status } = useSession();
  const [featureState, setFeatureState] = useState<FeatureState | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeatures = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/features');
      if (!response.ok) {
        throw new Error('Failed to fetch feature access');
      }
      
      const data = await response.json();
      setFeatureState(data);
    } catch (err) {
      console.error('Error fetching features:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch features');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (status !== 'loading') {
      fetchFeatures();
    }
  }, [status, session, fetchFeatures]);

  // Convenience methods for checking specific features
  const hasFeature = useCallback((feature: Feature): boolean => {
    return featureState?.features[feature]?.hasAccess || false;
  }, [featureState]);

  const getFeatureAccess = useCallback((feature: Feature): FeatureAccess | null => {
    return featureState?.features[feature] || null;
  }, [featureState]);

  const canCreateStory = useCallback((): boolean => {
    return hasFeature(Feature.STORY_CREATION);
  }, [hasFeature]);

  const canGenerateAudio = useCallback((): boolean => {
    return hasFeature(Feature.AUDIO_GENERATION);
  }, [hasFeature]);

  const canGenerateTranscription = useCallback((): boolean => {
    return hasFeature(Feature.TRANSCRIPTION_GENERATION);
  }, [hasFeature]);

  const canUseTranscriptionHighlighting = useCallback((): boolean => {
    return hasFeature(Feature.TRANSCRIPTION_HIGHLIGHTING);
  }, [hasFeature]);

  const canRegenerateImage = useCallback((): boolean => {
    return hasFeature(Feature.IMAGE_REGENERATION);
  }, [hasFeature]);

  const getStoriesRemaining = useCallback((): number => {
    const storyAccess = getFeatureAccess(Feature.STORY_CREATION);
    return storyAccess?.remaining || 0;
  }, [getFeatureAccess]);

  const getImageRegenerationsRemaining = useCallback((): number => {
    const imageAccess = getFeatureAccess(Feature.IMAGE_REGENERATION);
    return imageAccess?.remaining || 0;
  }, [getFeatureAccess]);

  const getRequiredPlanForFeature = useCallback((feature: Feature): string | null => {
    const featureAccess = getFeatureAccess(feature);
    return featureAccess?.requiredPlan || null;
  }, [getFeatureAccess]);

  const refetch = () => {
    fetchFeatures();
  };

  return {
    // State
    featureState,
    isLoading: status === 'loading' || isLoading,
    error,
    
    // Plan info
    plan: featureState?.plan || 'free',
    authenticated: featureState?.authenticated || false,
    
    // Feature checking methods
    hasFeature,
    getFeatureAccess,
    
    // Convenience methods
    canCreateStory,
    canGenerateAudio,
    canGenerateTranscription,
    canUseTranscriptionHighlighting,
    canRegenerateImage,
    
    // Usage info
    getStoriesRemaining,
    getImageRegenerationsRemaining,
    getRequiredPlanForFeature,
    
    // Actions
    refetch
  };
} 