'use client';

import { useState } from 'react';

interface PasswordUpdateData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface UsePasswordUpdateReturn {
  updatePassword: (data: PasswordUpdateData) => Promise<{ success: boolean; error?: string }>;
  isLoading: boolean;
  error: string | null;
  success: string | null;
  clearMessages: () => void;
}

export function usePasswordUpdate(): UsePasswordUpdateReturn {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const clearMessages = () => {
    setError(null);
    setSuccess(null);
  };

  const updatePassword = async (data: PasswordUpdateData): Promise<{ success: boolean; error?: string }> => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const response = await fetch('/api/user/update-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        const errorMessage = result.error || 'Failed to update password';
        setError(errorMessage);
        return { success: false, error: errorMessage };
      }

      setSuccess('Password updated successfully!');
      return { success: true };

    } catch {
      const errorMessage = 'An error occurred. Please try again.';
      setError(errorMessage);
      return { success: false, error: errorMessage };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    updatePassword,
    isLoading,
    error,
    success,
    clearMessages,
  };
} 