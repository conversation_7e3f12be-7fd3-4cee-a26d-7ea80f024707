import { useState, useMemo, useCallback } from 'react';

interface UsePaginationProps {
  totalItems: number;
  itemsPerPage: number;
  initialPage?: number;
}

interface PaginationResult<T> {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  startIndex: number;
  endIndex: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  goToPage: (page: number) => void;
  nextPage: () => void;
  previousPage: () => void;
  getPaginatedItems: <U extends T>(items: U[]) => U[];
}

export function usePagination<T>({
  totalItems,
  itemsPerPage,
  initialPage = 1
}: UsePaginationProps): PaginationResult<T> {
  const [currentPage, setCurrentPage] = useState(initialPage);

  const paginationData = useMemo(() => {
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const validCurrentPage = Math.min(Math.max(1, currentPage), totalPages || 1);
    
    const startIndex = (validCurrentPage - 1) * itemsPerPage;
    const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
    
    return {
      totalPages,
      validCurrentPage,
      startIndex,
      endIndex,
      hasNextPage: validCurrentPage < totalPages,
      hasPreviousPage: validCurrentPage > 1
    };
  }, [totalItems, itemsPerPage, currentPage]);

  const goToPage = useCallback((page: number) => {
    const validPage = Math.min(Math.max(1, page), paginationData.totalPages || 1);
    setCurrentPage(validPage);
  }, [paginationData.totalPages]);

  const nextPage = useCallback(() => {
    if (paginationData.hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [paginationData.hasNextPage]);

  const previousPage = useCallback(() => {
    if (paginationData.hasPreviousPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [paginationData.hasPreviousPage]);

  const getPaginatedItems = <U extends T>(items: U[]): U[] => {
    return items.slice(paginationData.startIndex, paginationData.endIndex);
  };

  return {
    currentPage: paginationData.validCurrentPage,
    totalPages: paginationData.totalPages,
    totalItems,
    itemsPerPage,
    startIndex: paginationData.startIndex,
    endIndex: paginationData.endIndex,
    hasNextPage: paginationData.hasNextPage,
    hasPreviousPage: paginationData.hasPreviousPage,
    goToPage,
    nextPage,
    previousPage,
    getPaginatedItems
  };
} 