'use client';

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';

interface PremiumFeatureAccess {
  hasAccess: boolean;
  planName: string;
  upgradeRequired: boolean;
  hasFamilyPlanAccess: boolean;
}

export function usePremiumFeatures() {
  const { data: session, status } = useSession();
  const [premiumAccess, setPremiumAccess] = useState<PremiumFeatureAccess | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPremiumAccess = useCallback(async () => {
    if (!session) return;
    
    try {
      setIsLoading(true);
      setError(null);
      
      const response = await fetch('/api/subscription/premium-features');
      if (!response.ok) {
        throw new Error('Failed to fetch premium feature access');
      }
      
      const data = await response.json();
      setPremiumAccess(data);
    } catch (err) {
      console.error('Error fetching premium access:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch premium access');
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  useEffect(() => {
    if (status !== 'loading') {
      if (session) {
        fetchPremiumAccess();
      } else {
        // For unauthenticated users, set default values
        setPremiumAccess({
          hasAccess: false,
          planName: 'free',
          upgradeRequired: true,
          hasFamilyPlanAccess: false
        });
        setIsLoading(false);
      }
    }
  }, [status, session, fetchPremiumAccess]);

  const refetch = () => {
    if (session) {
      fetchPremiumAccess();
    }
  };

  return {
    premiumAccess,
    isLoading: status === 'loading' || isLoading,
    error,
    refetch
  };
}