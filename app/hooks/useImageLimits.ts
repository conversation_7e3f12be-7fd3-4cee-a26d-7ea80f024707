'use client';

import { useState, useEffect, useCallback } from 'react';

interface ImageLimits {
  canRegenerate: boolean;
  currentCount: number;
  limit: number;
  planName: string;
  remaining: number;
}

export function useImageLimits(storyId?: number) {
  const [limits, setLimits] = useState<ImageLimits | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchLimits = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const url = storyId 
        ? `/api/subscription/image-limits?storyId=${storyId}` 
        : '/api/subscription/image-limits';
      
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error('Failed to fetch image regeneration limits');
      }
      
      const data = await response.json();
      setLimits(data);
    } catch (err) {
      console.error('Error fetching image limits:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch image limits');
    } finally {
      setIsLoading(false);
    }
  }, [storyId]);

  useEffect(() => {
    fetchLimits();
  }, [fetchLimits]);

  return {
    limits,
    isLoading,
    error,
    refetch: fetchLimits
  };
}