import { useState, useCallback } from 'react';
import { handleApiError, isUpgradeRequiredError } from '@/lib/utils/api';

export interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  upgradeRequired: boolean;
}

export interface UseApiCallOptions {
  onSuccess?: (data: unknown) => void;
  onError?: (error: string) => void;
  resetOnCall?: boolean;
}

export function useApiCall<T = unknown>(options: UseApiCallOptions = {}) {
  const { onSuccess, onError, resetOnCall = true } = options;

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    upgradeRequired: false,
  });

  const execute = useCallback(async (apiCallFn: () => Promise<T>) => {
    if (resetOnCall) {
      setState(prev => ({
        ...prev,
        loading: true,
        error: null,
        upgradeRequired: false,
      }));
    } else {
      setState(prev => ({ ...prev, loading: true }));
    }

    try {
      const result = await apiCallFn();
      setState({
        data: result,
        loading: false,
        error: null,
        upgradeRequired: false,
      });
      
      if (onSuccess) {
        onSuccess(result);
      }
      
      return result;
    } catch (error) {
      const errorMessage = handleApiError(error);
      const upgradeRequired = isUpgradeRequiredError(error);
      
      setState({
        data: null,
        loading: false,
        error: errorMessage,
        upgradeRequired,
      });
      
      if (onError) {
        onError(errorMessage);
      }
      
      throw error;
    }
  }, [onSuccess, onError, resetOnCall]);

  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      upgradeRequired: false,
    });
  }, []);

  const setData = useCallback((data: T | null) => {
    setState(prev => ({ ...prev, data }));
  }, []);

  const setError = useCallback((error: string | null) => {
    setState(prev => ({ ...prev, error, upgradeRequired: false }));
  }, []);

  return {
    ...state,
    execute,
    reset,
    setData,
    setError,
  };
}

// Specialized hooks for common patterns
export function useApiMutation<T = unknown>(options: UseApiCallOptions = {}) {
  return useApiCall<T>({ resetOnCall: false, ...options });
}

export function useApiQuery<T = unknown>(options: UseApiCallOptions = {}) {
  return useApiCall<T>({ resetOnCall: true, ...options });
} 