import { useState, useCallback, useEffect } from 'react';
import { Story, StoryManagementState, DeleteDialogState } from '../../types/story';

export function useStoryManagement(initialStories: Story[]) {
  const [state, setState] = useState<StoryManagementState>({
    stories: initialStories,
    deleteDialog: {
      isOpen: false,
      storyUuid: null,
      storyTitle: '',
      isDeleting: false
    }
  });

  const [isLoading, setIsLoading] = useState(false);

  // Fetch stories from API as fallback
  const fetchStories = useCallback(async () => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/my-stories');
      if (response.ok) {
        const stories = await response.json();
        setState(prev => ({ ...prev, stories }));
      }
    } catch (error) {
      console.error('Error fetching stories:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Effect to handle cases where initialStories might be empty due to hydration issues
  useEffect(() => {
    // Only fetch if we have no stories and not already loading
    // Add a flag to prevent multiple fetches
    let isCancelled = false;
    
    // Capture values at effect creation to avoid stale closures
    const shouldFetch = state.stories.length === 0 && !isLoading;
    
    if (shouldFetch) {
      // Small delay to allow for proper hydration
      const timer = setTimeout(() => {
        if (!isCancelled) {
          fetchStories();
        }
      }, 100);
      
      return () => {
        clearTimeout(timer);
        isCancelled = true;
      };
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Intentionally only run once on mount to avoid infinite loops

  const setDeleteDialog = useCallback((deleteDialog: DeleteDialogState) => {
    setState(prev => ({ ...prev, deleteDialog }));
  }, []);

  const handleDeleteClick = useCallback((storyUuid: string, storyTitle: string) => {
    setDeleteDialog({
      isOpen: true,
      storyUuid,
      storyTitle,
      isDeleting: false
    });
  }, [setDeleteDialog]);

  const handleDeleteConfirm = useCallback(async () => {
    if (!state.deleteDialog.storyUuid) return;

    setState(prev => ({ 
      ...prev, 
      deleteDialog: { ...prev.deleteDialog, isDeleting: true }
    }));

    try {
      const response = await fetch(`/api/stories/${state.deleteDialog.storyUuid}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          stories: prev.stories.filter(story => story.story_uuid !== state.deleteDialog.storyUuid)
        }));
        // Close dialog after a brief delay to show the deleting animation
        setTimeout(() => {
          setDeleteDialog({
            isOpen: false,
            storyUuid: null,
            storyTitle: '',
            isDeleting: false
          });
        }, 2000);
      } else {
        const error = await response.json();
        alert(`Failed to delete story: ${error.error}`);
        setState(prev => ({ 
          ...prev, 
          deleteDialog: { ...prev.deleteDialog, isDeleting: false }
        }));
      }
    } catch (error) {
      console.error('Error deleting story:', error);
      alert('Failed to delete story. Please try again.');
      setState(prev => ({ 
        ...prev, 
        deleteDialog: { ...prev.deleteDialog, isDeleting: false }
      }));
    }
  }, [state.deleteDialog.storyUuid, setDeleteDialog]);

  const handleDeleteCancel = useCallback(() => {
    setDeleteDialog({
      isOpen: false,
      storyUuid: null,
      storyTitle: '',
      isDeleting: false
    });
  }, [setDeleteDialog]);

  const handleTogglePublic = useCallback(async (storyUuid: string, newStatus: boolean) => {
    try {
      const response = await fetch(`/api/stories/${storyUuid}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_public: newStatus }),
      });

      if (response.ok) {
        setState(prev => ({
          ...prev,
          stories: prev.stories.map(story => 
            story.story_uuid === storyUuid ? { ...story, is_public: newStatus } : story
          )
        }));
      } else {
        const error = await response.json();
        alert(`Failed to update story: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating story status:', error);
      alert('Failed to update story status');
    }
  }, []);

  return {
    stories: state.stories,
    deleteDialog: state.deleteDialog,
    isLoading,
    fetchStories,
    handleDeleteClick,
    handleDeleteConfirm,
    handleDeleteCancel,
    handleTogglePublic,
  };
}