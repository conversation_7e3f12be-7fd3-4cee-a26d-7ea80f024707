import { useState, useMemo } from 'react';
import { useDebounce } from '../useDebounce';
import { Story } from '../../types/story';

export function useStoryFiltering(stories: Story[]) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTheme, setSelectedTheme] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Debounce search query to improve performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredStories = useMemo(() => {
    return stories.filter((story) => {
      // Search filter - check title, content, main character, and setting
      const matchesSearch = !debouncedSearchQuery || 
        story.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.content.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.main_character.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.setting.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      // Theme filter
      const matchesTheme = !selectedTheme || 
        story.themes.some(theme => theme === selectedTheme);

      // Tags filter - check if any of the selected tags match the story's actual tags
      const matchesTags = selectedTags.length === 0 || 
        selectedTags.some(selectedTag => 
          story.tags.some(storyTag => 
            storyTag.toLowerCase() === selectedTag.toLowerCase()
          )
        );

      return matchesSearch && matchesTheme && matchesTags;
    });
  }, [stories, debouncedSearchQuery, selectedTheme, selectedTags]);

  return {
    searchQuery,
    setSearchQuery,
    selectedTheme,
    setSelectedTheme,
    selectedTags,
    setSelectedTags,
    filteredStories
  };
}

// Public story interface for the discover page
interface PublicStory {
  id: number;
  story_uuid: string;
  title: string;
  main_character: string;
  setting: string;
  created_at: string;
  themes: string[];
  ageRange: string;
  hasImage: boolean;
  hasAudio: boolean;
  slug: string;
  excerpt: string;
  voteData?: {
    counts: {
      upvotes: number;
      downvotes: number;
      totalVotes: number;
      score: number;
    };
  };
}

export function usePublicStoryFiltering(stories: PublicStory[]) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTheme, setSelectedTheme] = useState('');
  const [selectedAgeRange, setSelectedAgeRange] = useState('');
  const [selectedAudioFilter, setSelectedAudioFilter] = useState('');

  // Debounce search query to improve performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300);

  const filteredStories = useMemo(() => {
    return stories.filter((story) => {
      // Search filter - check title, excerpt, main character, and setting
      const matchesSearch = !debouncedSearchQuery || 
        story.title.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.excerpt.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.main_character.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
        story.setting.toLowerCase().includes(debouncedSearchQuery.toLowerCase());

      // Theme filter
      const matchesTheme = !selectedTheme || 
        story.themes.some(theme => theme === selectedTheme);

      // Age range filter
      const matchesAgeRange = !selectedAgeRange || 
        story.ageRange === selectedAgeRange;

      // Audio filter
      const matchesAudio = !selectedAudioFilter || 
        (selectedAudioFilter === 'with-audio' && story.hasAudio) ||
        (selectedAudioFilter === 'without-audio' && !story.hasAudio);

      return matchesSearch && matchesTheme && matchesAgeRange && matchesAudio;
    });
  }, [stories, debouncedSearchQuery, selectedTheme, selectedAgeRange, selectedAudioFilter]);

  return {
    searchQuery,
    setSearchQuery,
    selectedTheme,
    setSelectedTheme,
    selectedAgeRange,
    setSelectedAgeRange,
    selectedAudioFilter,
    setSelectedAudioFilter,
    filteredStories
  };
} 