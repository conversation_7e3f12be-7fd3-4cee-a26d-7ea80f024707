export function getFriendlyErrorMessage(originalMessage: string, context: 'registration' | 'verification' | 'oauth' = 'registration'): string {
  const lowerMessage = originalMessage.toLowerCase();
  
  switch (context) {
    case 'registration':
      if (lowerMessage.includes('email') && lowerMessage.includes('exists')) {
        return "Hey there! Looks like you're already part of our story family! 👋 Try signing in instead.";
      }
      if (lowerMessage.includes('password')) {
        return "Your password needs a little more oomph! 💪 Try making it longer or adding some special characters.";
      }
      if (lowerMessage.includes('email')) {
        return "That email address looks a bit wonky! 🤪 Double-check those letters and dots?";
      }
      if (lowerMessage.includes('name')) {
        return "We'd love to know your name! 😊 Please fill in both first and last name fields.";
      }
      break;
      
    case 'verification':
      if (lowerMessage.includes('code') || lowerMessage.includes('verification')) {
        return "Whoops! That magic code isn't quite right. 🎩✨ Check your email and try again!";
      }
      if (lowerMessage.includes('expired')) {
        return "That code has gone on vacation! ⏰🏖️ No worries - we can send you a fresh one!";
      }
      break;
      
    case 'oauth':
      if (lowerMessage.includes('oauth') || lowerMessage.includes('social')) {
        return "Oops! Social sign-in seems to be having a little hiccup! 🤖 Let's give it another whirl!";
      }
      break;
  }
  
  return originalMessage;
} 