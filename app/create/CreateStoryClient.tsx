'use client';

import { useRouter } from 'next/navigation';
import LoadingSpinner from '../components/LoadingSpinner';
import GeneratingDialog from '../components/GeneratingDialog';
import { useStoryForm } from './hooks/useStoryForm';
import ThemeSelector from './components/ThemeSelector';
import TagInput from './components/TagInput';


export default function CreateStoryClient() {
  const router = useRouter();

  const {
    formData,
    setFormData,
    tagInput,
    setTagInput,
    themes,
    ageRanges,
    isLoading,
    isLoadingData,
    handleThemeToggle,
    handleAddTag,
    handleAddTagDirect,
    handleRemoveTag,
    handleSubmit
  } = useStoryForm();

  return (
    <>
      <div className="relative z-10">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8 sm:py-12">
          {/* Header Section */}
          <div className="text-center mb-8 sm:mb-12">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h1 className="text-3xl sm:text-4xl font-bold text-white">
                Create Your Story
              </h1>
            </div>
            <p className="text-lg sm:text-xl text-gray-300 max-w-2xl mx-auto">
              Bring your imagination to life with AI-powered storytelling
            </p>
          </div>



          {isLoadingData ? (
            <div className="flex justify-center py-12">
              <LoadingSpinner message="Loading story creator..." />
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-8">
                             {/* Step 1: Character & Setting */}
               <div className="bg-slate-800/50 rounded-2xl p-6 sm:p-8 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-semibold text-sm">
                    1
                  </div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white">Character & Setting</h2>
                </div>
                
                <div className="grid gap-6 sm:grid-cols-2">
                  <div className="space-y-3">
                    <label htmlFor="mainCharacter" className="block text-white font-medium text-sm sm:text-base">
                      Main Character <span className="text-red-400">*</span>
                    </label>
                    <input
                      type="text"
                      id="mainCharacter"
                      value={formData.mainCharacter}
                      onChange={(e) => setFormData(prev => ({ ...prev, mainCharacter: e.target.value }))}
                      placeholder="Who is the character of your story?"
                      className="w-full px-4 py-3 sm:py-4 bg-slate-700/70 border border-slate-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      required
                      aria-describedby="mainCharacter-help"
                    />
                    <p id="mainCharacter-help" className="text-gray-400 text-sm">
                      💡 Try: "Emma, a curious 7-year-old girl" or "Leo, a brave little lion"
                    </p>
                  </div>

                  <div className="space-y-3">
                    <label htmlFor="setting" className="block text-white font-medium text-sm sm:text-base">
                      Setting <span className="text-red-400">*</span>
                    </label>
                    <input
                      type="text"
                      id="setting"
                      value={formData.setting}
                      onChange={(e) => setFormData(prev => ({ ...prev, setting: e.target.value }))}
                      placeholder="Where does your story take place?"
                      className="w-full px-4 py-3 sm:py-4 bg-slate-700/70 border border-slate-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
                      required
                      aria-describedby="setting-help"
                    />
                    <p id="setting-help" className="text-gray-400 text-sm">
                      🌟 Try: "A magical forest" or "A space station orbiting Jupiter"
                    </p>
                  </div>
                </div>
              </div>

                             {/* Step 2: Themes */}
               <div className="bg-slate-800/50 rounded-2xl p-6 sm:p-8 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-semibold text-sm">
                    2
                  </div>
                  <h2 id="themes-label" className="text-xl sm:text-2xl font-bold text-white">Choose Themes</h2>
                  <span className="text-gray-400 text-sm">(Select up to 2)</span>
                </div>
                
                <div role="group" aria-labelledby="themes-label">
                  <ThemeSelector
                    themes={themes}
                    selectedThemes={formData.themes}
                    onThemeToggle={handleThemeToggle}
                  />
                </div>
              </div>

                             {/* Step 3: Story Details */}
               <div className="bg-slate-800/50 rounded-2xl p-6 sm:p-8 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-semibold text-sm">
                    3
                  </div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white">Story Details</h2>
                </div>
                
                <div className="space-y-3">
                  <label htmlFor="storyDetails" className="block text-white font-medium text-sm sm:text-base">
                    What should happen in your story? <span className="text-red-400">*</span>
                  </label>
                  <div className="relative">
                    <textarea
                      id="storyDetails"
                      value={formData.storyDetails}
                      onChange={(e) => setFormData(prev => ({ ...prev, storyDetails: e.target.value }))}
                      placeholder={formData.themes.length > 0 
                        ? themes.find(t => t.name === formData.themes[0])?.ideas || "Describe your story idea..."
                        : "Describe your story idea..."}
                      rows={6}
                      maxLength={500}
                      className="w-full px-4 py-4 bg-slate-700/70 border border-slate-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 resize-none"
                      required
                      aria-describedby="storyDetails-help storyDetails-count"
                    />
                    <div className="absolute bottom-3 right-3 text-gray-400 text-sm bg-slate-800/80 px-2 py-1 rounded">
                      {formData.storyDetails.length}/500
                    </div>
                  </div>
                  <p id="storyDetails-help" className="text-gray-400 text-sm">
                    ✨ Be creative! The more details you provide, the better your story will be.
                  </p>
                </div>
              </div>

                             {/* Step 4: Age & Tags */}
               <div className="bg-slate-800/50 rounded-2xl p-6 sm:p-8 border border-slate-700/50">
                <div className="flex items-center gap-3 mb-6">
                  <div className="flex items-center justify-center w-8 h-8 bg-blue-600 rounded-full text-white font-semibold text-sm">
                    4
                  </div>
                  <h2 className="text-xl sm:text-2xl font-bold text-white">Final Details</h2>
                </div>
                
                <div className="grid gap-6 sm:grid-cols-2">
                  <div className="space-y-3">
                    <label htmlFor="ageRange" className="block text-white font-medium text-sm sm:text-base">
                      Age Range <span className="text-red-400">*</span>
                    </label>
                    <select
                      id="ageRange"
                      value={formData.ageRange}
                      onChange={(e) => setFormData(prev => ({ ...prev, ageRange: e.target.value }))}
                      className="w-full px-4 py-3 sm:py-4 bg-slate-700/70 border border-slate-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 appearance-none cursor-pointer"
                      required
                    >
                      <option value="">Select age range</option>
                      {ageRanges.map((range) => (
                        <option key={range.id} value={range.range}>
                          {range.range}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div className="space-y-3">
                    <label className="block text-white font-medium text-sm sm:text-base">
                      Tags <span className="text-gray-400 text-sm font-normal">(Optional)</span>
                    </label>
                    <TagInput
                      tags={formData.tags}
                      tagInput={tagInput}
                      onTagInputChange={setTagInput}
                      onAddTag={handleAddTag}
                      onAddTagDirect={handleAddTagDirect}
                      onRemoveTag={handleRemoveTag}
                    />
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6">
                <button
                  type="button"
                  onClick={() => router.back()}
                  className="px-6 py-3 sm:py-4 border-2 border-slate-600 text-white rounded-xl hover:bg-slate-700 hover:border-slate-500 transition-all duration-200 font-medium"
                >
                  Cancel
                </button>
                                 <button
                   type="submit"
                   disabled={isLoading}
                   className={`flex-1 px-8 py-3 sm:py-4 rounded-xl transition-all duration-200 font-semibold text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 disabled:from-blue-800 disabled:to-purple-800 disabled:cursor-not-allowed text-white border-2 border-transparent shadow-lg hover:shadow-xl`}
                 >
                   {isLoading ? '🎨 Creating Your Story...' : '✨ Create My Story'}
                 </button>
              </div>
            </form>
          )}

          {/* Tips Section */}
          <div className="mt-12">
                         <div className="bg-gradient-to-br from-slate-800/60 to-slate-900/60 rounded-2xl p-6 sm:p-8 border border-slate-700/50">
              <div className="flex items-center gap-4 mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center">
                  <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-white">Tips for Amazing Stories</h3>
              </div>
              
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-blue-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-300">Be specific about your character's personality and appearance</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-purple-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-300">Include sensory details about your setting</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-green-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-300">Add a challenge or problem for your character to solve</p>
                </div>
                <div className="flex items-start gap-3">
                  <div className="w-2 h-2 bg-yellow-400 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-gray-300">Choose themes that match your child's interests</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <GeneratingDialog isOpen={isLoading} type="story" />
    </>
  );
}