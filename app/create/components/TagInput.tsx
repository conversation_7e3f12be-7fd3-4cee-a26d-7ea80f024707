'use client';

import { useState, useEffect } from 'react';

interface ExistingTag {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

interface TagInputProps {
  tags: string[];
  tagInput: string;
  onTagInputChange: (value: string) => void;
  onAddTag: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onRemoveTag: (tag: string) => void;
  onAddTagDirect?: (tagName: string) => void;
}

export default function TagInput({ tags, tagInput, onTagInputChange, onAddTag, onRemoveTag, onAddTagDirect }: TagInputProps) {
  const [existingTags, setExistingTags] = useState<ExistingTag[]>([]);
  const [isLoadingTags, setIsLoadingTags] = useState(true);

  // Fetch existing tags
  useEffect(() => {
    const fetchTags = async () => {
      try {
        const response = await fetch('/api/tags');
        if (response.ok) {
          const data = await response.json();
          setExistingTags(data);
        }
      } catch (error) {
        console.error('Error fetching tags:', error);
      } finally {
        setIsLoadingTags(false);
      }
    };

    fetchTags();
  }, []);

  // Handle clicking on an existing tag
  const handleExistingTagClick = (tagName: string) => {
    if (!tags.includes(tagName)) {
      if (onAddTagDirect) {
        onAddTagDirect(tagName);
      } else {
        // Fallback: set input value and clear it after adding
        onTagInputChange(tagName);
        setTimeout(() => {
          onTagInputChange('');
        }, 0);
      }
    }
  };

  // Filter existing tags to show only those not already selected
  const availableTags = existingTags.filter(tag => !tags.includes(tag.name));

  return (
    <div className="space-y-4">
      {/* Manual tag input */}
      <div className="relative">
        <input
          type="text"
          id="tags"
          value={tagInput}
          onChange={(e) => onTagInputChange(e.target.value)}
          onKeyDown={onAddTag}
          placeholder="Type a tag and press Enter..."
          className="w-full px-4 py-3 sm:py-4 bg-slate-700/70 border border-slate-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200"
          aria-describedby="tags-help"
        />
        <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.99 1.99 0 013 12V7a4 4 0 014-4z" />
          </svg>
        </div>
      </div>

      {/* Selected tags display */}
      {tags.length > 0 && (
        <div className="space-y-2">
          <p className="text-gray-400 text-sm font-medium">Selected tags:</p>
          <div className="flex flex-wrap gap-2">
            {tags.map((tag, index) => (
              <span
                key={index}
                className="inline-flex items-center gap-2 px-3 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white text-sm rounded-lg shadow-sm"
              >
                <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
                {tag}
                <button
                  type="button"
                  onClick={() => onRemoveTag(tag)}
                  className="ml-1 text-blue-200 hover:text-white transition-colors"
                  aria-label={`Remove ${tag} tag`}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Existing tags selection */}
      {!isLoadingTags && availableTags.length > 0 && (
        <div className="space-y-3">
          <p className="text-gray-400 text-sm font-medium">Or choose from your existing tags:</p>
          <div className="flex flex-wrap gap-2">
            {availableTags.slice(0, 8).map((tag) => (
              <button
                key={tag.id}
                type="button"
                onClick={() => handleExistingTagClick(tag.name)}
                className="inline-flex items-center gap-2 px-3 py-2 bg-slate-700/50 hover:bg-slate-600/70 text-gray-300 hover:text-white text-sm rounded-lg transition-all duration-200 border border-slate-600 hover:border-slate-500"
                aria-label={`Add ${tag.name} tag`}
              >
                <svg className="w-3 h-3 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
                {tag.name}
              </button>
            ))}
            {availableTags.length > 8 && (
              <span className="inline-flex items-center px-3 py-2 text-gray-500 text-sm">
                +{availableTags.length - 8} more
              </span>
            )}
          </div>
        </div>
      )}
      
      <p id="tags-help" className="text-gray-400 text-xs">
        💡 Tags help you organize and find your stories later
      </p>
    </div>
  );
} 