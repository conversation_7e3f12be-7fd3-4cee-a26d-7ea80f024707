import { Theme } from '../hooks/useStoryForm';

interface ThemeSelectorProps {
  themes: Theme[];
  selectedThemes: string[];
  onThemeToggle: (themeName: string) => void;
}

export default function ThemeSelector({ themes, selectedThemes, onThemeToggle }: ThemeSelectorProps) {
  return (
    <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
      {themes.map((theme) => {
        const isSelected = selectedThemes.includes(theme.name);
        const isDisabled = selectedThemes.length >= 2 && !isSelected;
        
        return (
          <button
            key={theme.id}
            type="button"
            onClick={() => onThemeToggle(theme.name)}
            disabled={isDisabled}
            className={`group relative p-3 sm:p-4 rounded-xl text-left transition-all duration-200 border-2 ${
              isSelected
                ? 'bg-gradient-to-br from-blue-600/20 to-purple-600/20 border-blue-500 shadow-lg shadow-blue-500/20'
                : isDisabled
                ? 'bg-slate-700/20 border-slate-600/30 opacity-40 cursor-not-allowed'
                : 'bg-slate-700/50 border-slate-600 hover:bg-slate-700/70 hover:border-slate-500 hover:shadow-md'
            }`}
            aria-pressed={isSelected}
            aria-describedby={`theme-${theme.id}-desc`}
          >
            {/* Theme content with aligned checkbox */}
            <div className="flex items-start gap-3">
              <div className={`relative w-5 h-5 rounded-full border-2 transition-all duration-200 flex-shrink-0 mt-0.5 flex items-center justify-center ${
                isSelected 
                  ? 'bg-blue-500 border-blue-500' 
                  : 'border-slate-400 group-hover:border-slate-300'
              }`}>
                {isSelected && (
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              
              <p 
                id={`theme-${theme.id}-desc`}
                className={`font-medium text-sm sm:text-base leading-relaxed ${
                  isSelected ? 'text-white' : isDisabled ? 'text-gray-500' : 'text-gray-200'
                }`}
              >
                {theme.description}
              </p>
            </div>
          </button>
        );
      })}
    </div>
  );
} 