interface FormFieldProps {
  label: string;
  required?: boolean;
  children: React.ReactNode;
  helpText?: string;
}

export default function FormField({ label, required = false, children, helpText }: FormFieldProps) {
  return (
    <div>
      <label className="block text-white text-lg font-semibold mb-3">
        {label} {required && <span className="text-red-400">*</span>}
      </label>
      {children}
      {helpText && (
        <p className="text-gray-400 text-sm mt-2">
          {helpText}
        </p>
      )}
    </div>
  );
} 