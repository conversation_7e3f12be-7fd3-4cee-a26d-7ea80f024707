'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

interface StoryLimits {
  canCreate: boolean;
  currentCount: number;
  limit: number;
  planName: string;
  remainingStories: number;
}

interface StoryLimitsDisplayProps {
  onLimitReached?: (isAtLimit: boolean) => void;
}

export default function StoryLimitsDisplay({ onLimitReached }: StoryLimitsDisplayProps) {
  const { data: session, status } = useSession();
  const [limits, setLimits] = useState<StoryLimits | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchLimits = async () => {
      if (!session) return;
      
      try {
        const response = await fetch('/api/subscription/limits');
        if (response.ok) {
          const data = await response.json();
          setLimits(data);
          
          // Notify parent component about limit status
          if (onLimitReached) {
            onLimitReached(!data.canCreate);
          }
        }
      } catch (error) {
        console.error('Error fetching story limits:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchLimits();
  }, [session, onLimitReached]);

  if (status === 'loading' || isLoading || !session) {
    return (
      <div className="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-700">
        <div className="animate-pulse">
          <div className="h-4 bg-slate-700 rounded w-1/4 mb-2"></div>
          <div className="h-3 bg-slate-700 rounded w-1/2 mb-4"></div>
          <div className="h-2 bg-slate-700 rounded w-full"></div>
        </div>
      </div>
    );
  }

  if (!limits) {
    return null;
  }

  return (
    <div className="bg-slate-800/50 px-3 py-2 rounded border border-slate-700 inline-block">
      <span className="text-white text-sm font-medium">
        {limits.planName} Plan - Stories: <span className="font-semibold">{limits.currentCount}</span> of {limits.limit}
      </span>
      <span className="text-gray-400 text-sm ml-2">
        ({limits.remainingStories} remaining)
      </span>
    </div>
  );
}