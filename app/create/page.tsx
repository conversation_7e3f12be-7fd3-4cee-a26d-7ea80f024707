import { redirect } from 'next/navigation';
import { Metada<PERSON> } from 'next';
import { auth } from '../../auth';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import CreateStoryClient from './CreateStoryClient';

export const metadata: Metadata = {
  title: 'Create Story - MyStoryMaker',
  description: 'Create magical personalized stories for your children. Use AI to transform your ideas into engaging tales with custom characters, settings, and themes.',
};

export default async function CreateStoryPage() {
  // Check if user is authenticated (server-side)
  const session = await auth();
  
  if (!session?.user?.id) {
    redirect('/');
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900">
      <Navigation currentPage="create" />
      <CreateStoryClient />
      <Footer />
    </div>
  );
}