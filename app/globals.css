@import "tailwindcss";

/* Atkinson Hyperlegible Font Face Declarations */
@font-face {
  font-family: 'Atkinson Hyperlegible';
  src: url('/fonts/AtkinsonHyperlegible-Regular.woff2') format('woff2');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Atkinson Hyperlegible';
  src: url('/fonts/AtkinsonHyperlegible-Bold.woff2') format('woff2');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'Atkinson Hyperlegible';
  src: url('/fonts/AtkinsonHyperlegible-Italic.woff2') format('woff2');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

@font-face {
  font-family: 'Atkinson Hyperlegible';
  src: url('/fonts/AtkinsonHyperlegible-BoldItalic.woff2') format('woff2');
  font-weight: bold;
  font-style: italic;
  font-display: swap;
}

/* OpenDyslexic Font Face Declarations */
@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Regular.otf') format('opentype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Bold.otf') format('opentype');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OpenDyslexic';
  src: url('/fonts/OpenDyslexic-Italic.otf') format('opentype');
  font-weight: normal;
  font-style: italic;
  font-display: swap;
}

/* Custom font family classes */
.font-atkinson {
  font-family: 'Atkinson Hyperlegible', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.font-opendyslexic {
  font-family: 'OpenDyslexic', Arial, sans-serif;
}

:root {
  --background: #111827;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #111827;
    --foreground: #ffffff;
  }
}

body {
  background: #111827;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* Safari optimizations without breaking other browsers */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body:after {
  content: "";
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: radial-gradient(circle at center, transparent 50%, rgba(0,0,0,0.9) 100%);
  pointer-events: none;
  z-index: 0;
}

html {
  background: #111827;
  /* Safari optimizations */
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

/* Smooth page transitions */
.page-transition-enter {
  opacity: 0;
  transform: translateY(20px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 400ms ease-out, transform 400ms ease-out;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-20px);
  transition: opacity 300ms ease-in, transform 300ms ease-in;
}

/* Fade in animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-fade-in-delay {
  animation: fadeIn 0.6s ease-out 0.2s both;
}

.animate-fade-in-delay-2 {
  animation: fadeIn 0.6s ease-out 0.4s both;
}

/* Safari-specific fixes - only apply to Safari */
@supports (-webkit-touch-callout: none) {
  /* iOS Safari specific styles */
  .hero-section {
    /* Hardware acceleration for better performance */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
    /* Force height constraints to prevent full-screen image on refresh */
    max-height: 462px !important;
    min-height: 384px !important;
  }
  
  /* Prevent Safari from ignoring height constraints on images */
  .hero-section img {
    max-height: 462px !important;
    object-fit: cover !important;
    object-position: center !important;
  }
  
  /* Fix for iOS viewport units */
  .min-h-screen {
    min-height: 100vh;
    min-height: -webkit-fill-available;
  }
  
  /* Prevent Safari from stretching images during load */
  .hero-section > div:first-child {
    max-height: 462px !important;
    overflow: hidden !important;
  }
}

/* Sparkle animation for hero section */
@keyframes sparkle {
  0%, 100% { 
    opacity: 0; 
    transform: scale(0.5); 
  }
  50% { 
    opacity: 0.8; 
    transform: scale(1); 
  }
}
