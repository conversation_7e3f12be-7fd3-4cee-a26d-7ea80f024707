'use client';

import { useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';
import { useTags } from '../hooks/useTags';
import { PageLoadingSpinner } from '../components/ui/LoadingSpinner';

// Note: Metadata export not available in client components
// Title will be set via document.title in useEffect if needed

interface Tag {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export default function TagsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { tags, loading, error, createTag, updateTag, deleteTag } = useTags();
  const [newTag, setNewTag] = useState('');
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editingText, setEditingText] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  if (status !== 'loading' && !session) {
    router.push('/sign-in');
    return null;
  }

  const handleCreateTag = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newTag.trim() || submitting) return;

    setSubmitting(true);
    try {
      const success = await createTag(newTag);
      if (success) {
        setNewTag('');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleUpdateTag = async (id: number) => {
    if (!editingText.trim() || submitting) return;

    setSubmitting(true);
    try {
      const success = await updateTag(id, editingText);
      if (success) {
        setEditingId(null);
        setEditingText('');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleDeleteTag = async (id: number) => {
    if (submitting) return;
    
    if (!confirm('Are you sure you want to delete this tag?')) return;

    setSubmitting(true);
    try {
      await deleteTag(id);
    } finally {
      setSubmitting(false);
    }
  };

  const startEditing = (tag: Tag) => {
    setEditingId(tag.id);
    setEditingText(tag.name);
  };

  const cancelEditing = () => {
    setEditingId(null);
    setEditingText('');
  };

  const filteredTags = tags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
        <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
        
        <Navigation currentPage="tags" />
        <div className="relative z-10">
          <div className="max-w-7xl mx-auto px-4">
            <PageLoadingSpinner message="Loading your tags..." />
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-blue-900/20 via-slate-900 to-slate-900"></div>
      <div className="absolute inset-0 bg-grid-white/[0.02] bg-[size:60px_60px]"></div>
      
      <Navigation currentPage="tags" />
      
      <main className="relative z-10 max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <div className="flex items-center gap-4 mb-6 justify-center">
            <div className="p-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl shadow-lg">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
              </svg>
            </div>
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">
              Tag Management
            </h1>
          </div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Organize and manage your story tags for better content discovery and categorization
          </p>
        </div>
        
        <div className="max-w-4xl mx-auto">
          <div className="bg-slate-800/80 border border-slate-700/50 rounded-xl p-6 lg:p-8">

          
          {/* Error display */}
          {error && (
            <div className="mb-8 p-4 bg-red-900/50 border border-red-600/50 rounded-lg">
              <div className="flex items-center gap-3">
                <div className="p-1 bg-red-500/20 rounded">
                  <svg className="w-4 h-4 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <p className="text-red-200 font-medium">{error}</p>
              </div>
            </div>
          )}
          
          {/* Search Section */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-blue-500/20 rounded-lg">
                <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h2 className="text-lg font-semibold text-white">Search Tags</h2>
            </div>
            <p className="text-gray-400 text-sm mb-4">Find specific tags in your collection</p>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search your tags..."
              className="w-full px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all"
            />
          </div>

          {/* Create New Tag Section */}
          <div className="mb-8">
            <div className="flex items-center gap-3 mb-4">
              <div className="p-2 bg-emerald-500/20 rounded-lg">
                <svg className="w-5 h-5 text-emerald-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
              </div>
              <h2 className="text-lg font-semibold text-white">Create New Tag</h2>
            </div>
            <p className="text-gray-400 text-sm mb-4">Add a new tag to organize your stories</p>
            <form onSubmit={handleCreateTag}>
              <div className="flex gap-3">
                <input
                  type="text"
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Enter tag name..."
                  className="flex-1 px-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-emerald-500/50 focus:border-emerald-500/50 transition-all"
                  maxLength={100}
                />
                <button
                  type="submit"
                  disabled={!newTag.trim() || submitting}
                  className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg font-medium transition-all flex items-center gap-2 shadow-lg"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                  Add Tag
                </button>
              </div>
            </form>
          </div>

          {/* Tags List Section */}
          <div>
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-purple-500/20 rounded-lg">
                <svg className="w-5 h-5 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
              </div>
              <h2 className="text-lg font-semibold text-white">Your Tags</h2>
              {tags.length > 0 && (
                <span className="px-2 py-1 bg-purple-500/20 text-purple-300 text-sm rounded-full">
                  {filteredTags.length} of {tags.length}
                </span>
              )}
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {tags.length === 0 ? (
                <div className="col-span-full text-center py-16">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-slate-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-3xl">🏷️</span>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2">No tags yet</h3>
                    <p className="text-gray-400 max-w-md mx-auto">
                      Create your first tag above to start organizing your stories and make them easier to discover.
                    </p>
                  </div>
                </div>
              ) : filteredTags.length === 0 ? (
                <div className="col-span-full text-center py-16">
                  <div className="mb-4">
                    <div className="w-16 h-16 bg-slate-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                      <span className="text-3xl">🔍</span>
                    </div>
                    <h3 className="text-xl font-semibold text-white mb-2">No matching tags</h3>
                    <p className="text-gray-400 max-w-md mx-auto">
                      No tags match your search criteria. Try a different search term or create a new tag.
                    </p>
                  </div>
                </div>
              ) : (
                filteredTags.map((tag) => (
                  <div
                    key={tag.id}
                    className="bg-slate-700/30 border border-slate-600/50 rounded-lg p-3 hover:bg-slate-700/50 transition-all group"
                  >
                        {editingId === tag.id ? (
                          <div className="space-y-2">
                            <input
                              type="text"
                              value={editingText}
                              onChange={(e) => setEditingText(e.target.value)}
                              className="w-full px-3 py-2 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 transition-all"
                              maxLength={100}
                              autoFocus
                            />
                        <div className="flex gap-2 justify-end">
                            <button
                              onClick={() => handleUpdateTag(tag.id)}
                              disabled={!editingText.trim() || submitting}
                            className="p-1.5 bg-gradient-to-r from-emerald-600 to-emerald-500 hover:from-emerald-700 hover:to-emerald-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all shadow-md"
                            title="Save changes"
                            >
                            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                              </svg>
                            </button>
                            <button
                              onClick={cancelEditing}
                              disabled={submitting}
                            className="p-1.5 bg-gradient-to-r from-gray-600 to-gray-500 hover:from-gray-700 hover:to-gray-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all shadow-md"
                            title="Cancel editing"
                            >
                            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                        </div>
                      </div>
                        ) : (
                      <div className="flex flex-col h-full">
                        <div className="flex-1 mb-2">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-purple-400 text-sm">🏷️</span>
                            <p className="text-white font-medium group-hover:text-blue-300 transition-colors truncate text-sm">{tag.name}</p>
                          </div>
                          <p className="text-gray-400 text-xs">
                            {new Date(tag.created_at).toLocaleDateString('en-US', { 
                              month: 'short', 
                              day: 'numeric',
                              year: '2-digit'
                            })}
                          </p>
                        </div>
                        <div className="flex gap-1.5 justify-end">
                            <button
                              onClick={() => startEditing(tag)}
                              disabled={submitting}
                            className="p-1.5 bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all shadow-md"
                            title="Edit tag"
                            >
                            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                              </svg>
                            </button>
                            <button
                              onClick={() => handleDeleteTag(tag.id)}
                              disabled={submitting}
                            className="p-1.5 bg-gradient-to-r from-red-600 to-red-500 hover:from-red-700 hover:to-red-600 disabled:from-slate-600 disabled:to-slate-600 disabled:cursor-not-allowed text-white rounded-lg transition-all shadow-md"
                            title="Delete tag"
                            >
                            <svg className="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                              </svg>
                            </button>
                        </div>
                      </div>
                    )}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}