import Link from 'next/link';
import Image from 'next/image';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { auth } from '../../../../auth';
import Navigation from '../../../components/Navigation';
import Footer from '../../../components/Footer';
import VoteButtons from '../../../components/VoteButtons';
import { query } from '../../../../lib/postgres-client';
import { getSafeImageUrl } from '../../../../lib/r2-storage-utils';
import { getThemeColor } from '../../../../lib/utils/themeColors';
import { getBaseUrl } from '../../../../lib/utils';
import UnifiedStoryActionsWrapper from './components/UnifiedStoryActionsWrapper';
import { Suspense } from 'react';
import PictureMagicButton from './components/PictureMagicButton';
import PublicPrivateToggle from './components/PublicPrivateToggle';
import { InlineLoadingSpinner } from '../../../components/ui/LoadingSpinner';
import StructuredData from '../../../components/StructuredData';

interface StoryPageProps {
  params: Promise<{
    uuid: string;
    slug: string;
  }>;
  searchParams: Promise<{
    source?: 'my-stories' | 'public' | 'shared';
    token?: string;
  }>;
}

interface StoryRecord {
  id: number;
  story_uuid: string;
  title: string;
  content: string;
  main_character: string;
  setting: string;
  details: string;
  age_range: string;
  created_at: string;
  is_public: boolean;
  user_id: string;
  is_deleted: boolean;
}

// Function to create URL-friendly slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Unified function to fetch story with access control
async function getStoryByUuidAndSlug(uuid: string, slug: string, userId: string | null, source?: string, shareToken?: string) {
  try {
    let whereClause = 's.story_uuid = $1 AND s.is_deleted = false';
    const params: (string | null)[] = [uuid];

    // Handle shared stories with token validation
    if (source === 'shared' && shareToken) {
      // Validate share token and check expiration
      const shareResult = await query(`
        SELECT s.*, ar.range as age_range, ss.expires_at
        FROM story_shares ss
        JOIN stories s ON ss.story_id = s.id
        LEFT JOIN age_ranges ar ON s.age_range = ar.id
        WHERE ss.access_token = $1 AND s.story_uuid = $2 AND s.is_deleted = false
      `, [shareToken, uuid]);

      if (shareResult.rows.length === 0) {
        return null; // Invalid share token
      }

      const story = shareResult.rows[0];

      // Check if share has expired
      if (new Date(story.expires_at) < new Date()) {
        return null; // Share expired
      }

      // Verify the slug matches
      if (createSlug(story.title) !== slug) {
        return null;
      }

      return await buildStoryObject(story, userId);
    }

    // Determine access based on source and user
    if (source === 'my-stories' && userId) {
      // My stories - user must own the story
      whereClause += ' AND s.user_id = $2';
      params.push(userId);
    } else if (source === 'public') {
      // Public stories - must be public
      whereClause += ' AND s.is_public = true';
    } else if (userId) {
      // Auto-detect: check if user owns it first, then check if it's public
      const ownerResult = await query(`
        SELECT s.*, ar.range as age_range
        FROM stories s
        LEFT JOIN age_ranges ar ON s.age_range = ar.id
        WHERE s.story_uuid = $1 AND s.user_id = $2 AND s.is_deleted = false
      `, [uuid, userId]);

      if (ownerResult.rows.length > 0) {
        // User owns this story
        const story = ownerResult.rows[0];
        if (createSlug(story.title) !== slug) return null;
        return await buildStoryObject(story, userId);
      }

      // Check if it's a public story
      whereClause += ' AND s.is_public = true';
    } else {
      // No user, only public stories
      whereClause += ' AND s.is_public = true';
    }

    // Fetch story
    const storyResult = await query(`
      SELECT s.*, ar.range as age_range
      FROM stories s
      LEFT JOIN age_ranges ar ON s.age_range = ar.id
      WHERE ${whereClause}
    `, params);

    if (storyResult.rows.length === 0) {
      return null;
    }

    const story = storyResult.rows[0];

    // Verify the slug matches (for SEO and correct URLs)
    if (createSlug(story.title) !== slug) {
      return null;
    }

    return await buildStoryObject(story, userId);
  } catch (error) {
    console.error('Error fetching story:', error);
    return null;
  }
}

// Helper function to build story object with themes, images, and audio
async function buildStoryObject(story: StoryRecord, userId: string | null) {
  // Get themes for this story
  const themesResult = await query(`
    SELECT t.description
    FROM stories_themes st
    JOIN themes t ON st.theme_id = t.id
    WHERE st.story_id = $1
  `, [story.id]);

  const themes = themesResult.rows.map((row: { description: string }) => row.description);

  // Get the default image for this story
  const imageResult = await query(
    'SELECT storage_path, updated_at FROM images WHERE story_id = $1 AND "default" = true AND (deleted IS NULL OR deleted = false)',
    [story.id]
  );

  let imageUrl = null;
  if (imageResult.rows.length > 0) {
    const imageData = imageResult.rows[0];
    // Use appropriate endpoint based on story ownership and access
    const timestamp = new Date(imageData.updated_at).getTime().toString();
    
    const isOwner = userId === story.user_id;
    const isPublic = story.is_public || false;
    
    if (isOwner) {
      // User owns the story - use private endpoint
      imageUrl = getSafeImageUrl(story.story_uuid, timestamp);
    } else if (isPublic) {
      // Public story - use public endpoint that doesn't require auth
      imageUrl = `/api/public/images/${story.id}?t=${timestamp}`;
    } else {
      // Private story that user doesn't own - no image access
      imageUrl = null;
    }
  }

  // Check if audio exists for this story
  let hasAudio = false;
  let hasTranscription = false;
  let transcriptionStatus = 'not_started';

  // For owned stories, check for user-specific audio
  // For public stories, check for any audio (created by the story owner)
  const isOwner = userId === story.user_id;
  const audioResult = await query(`
    SELECT a.storage_path, a.user_id, a.transcription_text, a.transcription_words, ts.status
    FROM audio a
    LEFT JOIN transcription_status ts ON a.transcription_status = ts.id
    WHERE a.story_id = $1 ${isOwner ? 'AND a.user_id = $2' : ''}
  `, isOwner ? [story.id, userId] : [story.id]);

  if (audioResult.rows.length > 0) {
    const audioData = audioResult.rows[0];
    hasAudio = true;
    transcriptionStatus = audioData.status || 'not_started';
    hasTranscription = transcriptionStatus === 'complete' && !!(audioData.transcription_text && audioData.transcription_words);
  }

  return {
    id: story.id,
    title: story.title,
    content: story.content,
    mainCharacter: story.main_character,
    setting: story.setting,
    themes: themes,
    ageRange: story.age_range || 'Unknown',
    details: story.details,
    createdAt: story.created_at,
    imageUrl: imageUrl,
    hasAudio: hasAudio,
    hasTranscription: hasTranscription,
    transcriptionStatus: transcriptionStatus,
    story_uuid: story.story_uuid,
    slug: createSlug(story.title),
    isPublic: story.is_public || false,
    isOwner: userId === story.user_id,
    userId: story.user_id
  };
}

// Generate metadata for Open Graph sharing
export async function generateMetadata({ params, searchParams }: StoryPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { uuid, slug } = resolvedParams;
  const { source, token } = resolvedSearchParams;

  const session = await auth();
  const userId = session?.user?.id || null;
  
  const story = await getStoryByUuidAndSlug(uuid, slug, userId, source, token);

  if (!story) {
    return {
      title: 'Story Not Found',
      description: 'The requested story could not be found.',
    };
  }

  const description = story.content.length > 160 
    ? story.content.substring(0, 157) + '...' 
    : story.content;

  const baseUrl = getBaseUrl();
  const storyUrl = `${baseUrl}/stories/${uuid}/${slug}`;
  
  // Use safe public image URL for metadata to avoid exposing user IDs
  // For metadata, always use absolute URLs and ensure public access
  const imageUrl = story.imageUrl 
    ? `${baseUrl}${story.imageUrl}` // Use the full imageUrl with cache-busting params
    : `${baseUrl}/images/hero.webp`;
  
  const imageAlt = story.imageUrl 
    ? `Illustration for ${story.title}` 
    : 'MyStoryMaker - AI-Powered Story Generator';

  const metadata: Metadata = {
    title: `${story.title} - MyStoryMaker`,
    description: description,
    authors: [{ name: 'MyStoryMaker' }],
    keywords: ['children stories', 'AI storytelling', 'kids books', 'story generator', 'educational stories'],
    creator: 'MyStoryMaker',
    publisher: 'MyStoryMaker',
    robots: 'index, follow',
    openGraph: {
      title: story.title,
      description: description,
      url: storyUrl,
      siteName: 'MyStoryMaker',
      locale: 'en_US',
      type: 'article',
      publishedTime: story.createdAt,
      modifiedTime: story.createdAt,
      section: story.ageRange,
      tags: story.themes,
      images: [
        {
          url: imageUrl,
          secureUrl: imageUrl,
          width: 1200,
          height: 630,
          alt: imageAlt,
        }
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: story.title,
      description: description,
      images: [
        {
          url: imageUrl,
          alt: imageAlt,
        }
      ],
    },
    other: {

      // Article-specific metadata
      'article:author': story.isOwner ? 'MyStoryMaker User' : '',
      'article:published_time': story.createdAt,
      'article:modified_time': story.createdAt,
      'article:section': story.ageRange,
      'article:tag': story.themes.join(','),
      // Twitter image alt (backup)
      'twitter:image:alt': imageAlt,
      // Ensure proper content type
      'Content-Type': 'text/html; charset=utf-8',
    },
  };

  return metadata;
}

// Main story content component
async function StoryContent({ params, searchParams }: StoryPageProps) {
  const resolvedParams = await params;
  const resolvedSearchParams = await searchParams;
  const { uuid, slug } = resolvedParams;
  const { source, token } = resolvedSearchParams;

  const session = await auth();
  const userId = session?.user?.id || null;
  
  const story = await getStoryByUuidAndSlug(uuid, slug, userId, source, token);

  if (!story) {
    notFound();
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
      month: 'long', 
      day: 'numeric', 
      year: 'numeric' 
    });
  };

  // Determine breadcrumb and badge based on story type and ownership
  const getBreadcrumbAndBadge = () => {
    if (story.isOwner) {
      return {
        breadcrumb: { href: '/my-stories', text: 'My Stories' },
        badge: null
      };
    } else if (source === 'shared') {
      return {
        breadcrumb: { href: '/public/stories', text: 'Discover Stories' },
        badge: { text: '🔗 Shared Story', className: 'bg-blue-700' }
      };
    } else if (story.isPublic) {
      return {
        breadcrumb: { href: '/public/stories', text: 'Discover Stories' },
        badge: null
      };
    } else {
      return {
        breadcrumb: { href: '/public/stories', text: 'Stories' },
        badge: null
      };
    }
  };

  const { breadcrumb, badge } = getBreadcrumbAndBadge();

  // Structured data for the story
  const structuredData = {
    title: story.title,
    description: story.content.substring(0, 160) + '...',
    image: story.imageUrl ? `https://mystorymaker.app${story.imageUrl}` : 'https://mystorymaker.app/images/hero.webp',
    url: `https://mystorymaker.app/stories/${story.story_uuid}/${createSlug(story.title)}`,
    datePublished: story.createdAt,
    themes: story.themes,
    ageRange: story.ageRange
  };

  // Breadcrumb structured data
  const breadcrumbData = [
    { name: 'Home', url: 'https://mystorymaker.app' },
    { name: breadcrumb.text, url: `https://mystorymaker.app${breadcrumb.href}` },
    { name: story.title, url: `https://mystorymaker.app/stories/${story.story_uuid}/${createSlug(story.title)}` }
  ];

  return (
    <>
      <StructuredData type="article" data={structuredData} />
      <StructuredData type="breadcrumb" data={breadcrumbData} />
      {/* Breadcrumb Navigation */}
      <div className="mb-8">
        <nav className="flex items-center space-x-2 text-sm text-gray-400">
          <Link href={breadcrumb.href} className="hover:text-white transition-colors">
            {breadcrumb.text}
          </Link>
          <span>›</span>
          <span className="text-white">{story.title}</span>
        </nav>
      </div>

      {/* Story Type Badge */}
      {badge && (
        <div className="mb-6">
          <span className={`inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium text-white border backdrop-blur-sm ${badge.className} ${
            badge.className.includes('blue') ? 'border-blue-500/30' :
            badge.className.includes('green') ? 'border-green-500/30' : 'border-slate-500/30'
          }`}>
            {badge.text}
          </span>
        </div>
      )}

      <div className="bg-gradient-to-br from-slate-800 to-slate-900 border border-slate-700/50 rounded-2xl overflow-hidden shadow-2xl">
        {/* Story Image with Overlay Content */}
        {story.imageUrl ? (
          <div className="relative">
            {/* Story Image */}
            <div className="relative h-48 sm:h-96 w-full">
              <Image
                src={story.imageUrl}
                alt={`Illustration for ${story.title}`}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 768px, 896px"
                priority
                placeholder="blur"
                blurDataURL="data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD/2wBDAAYEBQYFBAYGBQYHBwYIChAKCgkJChQODwwQFxQYGBcUFhYaHSUfGhsjHBYWICwgIyYnKSopGR8tMC0oMCUoKSj/2wBDAQcHBwoIChMKChMoGhYaKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCj/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwCdABmX/9k="
              />
              {/* Picture Magic Button - only for owners */}
              {story.isOwner && (
                <div className="absolute top-3 left-3 z-10">
                  <PictureMagicButton
                    storyUuid={story.story_uuid}
                    storyTitle={story.title}
                  />
                </div>
              )}

              {/* Mobile-only overlay elements */}
              <div className="sm:hidden">
                {/* Audio indicator - Top Left (mobile style) */}
                {story.hasAudio && (
                  <div className="absolute top-3 left-3">
                    <span className="px-3 py-1 bg-purple-700/80 backdrop-blur-sm text-white text-xs rounded-lg font-medium shadow-lg flex items-center gap-1" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}>
                      <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
                      </svg>
                      Audio
                    </span>
                  </div>
                )}

                {/* Theme Bubbles - Top Right (mobile style) */}
                {story.themes.length > 0 && (
                  <div className="absolute top-3 right-3 flex gap-1">
                    {story.themes.slice(0, 2).map((theme, index) => (
                      <span 
                        key={index}
                        className={`px-3 py-1 ${getThemeColor(theme)} text-white text-xs rounded-lg font-medium shadow-lg backdrop-blur-sm`}
                        style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}
                      >
                        {theme}
                      </span>
                    ))}
                    {story.themes.length > 2 && (
                      <span className="px-3 py-1 bg-gray-600/80 backdrop-blur-sm text-white text-xs rounded-lg font-medium shadow-lg" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}>
                        +{story.themes.length - 2}
                      </span>
                    )}
                  </div>
                )}

                {/* Date - Bottom Left (mobile style) */}
                <div className="absolute bottom-3 left-3">
                  <span className="px-3 py-1 bg-gray-700/80 backdrop-blur-sm text-white text-xs rounded-lg border border-gray-600/50 shadow-lg" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}>
                    {formatDate(story.createdAt)}
                  </span>
                </div>

                {/* Public/Private Status - Bottom Right (mobile style) */}
                {story.isOwner && (
                  <div className="absolute bottom-3 right-3">
                    <span className={`px-3 py-1 text-xs font-medium rounded-lg shadow-sm border ${
                      story.isPublic 
                        ? 'bg-green-700/80 text-white border-green-500/30' 
                        : 'bg-gray-700/80 text-white border-gray-500/30'
                    }`}>
                      {story.isPublic ? 'Public' : 'Private'}
                    </span>
                  </div>
                )}
              </div>

              {/* Desktop overlay content - hidden on mobile */}
              <div className="hidden sm:block absolute bottom-0 left-0 right-0 p-4">
                {/* Dark overlay for text readability on desktop */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent -z-10"></div>
                
                {/* Title - shown on desktop */}
                <h1 className="text-4xl font-bold text-white mb-4 drop-shadow-lg" style={{ textShadow: '0 2px 4px rgba(0, 0, 0, 0.8)' }}>{story.title}</h1>
                
                {/* Themes, Date and Public/Private Toggle - desktop layout */}
                <div className="flex items-center justify-between flex-wrap gap-4">
                  <div className="flex items-center gap-4 flex-wrap">
                    {/* Audio indicator */}
                    {story.hasAudio && (
                      <span className="px-3 py-1 bg-purple-700/80 text-white text-sm rounded-lg font-medium shadow-lg flex items-center gap-2" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}>
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
                        </svg>
                        Audio Available
                      </span>
                    )}
                    {/* Theme Bubbles */}
                    <div className="flex flex-wrap gap-2">
                      {story.themes.map((theme: string, index: number) => (
                        <span
                          key={index}
                          className={`px-3 py-1 ${getThemeColor(theme)} text-white text-sm rounded-lg font-medium shadow-lg`}
                          style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}
                        >
                          {theme}
                        </span>
                      ))}
                    </div>
                    {/* Date */}
                    <span className="px-3 py-1 bg-gray-700/80 text-white text-sm rounded-lg border border-gray-600/50 shadow-lg" style={{ textShadow: '0 1px 2px rgba(0, 0, 0, 0.8)' }}>
                      {formatDate(story.createdAt)}
                    </span>
                  </div>
                  {/* Public/Private Toggle - desktop */}
                  {story.isOwner && (
                    <PublicPrivateToggle 
                      storyUuid={story.story_uuid}
                      initialIsPublic={story.isPublic}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        ) : (
          /* Fallback for stories without images */
          <div className="p-4 sm:p-8">
            <div className="flex items-start justify-between mb-4">
              <h1 className="hidden sm:block text-4xl font-bold text-white">{story.title}</h1>
              {/* Picture Magic Button for stories without images - only for owners */}
              {story.isOwner && (
                <PictureMagicButton
                  storyUuid={story.story_uuid}
                  storyTitle={story.title}
                />
              )}
            </div>
            {/* Themes, Date and Public/Private Toggle */}
            <div className="flex items-center justify-between flex-wrap gap-4">
              <div className="flex items-center gap-4 flex-wrap">
                {/* Audio indicator */}
                {story.hasAudio && (
                  <span className="px-3 py-1 bg-purple-700 text-white text-sm rounded-lg font-medium flex items-center gap-2">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M9 9v6l4-3-4-3z" />
                    </svg>
                    Audio Available
                  </span>
                )}
                {/* Theme Bubbles */}
                <div className="flex flex-wrap gap-2">
                  {story.themes.map((theme: string, index: number) => (
                    <span
                      key={index}
                      className={`px-3 py-1 ${getThemeColor(theme)} text-white text-sm rounded-lg font-medium`}
                    >
                      {theme}
                    </span>
                  ))}
                </div>
                {/* Date */}
                <span className="text-gray-300 text-sm">
                  {formatDate(story.createdAt)}
                </span>
              </div>
              {/* Public/Private Toggle - only for owners */}
              {story.isOwner && (
                <PublicPrivateToggle 
                  storyUuid={story.story_uuid}
                  initialIsPublic={story.isPublic}
                />
              )}
            </div>
          </div>
        )}
        
        {/* Horizontal Separator */}
        <div className="border-t border-slate-700"></div>
        
        {/* Story Actions and Content */}
        <div className="px-4 sm:px-8 py-4">
          {/* Mobile Title - shown only on mobile */}
          <h1 className="block sm:hidden text-xl font-bold text-white mb-6">{story.title}</h1>
          
          <Suspense fallback={<InlineLoadingSpinner message="Loading story content..." />}>
            <UnifiedStoryActionsWrapper
              story={{
                id: story.id,
                title: story.title,
                content: story.content,
                themes: story.themes,
                ageRange: story.ageRange,
                createdAt: story.createdAt,
                imageUrl: story.imageUrl || undefined,
                story_uuid: story.story_uuid,
                isPublic: story.isPublic,
                isOwner: story.isOwner
              }}
              storyId={story.id.toString()}
              storyTitle={story.title}
              hasExistingAudio={story.hasAudio}
              isOwner={story.isOwner}
              isPublic={story.isPublic}
              shareToken={token}
            />
          </Suspense>
          
          {/* Voting Section for non-owners */}
          {!story.isOwner && (
            <>
              {/* Horizontal Separator */}
              <div className="border-t border-slate-700/50 my-6"></div>
              
              {/* Voting Section */}
              <div className="mb-6 bg-gradient-to-br from-slate-800/50 to-slate-900/50 border border-slate-700/50 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-white mb-4 flex items-center gap-2">
                  <svg className="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                  </svg>
                  Rate this story
                </h3>
                <Suspense fallback={<InlineLoadingSpinner message="Loading voting..." />}>
                  <VoteButtons 
                    storyUuid={uuid} 
                    size="lg" 
                    showCounts={true}
                    className="justify-start"
                  />
                </Suspense>
              </div>
            </>
          )}
          
          {/* Navigation Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 mt-6">
            <Link href={breadcrumb.href}>
              <button className="px-6 py-3 border border-slate-600/50 text-white rounded-xl hover:bg-slate-700/50 hover:border-slate-500 transition-all duration-200 font-medium">
                ← Back to {breadcrumb.text}
              </button>
            </Link>
            {!story.isOwner && (
              <Link href="/create">
                <button className="px-6 py-3 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white rounded-xl transition-all duration-200 font-medium border border-blue-600/50 hover:border-blue-500 transform hover:scale-105 shadow-lg hover:shadow-xl">
                  Create Your Own Story
                </button>
              </Link>
            )}
          </div>
        </div>
      </div>
    </>
  );
}

export default async function UnifiedStoryPage({ params, searchParams }: StoryPageProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-50" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23334155' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>
      
      {/* Radial gradient accent */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-gradient-radial from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl"></div>
      
      <Navigation />
      
      <div className="max-w-4xl mx-auto px-2 sm:px-4 py-12 relative z-10">
        <Suspense fallback={<InlineLoadingSpinner message="Loading story..." />}>
          <StoryContent params={params} searchParams={searchParams} />
        </Suspense>
      </div>

      <Footer />
    </div>
  );
} 