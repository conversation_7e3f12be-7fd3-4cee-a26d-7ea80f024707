'use client';

import { useState } from 'react';
import PictureMagicModal from '../../../../components/PictureMagicModal';

interface PictureMagicButtonProps {
  storyUuid: string;
  storyTitle: string;
  onSuccess?: () => void;
}

export default function PictureMagicButton({
  storyUuid,
  storyTitle,
  onSuccess
}: PictureMagicButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleSuccess = () => {
    // Don't close the modal or reload the page
    // The modal will stay open and show the new image
    if (onSuccess) {
      onSuccess();
    }
  };

  return (
    <>
      <button
        onClick={() => setIsModalOpen(true)}
        className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded-lg text-xs font-medium transition-all flex items-center gap-1 shadow-sm"
        title="Open Picture Magic to regenerate and manage images"
      >
        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
        </svg>
        Picture Magic
      </button>

      <PictureMagicModal
        storyUuid={storyUuid}
        storyTitle={storyTitle}
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleSuccess}
      />
    </>
  );
} 