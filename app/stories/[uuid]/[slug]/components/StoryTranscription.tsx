'use client';

import { useTranscription } from '../../../../hooks/audio/useTranscription';

interface AudioState {
  isPlaying: boolean;
  currentTime: number;
  hasAudio: boolean;
  audioUrl: string | null;
  error: string | null;
  isGenerating: boolean;
  currentWordIndex: number;
}

interface StoryTranscriptionProps {
  storyId: string;
  storyText: string;
  highlightingEnabled: boolean;
  hasAudio: boolean;
  audioState: AudioState;
  seekToTime: (time: number) => void;
  shareToken?: string;
}

export default function StoryTranscription({
  storyId,
  storyText,
  highlightingEnabled,
  hasAudio,
  audioState,
  seekToTime,
  shareToken
}: StoryTranscriptionProps) {

  const {
    transcription,
    getCurrentWordIndex,
    getWordStartTime,
  } = useTranscription(storyId, hasAudio, shareToken);

  // Calculate current word index based on audio time
  const currentWordIndex = transcription && audioState.isPlaying && highlightingEnabled 
    ? getCurrentWordIndex(audioState.currentTime)
    : -1;

  const handleWordClick = (wordIndex: number) => {
    const startTime = getWordStartTime(wordIndex);
    seekToTime(startTime);
  };

  const renderTranscriptionText = () => {
    if (!transcription) {
      return <p className="text-slate-300 leading-relaxed">{storyText}</p>;
    }

    return (
      <div className="text-slate-300 leading-relaxed">
        {transcription.words.map((word, index: number) => (
          <span
            key={index}
            className={`cursor-pointer transition-colors duration-200 ${
              index === currentWordIndex && highlightingEnabled
                ? 'bg-blue-500 text-white px-1 rounded' 
                : 'hover:bg-slate-700 px-1 rounded'
            }`}
            onClick={() => handleWordClick(index)}
          >
            {word.word}
            {index < transcription.words.length - 1 ? ' ' : ''}
          </span>
        ))}
      </div>
    );
  };

  return (
    <div>
      {transcription && (
        <div className="mb-4">
          <span className="text-xs bg-green-600 text-white px-2 py-1 rounded">
            Interactive - Click on any word to jump to that part of the audio
          </span>
        </div>
      )}
      {renderTranscriptionText()}
    </div>
  );
} 