'use client';

import { useEffect, useState } from 'react';
import { useAudioPlayer } from '../../../../hooks/audio/useAudioPlayer';
import { useTranscription } from '../../../../hooks/audio/useTranscription';
import { useFeatures } from '../../../../hooks/useFeatures';
import ErrorMessage from '../../../../components/ui/ErrorMessage';
import TextTools from './TextTools';

interface StoryData {
  id: number;
  title: string;
  content: string;
  themes: string[];
  ageRange: string;
  createdAt: string;
  imageUrl?: string;
  story_uuid: string;
  isPublic: boolean;
  isOwner: boolean;
}

interface UnifiedStoryActionsProps {
  story: StoryData;
  storyId: string;
  hasExistingAudio: boolean;
  highlightingEnabled: boolean;
  onShare: () => void;
  onToggleHighlighting: () => void;
  onError: (error: string) => void;
  error: string | null;
  shareToken?: string;
  isOwner?: boolean;
  isPublic?: boolean;
}

export default function UnifiedStoryActions({
  story,
  storyId,
  hasExistingAudio,
  highlightingEnabled,
  onShare,
  onToggleHighlighting,
  onError,
  error,
  shareToken,
  isOwner = false,
  isPublic = false
}: UnifiedStoryActionsProps) {
  const [duration, setDuration] = useState<number>(0);
  
  // Text formatting state
  const [fontSize, setFontSize] = useState(16);
  const [fontFamily, setFontFamily] = useState('font-atkinson');
  const [lineHeight, setLineHeight] = useState(1.6);
  const [isDarkMode, setIsDarkMode] = useState(true);
  
  const { 
    canGenerateAudio, 
    canGenerateTranscription,
    isLoading: featuresLoading 
  } = useFeatures();
  
  const {
    state: audioState,
    audioRef,
    loadExistingAudio,
    generateAudio,
    handleTimeUpdate,
    handleAudioEnded,
    handleAudioError,
    handleAudioLoaded,
    seekToTime,
    setCurrentWordIndex,
    setHasAudio,
    setIsPlaying,
    setAudioUrl,
  } = useAudioPlayer();

  const { 
    transcription, 
    status: transcriptionStatus, 
    isTranscribing, 
    generateTranscription,
    getCurrentWordIndex,
    getWordStartTime
  } = useTranscription(storyId, audioState.hasAudio, shareToken);

  // Initialize hasAudio state
  useEffect(() => {
    setHasAudio(hasExistingAudio);
  }, [hasExistingAudio, setHasAudio]);

  // Update duration when audio loads
  useEffect(() => {
    const audio = audioRef.current;
    if (audio) {
      const updateDuration = () => {
        if (audio.duration && !isNaN(audio.duration)) {
          setDuration(audio.duration);
        }
      };
      
      audio.addEventListener('loadedmetadata', updateDuration);
      audio.addEventListener('durationchange', updateDuration);
      
      // Check if duration is already available
      updateDuration();
      
      return () => {
        audio.removeEventListener('loadedmetadata', updateDuration);
        audio.removeEventListener('durationchange', updateDuration);
      };
    }
    // audioRef is a ref object and doesn't need to be in dependencies
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [audioState.audioUrl]);

  const handleDownload = async () => {
    try {
      // Dynamic import to prevent server-side bundling issues
      const { StoryPDFGenerator } = await import('../../../../../lib/pdf/storyPdfGenerator');
      const generator = new StoryPDFGenerator();
      const pdfBlob = await generator.generateStoryPDF(story);
      
      // Create download link
      const url = URL.createObjectURL(pdfBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${story.title.replace(/[^a-z0-9]/gi, '_').toLowerCase()}.pdf`;
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    }
  };

  const handlePlayPause = async () => {
    // If no audio URL but we have existing audio, load it first
    if (!audioState.audioUrl && hasExistingAudio) {
      try {
        onError('');

        // For public stories that user doesn't own, use public audio endpoint
        if (isPublic && !isOwner) {
          const publicAudioUrl = `/api/public/audio/${story.story_uuid}`;
          // Set the audio URL directly for public stories
          setAudioUrl(publicAudioUrl);
          setHasAudio(true);

          // Auto-play if requested
          if (audioRef.current) {
            audioRef.current.src = publicAudioUrl;
            audioRef.current.load();

            // Wait for the audio to be ready before playing
            const playAudio = () => {
              if (audioRef.current) {
                audioRef.current.play()
                  .then(() => setIsPlaying(true))
                  .catch(() => onError('Failed to play audio'));
              }
            };

            audioRef.current.addEventListener('canplay', playAudio, { once: true });
          }
          return;
        } else {
          // For owned stories, use the existing audio loading logic
          await loadExistingAudio(storyId, true); // autoPlay = true
          return;
        }
      } catch (err) {
        onError(err instanceof Error ? err.message : 'Failed to load audio');
        return;
      }
    }

    // If no audio URL and no existing audio, generate new audio (for premium users)
    if (!audioState.audioUrl && !hasExistingAudio && hasAccess) {
      try {
        onError('');
        await generateAudio(storyId, false, true); // autoPlay = true
        return;
      } catch (err) {
        onError(err instanceof Error ? err.message : 'Failed to generate audio');
        return;
      }
    }

    // If we have audio URL, toggle playback
    if (!audioRef.current || !audioState.audioUrl) return;

    try {
      if (audioState.isPlaying) {
        audioRef.current.pause();
        setIsPlaying(false);
      } else {
        await audioRef.current.play();
        setIsPlaying(true);
      }
    } catch (error) {
      console.error('Error playing audio:', error);
      onError('Failed to play audio');
    }
  };

  const handleMute = () => {
    if (audioRef.current) {
      audioRef.current.muted = !audioRef.current.muted;
    }
  };

  const handleStop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setCurrentWordIndex(-1);
      // Properly set the playing state to false when stopping
      setIsPlaying(false);
    }
  };

  const handleGenerateTranscription = async () => {
    if (!canGenerateTranscription()) return;
    
    try {
      onError('');
      await generateTranscription();
    } catch (err) {
      onError(err instanceof Error ? err.message : 'Failed to create read along');
    }
  };

  const handleSeek = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!audioRef.current || !duration) return;
    
    const rect = e.currentTarget.getBoundingClientRect();
    const clickX = e.clientX - rect.left;
    const percentage = clickX / rect.width;
    const newTime = percentage * duration;
    
    seekToTime(newTime);
  };

  const formatTime = (time: number) => {
    if (!time || isNaN(time)) return '0:00';
    
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const progressPercentage = duration > 0 ? (audioState.currentTime / duration) * 100 : 0;
  const hasAccess = canGenerateAudio();
  const hasFamilyPlanAccess = canGenerateTranscription();

  // Calculate current word index for highlighting - only for Family Plan users
  const currentWordIndex = transcription && audioState.isPlaying && highlightingEnabled && hasFamilyPlanAccess
    ? getCurrentWordIndex(audioState.currentTime)
    : -1;

  const handleWordClick = (wordIndex: number) => {
    const startTime = getWordStartTime(wordIndex);
    seekToTime(startTime);
  };

  const renderTranscriptionText = () => {
    const textStyles = {
      fontSize: `${fontSize}px`,
      lineHeight: lineHeight,
    };

    const containerClasses = `
      ${fontFamily} 
      space-y-4 
      font-light 
      transition-all 
      duration-300 
      ${isDarkMode 
        ? 'text-slate-100' 
        : 'text-gray-900 bg-white/95 p-6 rounded-lg border border-gray-200 shadow-sm'
      }
    `.trim();

    if (!transcription || !hasFamilyPlanAccess) {
      // Split original content into paragraphs and render with proper spacing
      const paragraphs = story.content.split('\n').filter(p => p.trim());
      return (
        <div className={containerClasses} style={textStyles}>
          {paragraphs.map((paragraph, index) => (
            <p key={index}>{paragraph}</p>
          ))}
        </div>
      );
    }

    // Create a mapping of original story structure to transcribed words
    const originalParagraphs = story.content.split('\n').filter(p => p.trim());
    const transcribedText = transcription.words.map(w => w.word).join(' ');
    
    // Simple approach: try to match paragraph breaks based on sentence endings
    const sentences = transcribedText.split(/[.!?]+/).filter(s => s.trim());
    
    // Distribute words into paragraphs based on original structure
    const targetParagraphs = Math.min(originalParagraphs.length, Math.max(2, Math.ceil(sentences.length / 3)));
    const wordsPerParagraph = Math.ceil(transcription.words.length / targetParagraphs);
    
    const paragraphs = [];
    let wordIndex = 0;
    
    for (let p = 0; p < targetParagraphs && wordIndex < transcription.words.length; p++) {
      const paragraphWords = [];
      const endIndex = Math.min(wordIndex + wordsPerParagraph, transcription.words.length);
      
      // Adjust end to complete sentences when possible
      let adjustedEndIndex = endIndex;
      if (endIndex < transcription.words.length) {
        // Look for sentence endings near the target break point
        for (let i = Math.max(wordIndex + Math.floor(wordsPerParagraph * 0.7), wordIndex + 1); 
             i < Math.min(endIndex + Math.floor(wordsPerParagraph * 0.3), transcription.words.length); 
             i++) {
          const word = transcription.words[i].word;
          if (word.match(/[.!?]$/)) {
            adjustedEndIndex = i + 1;
            break;
          }
        }
      }
      
      for (let i = wordIndex; i < adjustedEndIndex; i++) {
        paragraphWords.push(i);
      }
      
      paragraphs.push(paragraphWords);
      wordIndex = adjustedEndIndex;
    }
    
    // Add any remaining words to the last paragraph
    if (wordIndex < transcription.words.length) {
      const lastParagraph = paragraphs[paragraphs.length - 1] || [];
      for (let i = wordIndex; i < transcription.words.length; i++) {
        lastParagraph.push(i);
      }
      if (paragraphs.length === 0) {
        paragraphs.push(lastParagraph);
      }
    }

    return (
      <div className={containerClasses} style={textStyles}>
        {paragraphs.map((paragraphWordIndices, paragraphIndex) => (
          <p key={paragraphIndex}>
            {paragraphWordIndices.map((wordIndex, positionInParagraph) => (
              <span
                key={wordIndex}
                className={`cursor-pointer transition-colors duration-200 ${fontFamily} font-light ${
                  wordIndex === currentWordIndex && highlightingEnabled
                    ? 'bg-blue-500 text-white px-1 rounded' 
                    : isDarkMode 
                      ? 'hover:bg-slate-700 px-1 rounded'
                      : 'hover:bg-gray-200 px-1 rounded'
                }`}
                onClick={() => handleWordClick(wordIndex)}
              >
                {transcription.words[wordIndex].word}
                {positionInParagraph < paragraphWordIndices.length - 1 ? ' ' : ''}
              </span>
            ))}
          </p>
        ))}
      </div>
    );
  };

  return (
    <div className="w-full">
      {/* Hidden audio element */}
      {audioState.audioUrl && (
        <audio
          ref={audioRef}
          src={audioState.audioUrl}
          onTimeUpdate={handleTimeUpdate}
          onEnded={handleAudioEnded}
          onError={handleAudioError}
          onLoadedData={handleAudioLoaded}
          preload="metadata"
          controls={false}
        />
      )}

      {/* Full-width Audio Progress Bar - Only show when playing */}
      {audioState.audioUrl && duration > 0 && audioState.isPlaying && (
        <div className="w-full mb-6 bg-slate-900/50 rounded-lg p-4 border border-slate-700">
          {/* Now Playing Header */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2 text-white">
              <svg className="w-4 h-4 text-purple-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M9.383 3.076A1 1 0 0110 4v12a1 1 0 01-1.617.824L4.5 13H2a1 1 0 01-1-1V8a1 1 0 011-1h2.5l3.883-3.824zM15 8.75a.75.75 0 011.5 0v2.5a.75.75 0 01-1.5 0v-2.5z" clipRule="evenodd" />
              </svg>
              <span className="text-sm font-medium">Now Playing</span>
            </div>
            <div className="text-sm text-gray-300 font-mono">
              {formatTime(audioState.currentTime)} / {formatTime(duration)}
            </div>
          </div>
          
          {/* Progress Bar */}
          <div 
            className="w-full h-2 bg-slate-700 rounded-full cursor-pointer relative group"
            onClick={handleSeek}
          >
            <div 
              className="h-full bg-gradient-to-r from-purple-500 to-purple-400 rounded-full transition-all duration-150 relative"
              style={{ width: `${progressPercentage}%` }}
            >
              {/* Progress indicator dot */}
              <div className="absolute right-0 top-1/2 -translate-y-1/2 w-3 h-3 bg-white rounded-full shadow-lg opacity-0 group-hover:opacity-100 transition-opacity" />
            </div>
          </div>
        </div>
      )}

      {error && <ErrorMessage message={error} className="mb-4" />}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-6">
        {/* Download Button */}
        <button
          onClick={handleDownload}
          className="flex items-center gap-2 text-white hover:text-gray-300 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M12 3v12m0 0l-4-4m4 4l4-4M3 17v2a2 2 0 002 2h14a2 2 0 002-2v-2" />
          </svg>
          <span className="hidden sm:inline">Download</span>
        </button>

        {/* Share Button */}
        <button 
          onClick={onShare}
          className="flex items-center gap-2 text-white hover:text-gray-300 transition-colors"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
          </svg>
          <span className="hidden sm:inline">Share</span>
        </button>

        {/* Text Tools */}
        <TextTools
          fontSize={fontSize}
          fontFamily={fontFamily}
          lineHeight={lineHeight}
          isDarkMode={isDarkMode}
          onFontSizeChange={setFontSize}
          onFontFamilyChange={setFontFamily}
          onLineHeightChange={setLineHeight}
          onThemeToggle={() => setIsDarkMode(!isDarkMode)}
        />

        {/* Audio Controls - Always show, but with different states based on permissions */}
        {featuresLoading ? (
          <div className="flex items-center gap-2 animate-pulse">
            <div className="w-5 h-5 bg-slate-700 rounded"></div>
            <div className="hidden sm:block w-12 h-4 bg-slate-700 rounded"></div>
          </div>
        ) : !hasAccess && !audioState.hasAudio ? (
          <button 
            disabled
            className="flex items-center gap-2 text-gray-500 cursor-not-allowed transition-all duration-200"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
              <path strokeLinecap="round" strokeLinejoin="round" d="M5 3l14 9-14 9V3z" />
            </svg>
            <span className="hidden sm:inline">Play (Premium)</span>
          </button>
        ) : (audioState.hasAudio || hasAccess) ? (
          <button 
            onClick={handlePlayPause}
            disabled={audioState.isGenerating}
            className="flex items-center gap-2 text-white hover:text-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed transition-all duration-200"
          >
            {audioState.isGenerating ? (
              <>
                <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span className="hidden sm:inline">Generating...</span>
              </>
            ) : audioState.isPlaying ? (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 5.25v13.5m-7.5-13.5v13.5" />
                </svg>
                <span className="hidden sm:inline">Pause</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                  <path strokeLinecap="round" strokeLinejoin="round" d="M5 3l14 9-14 9V3z" />
                </svg>
                <span className="hidden sm:inline">Play</span>
              </>
            )}
          </button>
        ) : null}

        {/* Additional Audio Controls - only show when audio is available and not loading */}
        {!featuresLoading && (audioState.hasAudio || hasAccess) && (
          <>

            {/* Mute and Stop buttons - only show when audio is playing */}
            {audioState.isPlaying && (
              <>
                <button 
                  onClick={handleMute}
                  className="flex items-center gap-2 text-white hover:text-gray-300 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M15.536 8.464a5 5 0 010 7.072m2.828-9.9a9 9 0 010 12.728M6 10H4a1 1 0 00-1 1v2a1 1 0 001 1h2l4 4V6l-4 4z" />
                  </svg>
                  <span className="hidden sm:inline">Mute</span>
                </button>

                <button 
                  onClick={handleStop}
                  className="flex items-center gap-2 text-white hover:text-gray-300 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M5.25 7.5A2.25 2.25 0 017.5 5.25h9a2.25 2.25 0 012.25 2.25v9a2.25 2.25 0 01-2.25 2.25h-9a2.25 2.25 0 01-2.25-2.25v-9z" />
                  </svg>
                  <span className="hidden sm:inline">Stop</span>
                </button>
              </>
            )}

            {/* Transcription Controls - Only show for Family Plan users */}
            {audioState.hasAudio && hasFamilyPlanAccess && (
              <>
                {!transcription && transcriptionStatus !== 'complete' ? (
                  <button 
                    onClick={handleGenerateTranscription}
                    disabled={isTranscribing}
                    className="flex items-center gap-2 text-white hover:text-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed transition-colors"
                  >
                    {isTranscribing ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        <span className="hidden sm:inline">Creating Read Along...</span>
                      </>
                    ) : (
                      <>
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 14.25v-2.625a3.375 3.375 0 00-3.375-3.375h-1.5A1.125 1.125 0 0113.5 7.125v-1.5a3.375 3.375 0 00-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 00-9-9z" />
                        </svg>
                        <span className="hidden sm:inline">Read Along</span>
                      </>
                    )}
                  </button>
                ) : transcription && audioState.isPlaying ? (
                  <button 
                    onClick={onToggleHighlighting}
                    disabled={!transcription}
                    className={`flex items-center gap-2 transition-colors ${
                      highlightingEnabled 
                        ? 'text-yellow-400 hover:text-yellow-300' 
                        : 'text-white hover:text-gray-300'
                    } disabled:text-gray-500 disabled:cursor-not-allowed`}
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                      <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
                    </svg>
                    <span className="hidden sm:inline">{highlightingEnabled ? 'Turn Off Highlighting' : 'Turn On Highlighting'}</span>
                  </button>
                ) : null}
              </>
            )}
          </>
        )}
      </div>

      {/* Story Content with Transcription Highlighting */}
      <div className="mt-4">
        <div className="border-t border-slate-700 mb-4"></div>
        {renderTranscriptionText()}
        {transcription && hasFamilyPlanAccess && (
          <p className="text-xs text-slate-500 mt-4">
            Click on any word to jump to that part of the audio
          </p>
        )}
      </div>
    </div>
  );
} 