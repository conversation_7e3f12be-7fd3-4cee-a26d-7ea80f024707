'use client';

import { useState } from 'react';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';

interface PublicPrivateToggleProps {
  storyUuid: string;
  initialIsPublic: boolean;
}

export default function PublicPrivateToggle({ storyUuid, initialIsPublic }: PublicPrivateToggleProps) {
  const [isPublic, setIsPublic] = useState(initialIsPublic);
  const [isUpdating, setIsUpdating] = useState(false);

  const handleToggle = async () => {
    if (isUpdating) return;

    setIsUpdating(true);
    const newStatus = !isPublic;

    try {
      const response = await fetch(`/api/stories/${storyUuid}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ is_public: newStatus }),
      });

      if (response.ok) {
        setIsPublic(newStatus);
      } else {
        const error = await response.json();
        alert(`Failed to update story: ${error.error}`);
      }
    } catch (error) {
      console.error('Error updating story status:', error);
      alert('Failed to update story status');
    } finally {
      setIsUpdating(false);
    }
  };

  return (
    <button
      onClick={handleToggle}
      disabled={isUpdating}
      className={`inline-flex items-center gap-1.5 px-2.5 py-1.5 rounded-md text-xs font-semibold transition-all duration-200 ${
        isPublic 
          ? 'bg-white/95 hover:bg-white text-blue-700 border border-blue-200 shadow-sm' 
          : 'bg-black/75 hover:bg-black/85 text-white border border-white/20 shadow-sm'
      } ${isUpdating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-md'} backdrop-blur-sm`}
      title={`Click to make story ${isPublic ? 'private' : 'public'}`}
    >
      {isPublic ? (
        <EyeIcon className="w-3.5 h-3.5" />
      ) : (
        <EyeSlashIcon className="w-3.5 h-3.5" />
      )}
      <span className="hidden sm:inline">
        {isPublic ? 'Public' : 'Private'}
      </span>
      <span className="sm:hidden">
        {isPublic ? 'Pub' : 'Priv'}
      </span>
      {isUpdating && (
        <div className={`w-3 h-3 border rounded-full animate-spin ${
          isPublic ? 'border-blue-300 border-t-blue-700' : 'border-white/30 border-t-white'
        }`}></div>
      )}
    </button>
  );
} 