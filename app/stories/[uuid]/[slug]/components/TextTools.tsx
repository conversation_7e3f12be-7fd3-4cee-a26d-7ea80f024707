'use client';

import { useState } from 'react';

interface TextToolsProps {
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  isDarkMode: boolean;
  onFontSizeChange: (size: number) => void;
  onFontFamilyChange: (family: string) => void;
  onLineHeightChange: (height: number) => void;
  onThemeToggle: () => void;
}

// Default values
const DEFAULT_FONT_SIZE = 16;
const DEFAULT_FONT_FAMILY = 'font-atkinson';
const DEFAULT_LINE_HEIGHT = 1.6;
const DEFAULT_IS_DARK_MODE = true;

const FONT_FAMILIES = [
  { name: 'Atkinson Hyperlegible (Recommended)', value: 'font-atkinson' },
  { name: 'Sans Serif', value: 'font-sans' },
  { name: 'Serif', value: 'font-serif' },
  { name: 'Mono', value: 'font-mono' },
  { name: 'OpenDyslexic', value: 'font-opendyslexic' },
];

const FONT_SIZES = [
  { name: 'Small', value: 14 },
  { name: 'Medium', value: 16 },
  { name: 'Large', value: 18 },
  { name: 'Extra Large', value: 20 },
];

const LINE_HEIGHTS = [
  { name: 'Tight', value: 1.4 },
  { name: 'Normal', value: 1.6 },
  { name: 'Relaxed', value: 1.8 },
  { name: 'Loose', value: 2.0 },
];

export default function TextTools({
  fontSize,
  fontFamily,
  lineHeight,
  isDarkMode,
  onFontSizeChange,
  onFontFamilyChange,
  onLineHeightChange,
  onThemeToggle,
}: TextToolsProps) {
  const [isOpen, setIsOpen] = useState(false);

  const handleReset = () => {
    onFontSizeChange(DEFAULT_FONT_SIZE);
    onFontFamilyChange(DEFAULT_FONT_FAMILY);
    onLineHeightChange(DEFAULT_LINE_HEIGHT);
    if (isDarkMode !== DEFAULT_IS_DARK_MODE) {
      onThemeToggle();
    }
  };

  return (
    <>
      {/* Text Tools Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-3 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors text-sm"
        title="Text Tools"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
          <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
        </svg>
        <span className="hidden sm:inline">Text Tools</span>
      </button>

      {/* Modal */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4"
          onClick={() => setIsOpen(false)}
        >
          {/* Modal Content */}
          <div 
            className="relative w-full max-w-md bg-slate-800 border-4 border-slate-600/50 rounded-xl shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-slate-700">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center">
                  <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" strokeWidth={2}>
                    <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 11-3 0m3 0a1.5 1.5 0 10-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 01-3 0m3 0a1.5 1.5 0 00-3 0m-9.75 0h9.75" />
                  </svg>
                </div>
                <h3 className="text-lg font-semibold text-white">Text Tools</h3>
              </div>
              <button
                onClick={() => setIsOpen(false)}
                className="text-slate-400 hover:text-white transition-colors"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            {/* Content */}
            <div className="p-4 space-y-4">
              {/* Font Size */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">Text Size</label>
                <select
                  value={fontSize}
                  onChange={(e) => onFontSizeChange(Number(e.target.value))}
                  className="w-full px-4 py-4 bg-slate-700 border border-slate-600 rounded text-white text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation"
                >
                  {FONT_SIZES.map((size) => (
                    <option key={size.value} value={size.value}>
                      {size.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Font Family */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">Text Type</label>
                <select
                  value={fontFamily}
                  onChange={(e) => onFontFamilyChange(e.target.value)}
                  className="w-full px-4 py-4 bg-slate-700 border border-slate-600 rounded text-white text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation"
                >
                  {FONT_FAMILIES.map((font) => (
                    <option key={font.value} value={font.value}>
                      {font.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Line Height */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">Line Spacing</label>
                <select
                  value={lineHeight}
                  onChange={(e) => onLineHeightChange(Number(e.target.value))}
                  className="w-full px-4 py-4 bg-slate-700 border border-slate-600 rounded text-white text-base focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent touch-manipulation"
                >
                  {LINE_HEIGHTS.map((height) => (
                    <option key={height.value} value={height.value}>
                      {height.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Theme Toggle */}
              <div>
                <label className="block text-white text-sm font-medium mb-2">Reading Mode</label>
                <button
                  onClick={onThemeToggle}
                  className={`w-full px-4 py-2 rounded transition-colors flex items-center justify-center gap-2 ${
                    isDarkMode
                      ? 'bg-slate-700 text-gray-300 hover:bg-slate-600'
                      : 'bg-yellow-100 text-gray-800 hover:bg-yellow-200'
                  }`}
                >
                  {isDarkMode ? (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                      </svg>
                      Switch to Light Mode
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                      </svg>
                      Switch to Dark Mode
                    </>
                  )}
                </button>
              </div>

              {/* Reset Button */}
              <div className="pt-2 border-t border-slate-600">
                <button
                  onClick={handleReset}
                  className="w-full px-4 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors flex items-center justify-center gap-2 text-sm"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Reset to Defaults
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
} 