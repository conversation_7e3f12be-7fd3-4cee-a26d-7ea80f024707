import { Metadata } from 'next';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';

export const metadata: Metadata = {
  title: 'Terms of Service - MyStoryMaker',
  description: 'Terms of Service for MyStoryMaker - Creating magical stories for children through AI technology.',
};

export default function TermsPage() {
  return (
    <div className="min-h-screen">
      <Navigation currentPage="terms" />

      {/* Main Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="bg-slate-800/50 rounded-2xl p-8 md:p-12">
          <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white mb-6">Terms of Service</h1>
          <p className="text-gray-400 mb-12">Last updated: 5/25/2025</p>

          <div className="space-y-12 text-gray-300">
            {/* Section 1 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">1. Acceptance of Terms</h2>
              <p className="leading-relaxed">
                By accessing and using MyStoryMaker, you agree to be bound by these Terms of Service. If you do not agree with any part of these terms, please do not use our service.
              </p>
            </section>

            {/* Section 2 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">2. Description of Service</h2>
              <p className="leading-relaxed">
                MyStoryMaker is a platform that allows users to create, generate, and manage children's stories. Our service includes story generation, audio narration, and story management features.
              </p>
            </section>

            {/* Section 3 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">3. User Accounts</h2>
              <ul className="space-y-3 ml-6">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>You must be at least 18 years old to create an account</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>You are responsible for maintaining the confidentiality of your account</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>You must provide accurate and complete information when creating an account</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>You are responsible for all activities that occur under your account</span>
                </li>
              </ul>
            </section>

            {/* Section 4 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">4. Subscription and Payments</h2>
              <p className="leading-relaxed mb-4">
                We offer different subscription tiers with varying features and limitations:
              </p>
              <ul className="space-y-3 ml-6 mb-4">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Free tier: Limited to 3 stories per month with 2 image regenerations per story</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Starter tier ($9.99/month): Limited to 30 stories per month with 3 image regenerations per story, plus audio generation and PDF downloads</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Family tier ($19.99/month): Limited to 75 stories per month with 4 image regenerations per story, plus premium voices and word highlighting</span>
                </li>
              </ul>
              <p className="leading-relaxed">
                All payments are processed securely through Stripe. You can cancel your subscription at any time through your account settings.
              </p>
            </section>

            {/* Section 5 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">5. Content Guidelines</h2>
              <p className="leading-relaxed mb-4">
                When using our service, you agree not to:
              </p>
              <ul className="space-y-3 ml-6">
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Generate inappropriate or harmful content</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Violate any intellectual property rights</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Use the service for any illegal purposes</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Attempt to access unauthorized areas of the service</span>
                </li>
                <li className="flex items-start">
                  <span className="text-blue-400 mr-3 mt-1">•</span>
                  <span>Interfere with the proper working of the service</span>
                </li>
              </ul>
            </section>

            {/* Section 6 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">6. Intellectual Property</h2>
              <p className="leading-relaxed">
                The stories generated through our service are for personal use only. You retain rights to your story ideas and content, but the generated stories are subject to our terms of use and may not be used for commercial purposes without permission.
              </p>
            </section>

            {/* Section 7 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">7. Limitation of Liability</h2>
              <p className="leading-relaxed">
                MyStoryMaker is provided "as is" without any warranties. We are not liable for any damages arising from your use of our service.
              </p>
            </section>

            {/* Section 8 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">8. Changes to Terms</h2>
              <p className="leading-relaxed">
                We reserve the right to modify these terms at any time. We will notify users of any significant changes through our website or email.
              </p>
            </section>

            {/* Section 9 */}
            <section>
              <h2 className="text-2xl font-bold text-white mb-4">9. Contact Information</h2>
              <p className="leading-relaxed">
                If you have any questions about these Terms of Service, please contact us at:
              </p>
              <p className="leading-relaxed mt-2">
                Email: <EMAIL>
              </p>
            </section>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
} 