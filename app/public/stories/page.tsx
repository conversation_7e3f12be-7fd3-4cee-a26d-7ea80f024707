'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import PublicStoryCardWithVoting from './components/PublicStoryCardWithVoting';
import Pagination from '../../components/ui/Pagination';
import { usePublicStoryFiltering } from '../../hooks/story/useStoryFiltering';
import { usePagination } from '../../hooks/usePagination';

interface VoteData {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
}

interface PublicStory {
  id: number;
  story_uuid: string;
  title: string;
  main_character: string;
  setting: string;
  created_at: string;
  themes: string[];
  ageRange: string;
  hasImage: boolean;
  hasAudio: boolean;
  slug: string;
  excerpt: string;
  voteData?: VoteData;
}

interface Theme {
  id: string;
  name: string;
  description: string;
}

export default function PublicStoriesPage() {
  const [stories, setStories] = useState<PublicStory[]>([]);
  const [themes, setThemes] = useState<Theme[]>([]);
  const [ageRanges, setAgeRanges] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    searchQuery,
    setSearchQuery,
    selectedTheme,
    setSelectedTheme,
    selectedAgeRange,
    setSelectedAgeRange,
    selectedAudioFilter,
    setSelectedAudioFilter,
    filteredStories
  } = usePublicStoryFiltering(stories);

  const {
    currentPage,
    totalPages,
    hasNextPage,
    hasPreviousPage,
    goToPage,
    getPaginatedItems
  } = usePagination<PublicStory>({
    totalItems: filteredStories.length,
    itemsPerPage: 9
  });

  const paginatedStories = getPaginatedItems(filteredStories);

  // Reset to page 1 when filters change
  useEffect(() => {
    goToPage(1);
  }, [searchQuery, selectedTheme, selectedAgeRange, selectedAudioFilter, goToPage]);

  // Scroll to stories section when page changes
  useEffect(() => {
    if (currentPage > 1) {
      // Find the anchor above the stories grid and scroll to it
      const anchor = document.getElementById('stories-anchor');
      if (anchor) {
        anchor.scrollIntoView({ 
          behavior: 'smooth', 
          block: 'start' 
        });
      }
    }
  }, [currentPage]);

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        // Add cache busting parameter to ensure fresh data
        const cacheBuster = Date.now();
        const [storiesResponse, themesResponse] = await Promise.all([
          fetch(`/api/public/stories?t=${cacheBuster}`, {
            cache: 'no-store' // Prevent browser caching
          }),
          fetch(`/api/themes?t=${cacheBuster}`)
        ]);
        
        if (!storiesResponse.ok) {
          throw new Error('Failed to fetch stories');
        }
        
        const storiesData = await storiesResponse.json();
        setStories(storiesData);

        // Extract unique age ranges from stories
        const uniqueAgeRanges = [...new Set(storiesData.map((story: PublicStory) => story.ageRange))].filter(Boolean).sort();
        setAgeRanges(uniqueAgeRanges as string[]);

        if (themesResponse.ok) {
          const themesData = await themesResponse.json();
          setThemes(themesData);
        }
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load stories');
      } finally {
        setLoading(false);
        setIsLoadingData(false);
      }
    }

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-50" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>
      
      {/* Radial gradient accent */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-gradient-radial from-blue-500/10 to-transparent rounded-full blur-3xl"></div>
      
      <Navigation currentPage="public-stories" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 py-8 sm:py-12 relative z-10">
        <div className="mb-12">
          <div className="flex items-center justify-between mb-4">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white">Discover Stories</h1>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center gap-2 px-4 py-2 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors text-sm font-medium"
              title="Refresh stories"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <span className="hidden sm:inline">Refresh</span>
            </button>
          </div>
          <p className="text-gray-300 text-lg">Explore magical stories shared by our community</p>
        </div>

        {/* Search and filters */}
        <div className="mb-8 bg-gradient-to-br from-slate-800/80 to-slate-900/80 border border-slate-700/50 rounded-xl p-4">
          <div className="space-y-4">
            {/* All filters in one row for desktop, stacked on mobile */}
            <div className="flex flex-col lg:flex-row gap-3 items-stretch lg:items-center">
              {/* Search input - wider on desktop */}
              <div className="flex-1 lg:flex-[2] relative">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                <input
                  type="text"
                  placeholder="Search stories..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-4 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"
                />
              </div>

              {/* Filter dropdowns - 2x2 grid on mobile, single row on desktop */}
              <div className="grid grid-cols-2 lg:flex gap-3 lg:contents">
                {/* Theme filter - wider on desktop */}
                <div className="lg:flex-[1.5] lg:min-w-[180px] relative">
                  <div className="relative">
                    <select
                      value={selectedTheme}
                      onChange={(e) => setSelectedTheme(e.target.value)}
                      className="w-full px-3 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer transition-all duration-200 text-sm"
                      disabled={isLoadingData}
                    >
                      <option value="">All Themes</option>
                      {themes.map((theme) => (
                        <option key={theme.id} value={theme.description}>
                          {theme.description}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Age Range filter */}
                <div className="lg:flex-1 lg:min-w-[120px] relative">
                  <div className="relative">
                    <select
                      value={selectedAgeRange}
                      onChange={(e) => setSelectedAgeRange(e.target.value)}
                      className="w-full px-3 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer transition-all duration-200 text-sm"
                      disabled={isLoadingData}
                    >
                      <option value="">All Ages</option>
                      {ageRanges.map((ageRange) => (
                        <option key={ageRange} value={ageRange}>
                          {ageRange}
                        </option>
                      ))}
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Audio filter */}
                <div className="lg:flex-1 lg:min-w-[120px] relative">
                  <div className="relative">
                    <select
                      value={selectedAudioFilter}
                      onChange={(e) => setSelectedAudioFilter(e.target.value)}
                      className="w-full px-3 py-3 bg-slate-700/50 border border-slate-600/50 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none cursor-pointer transition-all duration-200 text-sm"
                      disabled={isLoadingData}
                    >
                      <option value="">All Stories</option>
                      <option value="with-audio">With Audio</option>
                      <option value="without-audio">Text Only</option>
                    </select>
                    <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                      <svg className="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </div>
                </div>

                {/* Clear filters button */}
                {(searchQuery || selectedTheme || selectedAgeRange || selectedAudioFilter) && (
                  <div className="col-span-2 lg:col-span-1 lg:flex-shrink-0">
                    <button
                      onClick={() => {
                        setSearchQuery('');
                        setSelectedTheme('');
                        setSelectedAgeRange('');
                        setSelectedAudioFilter('');
                      }}
                      className="w-full lg:w-auto px-4 py-3 bg-slate-700 hover:bg-slate-600 text-white rounded-lg transition-colors text-sm whitespace-nowrap"
                    >
                      Clear Filters
                    </button>
                  </div>
                )}
              </div>
            </div>

            {/* Filter results info */}
            {(searchQuery || selectedTheme || selectedAgeRange || selectedAudioFilter) && !loading && (
              <div className="bg-blue-600/20 border border-blue-500/30 px-3 py-2 rounded-lg">
                <span className="text-blue-300 text-sm font-medium">
                  {filteredStories.length > 0 ? (
                    <>
                      Showing {((currentPage - 1) * 9) + 1}-{Math.min(currentPage * 9, filteredStories.length)} of {filteredStories.length} stories
                    </>
                  ) : (
                    `No stories found out of ${stories.length} total`
                  )}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Loading State */}
        {loading && (
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-300">Loading stories...</p>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="text-center py-16">
            <div className="mb-6 w-16 h-16 mx-auto bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">Something went wrong</h3>
            <p className="text-gray-300 mb-8">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200 transform hover:scale-105"
            >
              Try Again
            </button>
          </div>
        )}

        {/* Invisible anchor for pagination scroll */}
        <div id="stories-anchor"></div>

        {/* Stories Grid */}
        {!loading && !error && (
          <>
            {filteredStories.length === 0 ? (
              <div className="text-center py-16">
                {stories.length === 0 ? (
                  <>
                    <div className="mb-6 w-20 h-20 mx-auto bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                      <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">No stories to discover yet!</h3>
                    <p className="text-gray-300 mb-8">Be the first to share a story with the community</p>
                    <Link href="/create">
                      <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-8 py-4 rounded-lg text-lg font-semibold transition-all duration-200 transform hover:scale-105">
                        Create Your First Story
                      </button>
                    </Link>
                  </>
                ) : (
                  <>
                    <div className="mb-6 w-16 h-16 mx-auto bg-gradient-to-br from-gray-500 to-gray-600 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    </div>
                    <h3 className="text-2xl font-bold text-white mb-4">No stories match your search</h3>
                    <p className="text-gray-300 mb-8">Try adjusting your search terms or filters</p>
                    <button
                      onClick={() => {
                        setSearchQuery('');
                        setSelectedTheme('');
                        setSelectedAgeRange('');
                        setSelectedAudioFilter('');
                      }}
                      className="bg-slate-700 hover:bg-slate-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors"
                    >
                      Clear Filters
                    </button>
                  </>
                )}
              </div>
                          ) : (
                <>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6" data-stories-grid>
                  {paginatedStories.map((story) => (
                    <PublicStoryCardWithVoting key={story.id} story={story} />
                  ))}
                </div>

                {/* Pagination */}
                <Pagination
                  currentPage={currentPage}
                  totalPages={totalPages}
                  onPageChange={goToPage}
                  hasNextPage={hasNextPage}
                  hasPreviousPage={hasPreviousPage}
                  className="mt-8"
                />
              </>
            )}

            {/* Call to Action */}
            <div className="mt-16 relative overflow-hidden rounded-2xl">
              <div className="absolute inset-0">
                <Image
                  src="/images/start_creating.webp"
                  alt="Start creating background"
                  fill
                  className="object-cover opacity-30"
                  sizes="100vw"
                />
                <div className="absolute inset-0 bg-black/40"></div>
              </div>
              <div className="relative z-10 text-center p-12 border border-slate-700/50 rounded-2xl">
                <h2 className="text-3xl font-bold text-white mb-4">Share Your Own Story</h2>
                <p className="text-gray-300 mb-8 text-lg">Create and share your magical stories with the community</p>
                <Link href="/create">
                  <button className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white px-10 py-4 rounded-lg text-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                    <span className="flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                      </svg>
                      Create Your Story
                    </span>
                  </button>
                </Link>
              </div>
            </div>
          </>
        )}
      </div>

      <Footer />
    </div>
  );
}