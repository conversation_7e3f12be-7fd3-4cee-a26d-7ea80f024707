import Navigation from '../../components/Navigation';
import Footer from '../../components/Footer';
import { PageLoadingSpinner } from '../../components/ui/LoadingSpinner';

export default function PublicStoriesLoading() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-50" style={{
        backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23334155' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='1'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
      }}></div>
      
      {/* Radial gradient accent */}
      <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-96 h-96 bg-gradient-radial from-blue-500/10 via-purple-500/5 to-transparent rounded-full blur-3xl"></div>
      
      <Navigation />
      
      <div className="max-w-7xl mx-auto px-4 py-12 relative z-10">
        <PageLoadingSpinner message="Loading stories..." />
      </div>
      
      <Footer />
    </div>
  );
} 