import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';
import { SubscriptionService } from '../../../../lib/services/subscriptionService';
import { query } from '../../../../lib/postgres-client';

export async function GET(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get story ID or UUID from query parameters if provided
    const url = new URL(request.url);
    const storyId = url.searchParams.get('storyId');
    const storyUuid = url.searchParams.get('storyUuid');

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Resolve story UUID to ID if needed
    let resolvedStoryId: number | undefined;
    if (storyId) {
      resolvedStoryId = parseInt(storyId);
    } else if (storyUuid) {
      const storyResult = await query(
        'SELECT id FROM stories WHERE story_uuid = $1 AND user_id = $2',
        [storyUuid, session.user.id]
      );
      
      if (storyResult.rows.length > 0) {
        resolvedStoryId = storyResult.rows[0].id;
      }
    }

    // Get user's image regeneration limits and current usage
    const regenerationCheck = await subscriptionService.canUserRegenerateImage(
      session.user.id, 
      resolvedStoryId
    );

    return NextResponse.json({
      canRegenerate: regenerationCheck.canRegenerate,
      currentCount: regenerationCheck.currentCount,
      limit: regenerationCheck.limit,
      planName: regenerationCheck.planName,
      remaining: regenerationCheck.remaining
    });

  } catch (error) {
    console.error('Image regeneration limits check error:', error);
    
    return NextResponse.json(
      { error: 'Failed to check image regeneration limits' }, 
      { status: 500 }
    );
  }
}