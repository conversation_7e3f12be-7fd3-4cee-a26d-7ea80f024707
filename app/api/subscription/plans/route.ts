import { NextResponse } from 'next/server';
import { SubscriptionService } from '../../../../lib/services/subscriptionService';

export async function GET() {
  try {
    const subscriptionService = new SubscriptionService();
    const plans = await subscriptionService.getAllPlans();

    return NextResponse.json(plans);
  } catch (error) {
    console.error('Error fetching subscription plans:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription plans' }, 
      { status: 500 }
    );
  }
} 