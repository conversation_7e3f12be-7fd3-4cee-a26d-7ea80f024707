import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { SubscriptionService } from '../../../../lib/services/subscriptionService';

export async function POST() {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;

    // Use our custom billing system to check current plan
    const subscriptionService = new SubscriptionService();
    const currentPlan = await subscriptionService.getUserPlan(userId);
    const hasPremium = await subscriptionService.hasPremiumFeatures(userId);

    return NextResponse.json({ 
      success: true, 
      message: 'Subscription synced from custom billing system',
      currentPlan: currentPlan,
      hasPremium: hasPremium,
      syncedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Subscription sync error:', error);
    return NextResponse.json(
      { error: 'Failed to sync subscription' }, 
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;

    const subscriptionService = new SubscriptionService();
    const currentPlan = await subscriptionService.getUserPlan(userId);
    const hasPremium = await subscriptionService.hasPremiumFeatures(userId);
    const subscription = await subscriptionService.getUserSubscription(userId);
    
    return NextResponse.json({
      userId: userId,
      currentPlan: currentPlan,
      hasPremium: hasPremium,
      subscription: subscription,
      planChecks: {
        hasStarter: currentPlan === 'starter',
        hasFamily: currentPlan === 'family',
        hasPremiumFeature: hasPremium
      },
      checkedAt: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error fetching subscription status:', error);
    return NextResponse.json(
      { error: 'Failed to fetch subscription status' }, 
      { status: 500 }
    );
  }
}