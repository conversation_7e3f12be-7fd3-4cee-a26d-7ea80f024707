import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { SubscriptionService } from '../../../../lib/services/subscriptionService';

export async function GET() {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;

    // Initialize subscription service
    const subscriptionService = new SubscriptionService();

    // Get user's subscription limits and current usage
    const subscriptionCheck = await subscriptionService.canUserCreateStory(userId);

    return NextResponse.json({
      canCreate: subscriptionCheck.canCreate,
      currentCount: subscriptionCheck.currentCount,
      limit: subscriptionCheck.limit,
      planName: subscriptionCheck.planName,
      remainingStories: subscriptionCheck.remainingStories
    });

  } catch (error) {
    console.error('Subscription limits check error:', error);
    
    return NextResponse.json(
      { error: 'Failed to check subscription limits' }, 
      { status: 500 }
    );
  }
}