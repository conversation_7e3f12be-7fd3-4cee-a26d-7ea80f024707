import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { SubscriptionService } from '../../../../lib/services/subscriptionService';

export async function GET() {
  try {
    const { error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const subscriptionService = new SubscriptionService();
    const premiumAccess = await subscriptionService.getPremiumFeatures();

    return NextResponse.json(premiumAccess);
  } catch (error) {
    console.error('Error checking premium feature access:', error);
    return NextResponse.json(
      { error: 'Failed to check premium feature access' }, 
      { status: 500 }
    );
  }
}