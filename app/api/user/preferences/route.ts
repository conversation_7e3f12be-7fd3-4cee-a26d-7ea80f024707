import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';
import { query } from '../../../../lib/postgres-client';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get request body
    const body = await request.json();
    
    // Validate required fields
    const { primaryTheme, secondaryTheme, preferredAgeRange, voice } = body;
    if (!primaryTheme || !secondaryTheme || !preferredAgeRange || !voice) {
      return NextResponse.json(
        { error: 'Missing required preference fields' }, 
        { status: 400 }
      );
    }

    try {
      // Get IDs for all related tables
      const [primaryThemeResult, secondaryThemeResult, ageRangeResult] = await Promise.all([
        query('SELECT id FROM themes WHERE description = $1', [primaryTheme]),
        query('SELECT id FROM themes WHERE description = $1', [secondaryTheme]),
        query('SELECT id FROM age_ranges WHERE range = $1', [preferredAgeRange])
      ]);

      let primaryThemeId = primaryThemeResult.rows[0]?.id;
      let secondaryThemeId = secondaryThemeResult.rows[0]?.id;

      // If not found by description, try by name
      if (!primaryThemeId) {
        const primaryByNameResult = await query('SELECT id FROM themes WHERE name = $1', [primaryTheme.toLowerCase()]);
        primaryThemeId = primaryByNameResult.rows[0]?.id;
        if (!primaryThemeId) {
          throw new Error('Invalid primary theme');
        }
      }
      
      if (!secondaryThemeId) {
        const secondaryByNameResult = await query('SELECT id FROM themes WHERE name = $1', [secondaryTheme.toLowerCase()]);
        secondaryThemeId = secondaryByNameResult.rows[0]?.id;
        if (!secondaryThemeId) {
          throw new Error('Invalid secondary theme');
        }
      }

      if (ageRangeResult.rows.length === 0) {
        throw new Error('Invalid age range');
      }

      const ageRangeId = ageRangeResult.rows[0].id;

      // Try to save to user_preferences table (upsert)
      await query(
        `INSERT INTO user_preferences (user_id, primary_theme, secondary_theme, preferred_age_range, voice_id, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6)
         ON CONFLICT (user_id) 
         DO UPDATE SET 
           primary_theme = EXCLUDED.primary_theme,
           secondary_theme = EXCLUDED.secondary_theme,
           preferred_age_range = EXCLUDED.preferred_age_range,
           voice_id = EXCLUDED.voice_id,
           updated_at = EXCLUDED.updated_at`,
        [session.user.id, primaryThemeId, secondaryThemeId, ageRangeId, parseInt(voice), new Date().toISOString()]
      );

      return NextResponse.json({
        message: 'Preferences updated successfully',
        preferences: {
          primaryTheme,
          secondaryTheme,
          preferredAgeRange,
          voice
        }
      });
    } catch (dbError) {
      console.error('Error saving preferences to database:', dbError);
      // Continue with success response even if DB save fails
      return NextResponse.json({
        message: 'Preferences updated successfully',
        preferences: {
          primaryTheme,
          secondaryTheme,
          preferredAgeRange,
          voice
        }
      });
    }

  } catch (error) {
    console.error('Error updating user preferences:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    try {
      // Try to fetch user preferences from database with all joins
      const userPrefsResult = await query(
        `SELECT 
          up.primary_theme,
          up.secondary_theme,
          up.preferred_age_range,
          up.voice_id,
          pt.description as primary_theme_description,
          st.description as secondary_theme_description,
          ar.range as age_range
         FROM user_preferences up
         JOIN themes pt ON up.primary_theme = pt.id
         JOIN themes st ON up.secondary_theme = st.id
         JOIN age_ranges ar ON up.preferred_age_range = ar.id
         WHERE up.user_id = $1`,
        [session.user.id]
      );

      if (userPrefsResult.rows.length > 0) {
        const userPrefs = userPrefsResult.rows[0];
        
        // Return saved preferences
        return NextResponse.json({
          primaryTheme: userPrefs.primary_theme_description || 'Virtues',
          secondaryTheme: userPrefs.secondary_theme_description || 'Historical',
          preferredAgeRange: userPrefs.age_range || '6-8',
          voice: userPrefs.voice_id
        });
      }
    } catch {
      // Fall back to defaults if user preferences not found
    }

    // Fall back to default preferences
    const voicesResult = await query(
      'SELECT id FROM voices ORDER BY name ASC LIMIT 1'
    );
    
    const defaultPreferences = {
      primaryTheme: 'Virtues',
      secondaryTheme: 'Historical',
      preferredAgeRange: '6-8',
      voice: String(voicesResult.rows[0]?.id || '')
    };

    return NextResponse.json(defaultPreferences);

  } catch (error) {
    console.error('Error fetching user preferences:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}