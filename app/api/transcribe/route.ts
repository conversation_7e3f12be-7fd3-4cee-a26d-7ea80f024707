import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../auth';
import { TranscriptionService } from '../../../lib/services/transcriptionService';
import { FeatureService } from '../../../lib/services/featureService';
import { Feature } from '../../../lib/types/features';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has transcription generation access
    const featureService = new FeatureService();
    const transcriptionAccess = await featureService.checkFeatureAccess(Feature.TRANSCRIPTION_GENERATION, session.user.id);
    
    if (!transcriptionAccess.hasAccess) {
      return NextResponse.json({ 
        error: 'Feature access required',
        message: 'Read Along transcription is only available for Family plan subscribers.',
        upgradeRequired: transcriptionAccess.upgradeRequired,
        requiredPlan: transcriptionAccess.requiredPlan
      }, { status: 403 });
    }

    // Get request body
    const body = await request.json();
    const { story_id, force_regenerate } = body;
    
    if (!story_id) {
      return NextResponse.json(
        { error: 'Story ID is required' }, 
        { status: 400 }
      );
    }

    // Initialize transcription service
    const transcriptionService = new TranscriptionService();

    // Check if transcription already exists and is complete (unless force regenerate)
    if (!force_regenerate) {
      const transcriptionStatus = await transcriptionService.getTranscriptionStatus(story_id, session.user.id);
      
      if (transcriptionStatus === 'complete') {
        const existingTranscription = await transcriptionService.getTranscription(story_id, session.user.id);
        if (existingTranscription) {
          return NextResponse.json({
            success: true,
            transcription: existingTranscription,
            message: 'Transcription already exists for this story'
          });
        }
      }

      if (transcriptionStatus === 'generating') {
        return NextResponse.json({
          success: false,
          error: 'Transcription is already being generated for this story'
        }, { status: 409 });
      }
    }

    // Set status to generating
    await transcriptionService.setTranscriptionStatus(story_id, session.user.id, 'generating');

    try {
      // Get the public audio URL for AssemblyAI
      const audioUrl = await transcriptionService.getPublicAudioUrl(story_id.toString(), session.user.id);

      // Generate transcription
      const transcriptionResult = await transcriptionService.transcribeAudio(audioUrl);

      // Save transcription to database (this sets status to 'complete')
      await transcriptionService.saveTranscription(story_id, session.user.id, transcriptionResult);

      return NextResponse.json({
        success: true,
        transcription: transcriptionResult
      });
    } catch (transcriptionError) {
      console.error('Transcription process error:', transcriptionError);
      // Set status to failed if transcription fails
      await transcriptionService.setTranscriptionStatus(story_id, session.user.id, 'failed');
      throw transcriptionError;
    }

  } catch (error) {
    console.error('Transcription API error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 