import { auth } from '../../../../auth';
import { query } from '@/lib/postgres-client';
import { downloadFile } from '@/lib/r2-storage-utils';

export async function GET(
  request: Request,
  { params }: { params: Promise<{ storyId: string }> }
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { storyId } = await params;

    // Verify user owns this story
    const storyResult = await query(
      'SELECT user_id FROM stories WHERE id = $1 AND user_id = $2',
      [storyId, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return new Response('Story not found', { status: 404 });
    }

    // Get audio file
    const audioResult = await query(
      'SELECT storage_path FROM audio WHERE story_id = $1 LIMIT 1',
      [storyId]
    );

    if (audioResult.rows.length === 0) {
      return new Response('Audio not found', { status: 404 });
    }

    const audioData = audioResult.rows[0];

    // Download the audio file from R2
    let fileData: Uint8Array;
    try {
      fileData = await downloadFile('audio', audioData.storage_path);
    } catch (error) {
      console.error('Error downloading audio from R2:', error);
      return new Response('Failed to download audio', { status: 500 });
    }

    // Handle range requests for proper audio streaming
    const range = request.headers.get('range');
    
    if (range) {
      // Parse range header
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileData.byteLength - 1;
      const chunkSize = (end - start) + 1;

      // For range requests, slice the Uint8Array
      const rangeData = fileData.slice(start, end + 1);

      return new Response(rangeData, {
        status: 206,
        headers: {
          'Content-Range': `bytes ${start}-${end}/${fileData.byteLength}`,
          'Accept-Ranges': 'bytes',
          'Content-Length': chunkSize.toString(),
          'Content-Type': 'audio/mpeg',
          'Cache-Control': 'private, max-age=3600'
        }
      });
    } else {
      // Return the entire file
      return new Response(fileData, {
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Length': fileData.byteLength.toString(),
          'Accept-Ranges': 'bytes',
          'Cache-Control': 'private, max-age=3600'
        }
      });
    }

  } catch (error) {
    console.error('Error serving audio:', error);
    return new Response('Internal server error', { status: 500 });
  }
} 