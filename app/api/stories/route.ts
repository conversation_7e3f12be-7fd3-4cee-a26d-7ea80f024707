import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { StoryService } from '../../../lib/services/storyService';
import { DatabaseService } from '../../../lib/services/databaseService';
import { ImageService } from '../../../lib/services/imageService';
import { SubscriptionService } from '../../../lib/services/subscriptionService';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const userId = session.user.id;

    // Get request body
    const body = await request.json();
    
    // Validate required fields
    const { main_character, setting, story_details, age_range } = body;
    if (!main_character || !setting || !story_details || !age_range) {
      return NextResponse.json(
        { error: 'Missing required fields' }, 
        { status: 400 }
      );
    }

    // Initialize services
    const storyService = new StoryService();
    const databaseService = new DatabaseService();
    const imageService = new ImageService();
    const subscriptionService = new SubscriptionService();

    // Check subscription limits
    const subscriptionCheck = await subscriptionService.canUserCreateStory(userId);
    
    if (!subscriptionCheck.canCreate) {
      return NextResponse.json({
        error: 'Story limit reached',
        details: {
          message: `Amazing work! You've crafted ${subscriptionCheck.currentCount} wonderful stories this month and reached your ${subscriptionCheck.planName} plan limit of ${subscriptionCheck.limit} stories. Your storytelling powers will refresh next month, or you can unlock unlimited creativity by upgrading your plan.`,
          currentCount: subscriptionCheck.currentCount,
          limit: subscriptionCheck.limit,
          planName: subscriptionCheck.planName,
          remainingStories: subscriptionCheck.remainingStories
        }
      }, { status: 403 });
    }

    // Prepare request for story generation
    const requestPayload = {
      mainCharacter: body.main_character,
      setting: body.setting,
      theme: body.themes,
      details: body.story_details,
      ageRange: body.age_range,
      tags: body.tags,
      user_id: userId
    };

    // Generate story
    const result = await storyService.generateStory(requestPayload);

    // Get age range ID
    const ageRangeId = await databaseService.getAgeRangeId(body.age_range);

    // Save story to database
    const storyData = {
      user_id: userId,
      title: result.title || 'Untitled Story',
      content: result.story || result.content || '',
      main_character: body.main_character,
      setting: body.setting,
      details: body.story_details,
      age_range: ageRangeId,
      created_at: new Date().toISOString()
    };

    const savedStory = await databaseService.saveStory(storyData);

    // Handle image upload if present
    let imagePath = null;
    if (result.image) {
      try {
        imagePath = await imageService.uploadImage(result.image, userId, savedStory.id);
        await databaseService.saveImageRecord(
          userId, 
          savedStory.id, 
          imagePath, 
          result.image_prompt, // Save the original image prompt from the API response
          undefined, // No user prompt for initial story generation
          true // Set as default since it's the first image
        );
      } catch (imageError) {
        console.error('Error processing image:', imageError);
        // Don't fail the entire request if image processing fails
      }
    }

    // Link themes to story
    if (body.themes && body.themes.length > 0) {
      try {
        await databaseService.linkStoryThemes(savedStory.id, body.themes);
      } catch (themeError) {
        console.error('Error linking themes:', themeError);
        // Don't fail the entire request if theme linking fails
      }
    }

    // Link tags to story
    if (body.tags && body.tags.length > 0) {
      try {
        await databaseService.linkStoryTags(savedStory.id, body.tags, userId);
      } catch (tagError) {
        console.error('Error linking tags:', tagError);
        // Don't fail the entire request if tag linking fails
      }
    }

    // Return the saved story
    return NextResponse.json({
      ...result,
      story_id: savedStory.id,
      database_id: savedStory.id,
      uuid: savedStory.story_uuid,
      image_path: imagePath
    });

  } catch (error) {
    console.error('Story creation error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}