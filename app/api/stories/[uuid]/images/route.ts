import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import { query } from '../../../../../lib/postgres-client';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // Verify user owns this story
    const storyResult = await query(
      'SELECT id, user_id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found or access denied' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get all images for this story
    const imagesResult = await query(
      'SELECT id, storage_path, "default", created_at FROM images WHERE story_id = $1 AND user_id = $2 AND (deleted IS NULL OR deleted = false) ORDER BY created_at DESC',
      [story.id, session.user.id]
    );

    const images = imagesResult.rows.map(image => ({
      id: image.id,
      storage_path: image.storage_path,
      default: image.default,
      created_at: image.created_at,
      url: `/api/images/story/${uuid}/image/${image.id}`
    }));

    return NextResponse.json({ images });

  } catch (error) {
    console.error('Story images API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    const { imageId } = await request.json();

    if (!imageId || isNaN(parseInt(imageId))) {
      return NextResponse.json({ error: 'Invalid image ID' }, { status: 400 });
    }

    // Verify story exists and belongs to user
    const storyResult = await query(
      'SELECT id, user_id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Verify image belongs to this story
    const imageResult = await query(
      'SELECT id FROM images WHERE id = $1 AND story_id = $2 AND (deleted IS NULL OR deleted = false)',
      [imageId, story.id]
    );

    if (imageResult.rows.length === 0) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    // Set all images for this story to non-default first (only non-deleted ones)
    await query(
      'UPDATE images SET "default" = false WHERE story_id = $1 AND (deleted IS NULL OR deleted = false)',
      [story.id]
    );

    // Set the specified image as default
    await query(
      'UPDATE images SET "default" = true WHERE id = $1',
      [imageId]
    );

    return NextResponse.json({
      success: true,
      message: 'Default image updated successfully'
    });

  } catch (error) {
    console.error('Error setting default image:', error);
    return NextResponse.json({ error: 'Failed to set default image' }, { status: 500 });
  }
}