import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../../auth';
import { query } from '../../../../../../lib/postgres-client';

interface RouteParams {
  params: Promise<{
    uuid: string;
    imageId: string;
  }>;
}

export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid, imageId } = await params;

    if (!imageId || isNaN(parseInt(imageId))) {
      return NextResponse.json({ error: 'Invalid image ID' }, { status: 400 });
    }

    // Verify story exists and belongs to user
    const storyResult = await query(
      'SELECT id, user_id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found or access denied' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get image details and verify it belongs to this story and user (only non-deleted images)
    const imageResult = await query(
      'SELECT id, storage_path, "default" FROM images WHERE id = $1 AND story_id = $2 AND user_id = $3 AND (deleted IS NULL OR deleted = false)',
      [parseInt(imageId), story.id, session.user.id]
    );

    if (imageResult.rows.length === 0) {
      return NextResponse.json({ error: 'Image not found or access denied' }, { status: 404 });
    }

    const image = imageResult.rows[0];

    // Check if this is the only non-deleted image for the story
    const imageCountResult = await query(
      'SELECT COUNT(*) as count FROM images WHERE story_id = $1 AND user_id = $2 AND (deleted IS NULL OR deleted = false)',
      [story.id, session.user.id]
    );

    const imageCount = parseInt(imageCountResult.rows[0].count);

    if (imageCount <= 1) {
      return NextResponse.json({ 
        error: 'Cannot delete the last image. Stories must have at least one image.' 
      }, { status: 400 });
    }

    // If this is the default image, set another non-deleted image as default first
    if (image.default) {
      const otherImageResult = await query(
        'SELECT id FROM images WHERE story_id = $1 AND user_id = $2 AND id != $3 AND (deleted IS NULL OR deleted = false) ORDER BY created_at DESC LIMIT 1',
        [story.id, session.user.id, parseInt(imageId)]
      );

      if (otherImageResult.rows.length > 0) {
        await query(
          'UPDATE images SET "default" = true WHERE id = $1',
          [otherImageResult.rows[0].id]
        );
      }
    }

    // Soft delete: Mark the image as deleted instead of removing it
    await query(
      'UPDATE images SET deleted = true, updated_at = NOW() WHERE id = $1',
      [parseInt(imageId)]
    );

    // Note: We don't delete from R2 storage for soft delete
    // The image file remains in storage but is marked as deleted in the database

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    });

  } catch (error) {
    console.error('Error deleting image:', error);
    return NextResponse.json({ error: 'Failed to delete image' }, { status: 500 });
  }
} 