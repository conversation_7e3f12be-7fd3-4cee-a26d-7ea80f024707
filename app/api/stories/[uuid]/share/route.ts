import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import { query } from '../../../../../lib/postgres-client';
import { Resend } from 'resend';
import { getBaseUrl } from '../../../../../lib/utils';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

// Create a new story share
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // Get request body
    const body = await request.json();
    const { type, recipient_email, expires_in_days = 30 } = body;

    // Validate share type
    if (!type || !['email', 'link', 'facebook'].includes(type)) {
      return NextResponse.json({ error: 'Invalid share type. Must be "email", "link", or "facebook"' }, { status: 400 });
    }

    // If email type, validate recipient email
    if (type === 'email' && (!recipient_email || !recipient_email.includes('@'))) {
      return NextResponse.json({ error: 'Valid recipient email is required for email shares' }, { status: 400 });
    }

    // First, verify the story exists and belongs to the user
    const storyResult = await query(
      'SELECT id, user_id, title, is_deleted, is_public FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Check if story is deleted
    if (story.is_deleted) {
      return NextResponse.json(
        { error: 'Cannot share deleted story' }, 
        { status: 400 }
      );
    }

    // Check if Facebook sharing requires public story
    if (type === 'facebook' && !story.is_public) {
      return NextResponse.json(
        { error: 'Story must be public to share on Facebook' }, 
        { status: 400 }
      );
    }

    // Get share type ID
    const shareTypeResult = await query(
      'SELECT id FROM share_types WHERE name = $1',
      [type]
    );

    if (shareTypeResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Invalid share type' }, 
        { status: 400 }
      );
    }

    const shareType = shareTypeResult.rows[0];

    // Calculate expiration date
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + expires_in_days);

    // Create the share record
    const shareResult = await query(
      `INSERT INTO story_shares (story_id, type_id, recipient_email, expires_at, created_by)
       VALUES ($1, $2, $3, $4, $5)
       RETURNING id, access_token, expires_at`,
      [
        story.id,
        shareType.id,
        type === 'email' ? recipient_email : null,
        expiresAt.toISOString(),
        session.user.id
      ]
    );

    if (shareResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Failed to create share' }, 
        { status: 500 }
      );
    }

    const shareRecord = shareResult.rows[0];

    // Generate the share URL
    const shareUrl = `${getBaseUrl()}/shared/${shareRecord.access_token}`;

    // If email type, send the email
    if (type === 'email') {
      try {
        // Use user name from session
        const senderName = session.user.name || session.user.email || 'Someone';
        
        await sendShareEmail(recipient_email, story.title, shareUrl, expiresAt, senderName);
      } catch (emailError) {
        console.error('Error sending share email:', emailError);
        // Don't fail the request if email fails, just log it
      }
    }

    return NextResponse.json({
      success: true,
      share_id: shareRecord.id,
      share_url: shareUrl,
      expires_at: shareRecord.expires_at,
      type: type,
      recipient_email: type === 'email' ? recipient_email : null
    });

  } catch (error) {
    console.error('Story share creation error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// Get shares for a story
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // Verify the story belongs to the user
    const storyResult = await query(
      'SELECT id, user_id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Get all shares for this story
    const sharesResult = await query(
      `SELECT 
        ss.id,
        ss.recipient_email,
        ss.access_token,
        ss.expires_at,
        ss.created_at,
        st.name as share_type_name
       FROM story_shares ss
       JOIN share_types st ON ss.type_id = st.id
       WHERE ss.story_id = $1 AND ss.created_by = $2
       ORDER BY ss.created_at DESC`,
      [story.id, session.user.id]
    );

    // Process shares to include share URLs
    const processedShares = sharesResult.rows.map((share: {
      id: number;
      share_type_name: string;
      recipient_email: string;
      access_token: string;
      expires_at: string;
      created_at: string;
    }) => ({
      id: share.id,
      type: share.share_type_name,
      recipient_email: share.recipient_email,
      share_url: `${getBaseUrl()}/shared/${share.access_token}`,
      expires_at: share.expires_at,
      created_at: share.created_at,
      is_expired: new Date(share.expires_at) < new Date()
    }));

    return NextResponse.json({
      shares: processedShares
    });

  } catch (error) {
    console.error('Story shares fetch error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// Delete a story share
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // Get share ID from query parameters
    const url = new URL(request.url);
    const shareId = url.searchParams.get('shareId');

    if (!shareId) {
      return NextResponse.json({ error: 'Share ID is required' }, { status: 400 });
    }

    // Verify the story belongs to the user and get story ID
    const storyResult = await query(
      'SELECT id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Delete the share (verify it belongs to the user and story)
    const deleteResult = await query(
      'DELETE FROM story_shares WHERE id = $1 AND story_id = $2 AND created_by = $3',
      [shareId, story.id, session.user.id]
    );

    if (deleteResult.rowCount === 0) {
      return NextResponse.json(
        { error: 'Share not found or access denied' }, 
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Share deleted successfully'
    });

  } catch (error) {
    console.error('Story share deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// Helper function to send share email
async function sendShareEmail(recipientEmail: string, storyTitle: string, shareUrl: string, expiresAt: Date, senderName: string) {
  const apiKey = process.env.RESEND_API_KEY;
  
  if (!apiKey) {
    throw new Error('Email service not configured');
  }

  try {
    const resend = new Resend(apiKey);
    
    const result = await resend.emails.send({
      from: 'MyStoryMaker <<EMAIL>>',
      to: [recipientEmail],
      subject: `${senderName} shared a story with you: "${storyTitle}"`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h2 style="color: #1e40af; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">
            You've received a shared story!
          </h2>
          <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <p><strong>${senderName}</strong> has shared the story "<strong>${storyTitle}</strong>" with you.</p>
            <p>Click the button below to read it:</p>
          </div>
          <div style="text-align: center; margin: 30px 0;">
            <a href="${shareUrl}" style="display: inline-block; background-color: #1e40af; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: bold;">
              Read Story
            </a>
          </div>
          <div style="background: #ffffff; padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              <strong>Important:</strong> This link will expire on ${expiresAt.toLocaleDateString()}.
            </p>
          </div>
          <hr style="margin: 24px 0; border: none; border-top: 1px solid #e5e7eb;">
          <p style="color: #666; font-size: 12px; text-align: center;">
            This email was sent from MyStoryMaker. If you didn't expect this email, you can safely ignore it.
          </p>
        </div>
      `
    });
    
    return result;
    
  } catch (emailError) {
    console.error('Share email sending failed:', emailError);
    throw emailError;
  }
} 