import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import { query } from '../../../../../lib/postgres-client';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    const body = await request.json();
    const { voteType } = body;

    if (!voteType || !['up', 'down'].includes(voteType)) {
      return NextResponse.json({ error: 'Invalid vote type. Must be "up" or "down"' }, { status: 400 });
    }

    // Check if story exists and is public
    const storyResult = await query(
      'SELECT id, is_public, user_id FROM stories WHERE story_uuid = $1 AND is_deleted = false',
      [uuid]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    if (!story.is_public) {
      return NextResponse.json({ error: 'Cannot vote on private stories' }, { status: 403 });
    }

    // Check if user has already voted
    const existingVoteResult = await query(
      'SELECT vote_type FROM story_votes WHERE story_id = $1 AND user_id = $2',
      [story.id, session.user.id]
    );

    const existingVote = existingVoteResult.rows[0];
    let result;

    if (existingVote) {
      if (existingVote.vote_type === voteType) {
        // Same vote type - remove the vote (toggle off)
        await query(
          'DELETE FROM story_votes WHERE story_id = $1 AND user_id = $2',
          [story.id, session.user.id]
        );

        result = { action: 'removed', voteType: null };
      } else {
        // Different vote type - update the vote
        await query(
          'UPDATE story_votes SET vote_type = $1 WHERE story_id = $2 AND user_id = $3',
          [voteType, story.id, session.user.id]
        );

        result = { action: 'updated', voteType };
      }
    } else {
      // No existing vote - create new vote
      await query(
        'INSERT INTO story_votes (story_id, user_id, vote_type) VALUES ($1, $2, $3)',
        [story.id, session.user.id, voteType]
      );

      result = { action: 'created', voteType };
    }

    // Get updated vote counts
    const voteCountsResult = await query(
      `SELECT 
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) as upvotes,
        COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as downvotes,
        COUNT(*) as total_votes,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) - COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as score
       FROM story_votes 
       WHERE story_id = $1`,
      [story.id]
    );

    const counts = voteCountsResult.rows[0] || { upvotes: 0, downvotes: 0, total_votes: 0, score: 0 };

    return NextResponse.json({
      ...result,
      counts: {
        upvotes: parseInt(counts.upvotes),
        downvotes: parseInt(counts.downvotes),
        totalVotes: parseInt(counts.total_votes),
        score: parseInt(counts.score)
      }
    });

  } catch (error) {
    console.error('Vote API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { uuid } = await params;

    // Get story to verify it exists and get the ID
    const storyResult = await query(
      'SELECT id FROM stories WHERE story_uuid = $1 AND is_deleted = false',
      [uuid]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get vote counts (public data)
    const voteCountsResult = await query(
      `SELECT 
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) as upvotes,
        COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as downvotes,
        COUNT(*) as total_votes,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) - COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as score
       FROM story_votes 
       WHERE story_id = $1`,
      [story.id]
    );

    const counts = voteCountsResult.rows[0] || { upvotes: 0, downvotes: 0, total_votes: 0, score: 0 };

    // Get user's vote if authenticated
    let userVote = null;
    const session = await auth();
    
    if (session?.user?.id) {
      const userVoteResult = await query(
        'SELECT vote_type FROM story_votes WHERE story_id = $1 AND user_id = $2',
        [story.id, session.user.id]
      );

      if (userVoteResult.rows.length > 0) {
        userVote = userVoteResult.rows[0].vote_type;
      }
    }

    return NextResponse.json({
      counts: {
        upvotes: parseInt(counts.upvotes),
        downvotes: parseInt(counts.downvotes),
        totalVotes: parseInt(counts.total_votes),
        score: parseInt(counts.score)
      },
      userVote
    });

  } catch (error) {
    console.error('Get votes API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
} 