import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';
import { query } from '../../../../lib/postgres-client';
import { dbCache } from '../../../../lib/utils/cache';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

// Update story properties (e.g., public/private status)
export async function PATCH(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // Get request body
    const body = await request.json();
    const { is_public } = body;

    if (typeof is_public !== 'boolean') {
      return NextResponse.json({ error: 'is_public must be a boolean' }, { status: 400 });
    }

    // First, verify the story exists and belongs to the user
    const storyResult = await query(
      'SELECT id, user_id, title, is_deleted FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Check if story is deleted
    if (story.is_deleted) {
      return NextResponse.json(
        { error: 'Cannot update deleted story' }, 
        { status: 400 }
      );
    }

    // Update the story
    await query(
      'UPDATE stories SET is_public = $1, updated_at = $2 WHERE id = $3 AND user_id = $4',
      [is_public, new Date().toISOString(), story.id, session.user.id]
    );

    // Invalidate cache when story visibility changes
    // This ensures the public stories API reflects the change immediately
    dbCache.invalidateStoryCache(uuid);
    dbCache.invalidateUserCache(session.user.id);

    return NextResponse.json({
      success: true,
      message: `Story ${is_public ? 'made public' : 'made private'} successfully`,
      story_id: story.id,
      is_public: is_public
    });

  } catch (error) {
    console.error('Story update error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// Soft delete a story
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;

    // First, verify the story exists and belongs to the user
    const storyResult = await query(
      'SELECT id, user_id, title, is_deleted FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Check if story is already deleted
    if (story.is_deleted) {
      return NextResponse.json(
        { error: 'Story is already deleted' }, 
        { status: 400 }
      );
    }

    // Perform soft delete by updating the record
    await query(
      'UPDATE stories SET is_deleted = true, deleted_at = $1 WHERE id = $2 AND user_id = $3',
      [new Date().toISOString(), story.id, session.user.id]
    );

    // Invalidate cache when story is deleted
    // This ensures the public stories API reflects the change immediately
    dbCache.invalidateStoryCache(uuid);
    dbCache.invalidateUserCache(session.user.id);

    return NextResponse.json({
      success: true,
      message: 'Story deleted successfully',
      story_id: story.id
    });

  } catch (error) {
    console.error('Story deletion error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 