import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import { query } from '../../../../../lib/postgres-client';
import { ImageRegenerationService } from '../../../../../lib/services/imageRegenerationService';
import { ImageService } from '../../../../../lib/services/imageService';
import { DatabaseService } from '../../../../../lib/services/databaseService';
import { SubscriptionService } from '../../../../../lib/services/subscriptionService';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { uuid } = await params;
    const body = await request.json();
    const { userPrompt } = body;

    if (!userPrompt || !userPrompt.trim()) {
      return NextResponse.json({ error: 'User prompt is required' }, { status: 400 });
    }

    // Check if image generation service is configured
    const hasImageRegenConfig = process.env.STORY_IMAGE_REGEN_API_ENDPOINT && process.env.STORY_IMAGE_REGEN_API_TOKEN;
    
    if (!hasImageRegenConfig) {
      console.error('Image generation service not configured - missing environment variables');
      return NextResponse.json({ 
        error: 'Image generation service is currently unavailable. Please try again later.' 
      }, { status: 503 });
    }

    // Verify story exists and belongs to user
    const storyResult = await query(
      'SELECT s.id, s.user_id, s.title, s.main_character, s.setting, s.age_range, ar.range as age_range_value FROM stories s LEFT JOIN age_ranges ar ON s.age_range = ar.id WHERE s.story_uuid = $1 AND s.user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found or access denied' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get the original prompt from the existing default image
    let originalPrompt = '';
    try {
      const imageResult = await query(
        'SELECT original_prompt FROM images WHERE story_id = $1 AND "default" = true AND (deleted IS NULL OR deleted = false) ORDER BY created_at DESC LIMIT 1',
        [story.id]
      );
      
      if (imageResult.rows.length > 0 && imageResult.rows[0].original_prompt) {
        originalPrompt = imageResult.rows[0].original_prompt;
      } else {
        // Fallback: create a basic prompt from story details if no original prompt exists
        originalPrompt = `Story: "${story.title}". Main character: ${story.main_character}. Setting: ${story.setting}. Age range: ${story.age_range_value || 'children'}. Style: children's book illustration, colorful, friendly, safe for kids.`;
      }
    } catch (promptError) {
      console.error('Error fetching original prompt:', promptError);
      // Fallback: create a basic prompt from story details
      originalPrompt = `Story: "${story.title}". Main character: ${story.main_character}. Setting: ${story.setting}. Age range: ${story.age_range_value || 'children'}. Style: children's book illustration, colorful, friendly, safe for kids.`;
    }

    // Check if user can regenerate images for this story
    const subscriptionService = new SubscriptionService();
    const regenerationCheck = await subscriptionService.canUserRegenerateImage(session.user.id, story.id);

    if (!regenerationCheck.canRegenerate) {
      return NextResponse.json({
        error: 'Image regeneration limit reached',
        details: {
          message: `You have reached your image regeneration limit for this story (${regenerationCheck.currentCount}/${regenerationCheck.limit}).`,
          currentCount: regenerationCheck.currentCount,
          limit: regenerationCheck.limit,
          planName: regenerationCheck.planName
        }
      }, { status: 403 });
    }

    try {
      // Generate the image using the regeneration service
      const imageRegenerationService = new ImageRegenerationService();
      
      // Combine the original prompt with the user's additional input
      const fullPrompt = `${originalPrompt} ${userPrompt.trim()}.`;
      
      const imageResult = await imageRegenerationService.regenerateImage(fullPrompt, story.age_range_value);

      // Upload the image
      const imageService = new ImageService();
      const imagePath = await imageService.uploadImage(imageResult.image, session.user.id, story.id);

      // Save image record to database
      const databaseService = new DatabaseService();
      await databaseService.saveImageRecord(
        session.user.id,
        story.id,
        imagePath,
        imageResult.image_prompt, // Original prompt from API
        userPrompt.trim(), // User's input prompt
        true // Set as default
      );



      // Construct the full image URL - fix the undefined issue
      const baseUrl = process.env.NEXT_PUBLIC_R2_PUBLIC_URL;
      let imageUrl;
      
      if (!baseUrl) {
        console.warn('NEXT_PUBLIC_R2_PUBLIC_URL is not defined, using relative path');
        // Use a relative path that the frontend can handle
        imageUrl = `/images/${imagePath}`;
      } else {
        imageUrl = `${baseUrl}/images/${imagePath}`;
      }

      const response = {
        success: true,
        message: 'Image regenerated successfully',
        imageUrl,
        storyId: story.id
      };

      return NextResponse.json(response);

    } catch (imageError) {
      console.error('ERROR in image regeneration process:', imageError);
      console.error('Error stack:', imageError instanceof Error ? imageError.stack : 'No stack trace');
      
      // Return specific error messages based on the error type
      if (imageError instanceof Error) {
        if (imageError.message.includes('Missing image regeneration API configuration')) {
          return NextResponse.json({ 
            error: 'Image generation service is not configured. Please try again later.' 
          }, { status: 503 });
        }
        
        if (imageError.message.includes('No authentication data defined on node')) {
          return NextResponse.json({ 
            error: 'Image generation service authentication error. Please contact support.' 
          }, { status: 503 });
        }
        
        if (imageError.message.includes('Failed to regenerate image')) {
          return NextResponse.json({ 
            error: 'Failed to generate image. Please try again with a different description.' 
          }, { status: 500 });
        }
      }
      
      return NextResponse.json({ 
        error: 'Failed to generate image. Please try again.' 
      }, { status: 500 });
    }

  } catch (error) {
    console.error('OUTER ERROR in regenerate-image endpoint:', error);
    console.error('Outer error stack:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json({ error: 'Failed to regenerate image' }, { status: 500 });
  }
} 