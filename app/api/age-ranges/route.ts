import { NextResponse } from 'next/server';
import { query } from '../../../lib/postgres-client';

export async function GET() {
  try {
    const result = await query('SELECT * FROM age_ranges ORDER BY range');

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error('Age ranges API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
} 