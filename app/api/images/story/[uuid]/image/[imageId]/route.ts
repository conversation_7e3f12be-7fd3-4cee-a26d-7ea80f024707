import { auth } from '../../../../../../../auth';
import { query } from '@/lib/postgres-client';
import { downloadFile } from '@/lib/r2-storage-utils';

interface RouteParams {
  params: Promise<{
    uuid: string;
    imageId: string;
  }>;
}

export async function GET(
  request: Request,
  { params }: RouteParams
) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return new Response('Unauthorized', { status: 401 });
    }

    const { uuid, imageId } = await params;

    // Verify user owns this story
    const storyResult = await query(
      'SELECT id, user_id FROM stories WHERE story_uuid = $1 AND user_id = $2',
      [uuid, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return new Response('Story not found', { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get specific image file
    const imageResult = await query(
      'SELECT storage_path FROM images WHERE id = $1 AND story_id = $2 AND user_id = $3 AND (deleted IS NULL OR deleted = false)',
      [parseInt(imageId), story.id, session.user.id]
    );

    if (imageResult.rows.length === 0) {
      return new Response('Image not found', { status: 404 });
    }

    const imageData = imageResult.rows[0];

    // Download the image file from R2
    let fileData: Uint8Array;
    try {
      fileData = await downloadFile('images', imageData.storage_path);
    } catch (error) {
      console.error('Error downloading image from R2:', error);
      return new Response('Failed to download image', { status: 500 });
    }

    return new Response(fileData, {
      headers: {
        'Content-Type': 'image/webp',
        'Content-Length': fileData.byteLength.toString(),
        'Cache-Control': 'public, max-age=31536000, immutable',
        'ETag': `"${uuid}-${imageId}"`,
        'Last-Modified': new Date().toUTCString()
      }
    });

  } catch (error) {
    console.error('Error serving image:', error);
    return new Response('Internal server error', { status: 500 });
  }
}