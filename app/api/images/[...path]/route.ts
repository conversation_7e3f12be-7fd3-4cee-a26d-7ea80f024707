import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';
import { downloadFile } from '../../../../lib/r2-storage-utils';
import { query } from '../../../../lib/postgres-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const session = await auth();
    const { path: pathArray } = await params;
    const path = pathArray.join('/');
    
    // Extract user ID from path (format: user_xxx/storyId/image_xxx.webp)
    const pathParts = path.split('/');
    if (pathParts.length < 3) {
      return NextResponse.json({ error: 'Invalid image path' }, { status: 400 });
    }
    
    const imageOwnerUserId = pathParts[0];
    const storyId = parseInt(pathParts[1]);
    
    // If user is authenticated and owns the image, allow access
    if (session?.user?.id && path.startsWith(session.user.id)) {
      return await serveImage(path);
    }
    
    // For unauthenticated users or users accessing others' images,
    // check if the story is public
    if (!isNaN(storyId)) {
      try {
        const storyResult = await query(
          'SELECT is_public, user_id FROM stories WHERE id = $1 AND is_deleted = false',
          [storyId]
        );
        
        if (storyResult.rows.length > 0) {
          const story = storyResult.rows[0];
          
          // Allow access if story is public and the image belongs to the story owner
          if (story.is_public && imageOwnerUserId === story.user_id) {
            return await serveImage(path);
          }
        }
      } catch (dbError) {
        console.error('Error checking story publicity:', dbError);
      }
    }
    
    // If we get here, access is denied
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });

  } catch (error) {
    console.error('Error serving image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

async function serveImage(path: string): Promise<NextResponse> {
  // Download the image from R2 storage
  let data: Uint8Array;
  try {
    data = await downloadFile('images', path);
  } catch (error) {
    console.error('Error downloading image:', error);
    return NextResponse.json({ error: 'Image not found' }, { status: 404 });
  }
  
  // Determine content type based on file extension
  let contentType = 'image/webp';
  if (path.endsWith('.jpeg') || path.endsWith('.jpg')) {
    contentType = 'image/jpeg';
  } else if (path.endsWith('.png')) {
    contentType = 'image/png';
  }

  // Return the image with proper headers
  return new NextResponse(data, {
    headers: {
      'Content-Type': contentType,
      'Cache-Control': 'public, max-age=31536000, immutable',
    },
  });
} 