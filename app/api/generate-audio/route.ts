import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../auth';
import { query } from '../../../lib/postgres-client';
import { AudioService } from '../../../lib/services/audioService';
import { SubscriptionService } from '../../../lib/services/subscriptionService';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has premium features access
    const subscriptionService = new SubscriptionService();
    const premiumAccess = await subscriptionService.getPremiumFeatures();
    
    if (!premiumAccess.hasAccess) {
      return NextResponse.json({ 
        error: 'Premium feature access required',
        message: 'Audio generation is only available for Starter and Family plan subscribers.',
        upgradeRequired: true,
        currentPlan: premiumAccess.planName
      }, { status: 403 });
    }

    // Get request body
    const body = await request.json();
    const { story_id, force_regenerate } = body;
    
    if (!story_id) {
      return NextResponse.json(
        { error: 'Story ID is required' }, 
        { status: 400 }
      );
    }

    // Fetch the story from database
    const storyResult = await query(
      'SELECT id, title, content, user_id FROM stories WHERE id = $1 AND user_id = $2',
      [story_id, session.user.id]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or access denied' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Initialize audio service
    const audioService = new AudioService();

    // Check if audio already exists (unless force regenerate)
    if (!force_regenerate) {
      const existingAudioUrl = await audioService.checkExistingAudio(story_id, session.user.id);
      if (existingAudioUrl) {
        return NextResponse.json({
          success: true,
          audio_url: existingAudioUrl,
          message: 'Audio already exists for this story'
        });
      }
    }

    // Generate audio
    const requestPayload = {
      title: story.title,
      text: story.content
    };

    const audioResponse = await audioService.generateAudio(requestPayload);

    // Upload audio and save record
    const storagePath = await audioService.uploadAudio(audioResponse.audio, session.user.id, story_id);
    
    try {
      await audioService.saveAudioRecord(session.user.id, story_id, storagePath);
    } catch (recordError) {
      console.error('Failed to save audio record:', recordError);
      // Don't fail the entire request if audio record save fails
      // The file is already uploaded successfully
    }

    // Return the audio URL
    const audioUrl = audioService.getAudioUrl(story_id);
    
    return NextResponse.json({
      success: true,
      audio_url: audioUrl
    });

  } catch (error) {
    console.error('Audio generation error:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 