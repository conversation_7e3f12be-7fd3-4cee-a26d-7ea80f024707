import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../auth';
import { query } from '../../../lib/postgres-client';

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const result = await query(
      'SELECT * FROM tags WHERE user_id = $1 ORDER BY created_at DESC',
      [session.user.id]
    );

    return NextResponse.json(result.rows);

  } catch (error) {
    console.error('Tags API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { name: tagText } = await request.json();

    if (!tagText || typeof tagText !== 'string') {
      return NextResponse.json(
        { error: 'Tag text is required and must be a string' }, 
        { status: 400 }
      );
    }

    const result = await query(
      'INSERT INTO tags (name, user_id, created_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING *',
      [tagText.trim(), session.user.id, new Date().toISOString(), new Date().toISOString()]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Failed to create tag' }, 
        { status: 500 }
      );
    }

    return NextResponse.json(result.rows[0], { status: 201 });

  } catch (error) {
    console.error('Tags API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id, name: tagText } = await request.json();

    if (!id || !tagText || typeof tagText !== 'string') {
      return NextResponse.json(
        { error: 'ID and tag text are required' }, 
        { status: 400 }
      );
    }

    const result = await query(
      'UPDATE tags SET name = $1, updated_at = $2 WHERE id = $3 AND user_id = $4 RETURNING *',
      [tagText.trim(), new Date().toISOString(), id, session.user.id]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { error: 'Tag not found or unauthorized' }, 
        { status: 404 }
      );
    }

    return NextResponse.json(result.rows[0]);

  } catch (error) {
    console.error('Tags API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'Tag ID is required' }, 
        { status: 400 }
      );
    }

    const result = await query(
      'DELETE FROM tags WHERE id = $1 AND user_id = $2',
      [id, session.user.id]
    );

    if (result.rowCount === 0) {
      return NextResponse.json(
        { error: 'Tag not found or unauthorized' }, 
        { status: 404 }
      );
    }

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Tags API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}