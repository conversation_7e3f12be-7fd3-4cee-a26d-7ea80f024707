import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../auth';
import { dbCache } from '../../../../lib/utils/cache';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated (optional - you might want to add admin check)
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { type, key } = body;

    if (type === 'all') {
      // Clear all cache
      dbCache.clearAll();
      return NextResponse.json({ 
        success: true, 
        message: 'All cache cleared successfully' 
      });
    } else if (type === 'story' && key) {
      // Clear specific story cache
      dbCache.invalidateStoryCache(key);
      return NextResponse.json({ 
        success: true, 
        message: `Story cache cleared for: ${key}` 
      });
    } else if (type === 'user' && key) {
      // Clear user-specific cache
      dbCache.invalidateUserCache(key);
      return NextResponse.json({ 
        success: true, 
        message: `User cache cleared for: ${key}` 
      });
    } else {
      return NextResponse.json({ 
        error: 'Invalid cache purge request. Use type: "all", "story", or "user" with appropriate key' 
      }, { status: 400 });
    }

  } catch (error) {
    console.error('Cache purge error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}

// GET endpoint to check cache stats
export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const stats = dbCache.getStats();
    return NextResponse.json({
      success: true,
      cache_stats: stats
    });

  } catch (error) {
    console.error('Cache stats error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 