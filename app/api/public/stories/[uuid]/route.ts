import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../../../lib/postgres-client';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

// Get public story by UUID (no authentication required)
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { uuid } = await params;

    if (!uuid) {
      return NextResponse.json({ error: 'Invalid story UUID' }, { status: 400 });
    }

    // Fetch story with related themes and age range, ensuring it's public and not deleted
    const storyResult = await query(
      `SELECT 
        s.*,
        ar.range as age_range
       FROM stories s
       LEFT JOIN age_ranges ar ON s.age_range = ar.id
       WHERE s.story_uuid = $1 AND s.is_public = true AND s.is_deleted = false`,
      [uuid]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Story not found or not public' }, 
        { status: 404 }
      );
    }

    const story = storyResult.rows[0];

    // Get themes for this story
    const themesResult = await query(
      `SELECT t.description
       FROM stories_themes st
       JOIN themes t ON st.theme_id = t.id
       WHERE st.story_id = $1`,
      [story.id]
    );

    const themes = themesResult.rows.map(row => row.description);

    // Get the default image for this story
    const imageResult = await query(
      'SELECT storage_path FROM images WHERE story_id = $1 AND "default" = true AND (deleted IS NULL OR deleted = false)',
      [story.id]
    );

    const hasImage = imageResult.rows.length > 0;

    // Check if audio exists for this story and get transcription info
    const audioResult = await query(
      `SELECT 
        a.storage_path, 
        a.transcription_text, 
        a.transcription_words,
        ts.status as transcription_status
       FROM audio a
       LEFT JOIN transcription_status ts ON a.transcription_status = ts.id
       WHERE a.story_id = $1`,
      [story.id]
    );

    let hasAudio = false;
    let hasTranscription = false;
    let transcriptionStatus = 'not_started';

    if (audioResult.rows.length > 0) {
      const audioData = audioResult.rows[0];
      hasAudio = true;
      transcriptionStatus = audioData.transcription_status || 'not_started';
      hasTranscription = transcriptionStatus === 'complete' && !!(audioData.transcription_text && audioData.transcription_words);
    }

    return NextResponse.json({
      id: story.id,
      title: story.title,
      content: story.content,
      mainCharacter: story.main_character,
      setting: story.setting,
      themes: themes,
      ageRange: story.age_range || 'Unknown',
      details: story.details,
      createdAt: story.created_at,
      hasImage: hasImage,
      hasAudio: hasAudio,
      hasTranscription: hasTranscription,
      transcriptionStatus: transcriptionStatus
    });

  } catch (error) {
    console.error('Public story API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 