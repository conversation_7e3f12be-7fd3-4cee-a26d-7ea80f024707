import { NextResponse } from 'next/server';
import { query } from '../../../../../lib/postgres-client';

// Function to create URL-friendly slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Function to shuffle array and return first n items
function getRandomItems<T>(array: T[], count: number): T[] {
  const shuffled = [...array].sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'https://mystorymaker.app',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}

export async function GET() {
  try {
    // Fetch all public stories with related themes and age range
    const storiesResult = await query(`
      SELECT 
        s.*,
        ar.range as age_range
      FROM stories s
      LEFT JOIN age_ranges ar ON s.age_range = ar.id
      WHERE s.is_public = true AND s.is_deleted = false
      ORDER BY s.created_at DESC
    `);

    if (storiesResult.rows.length === 0) {
      return NextResponse.json([]);
    }

    // Randomly select up to 3 stories
    const selectedStories = getRandomItems(storiesResult.rows, 3);

    // Get themes for selected stories in batch
    const storyIds = selectedStories.map(story => story.id);
    const themesResult = await query(`
      SELECT 
        st.story_id,
        t.description
      FROM stories_themes st
      JOIN themes t ON st.theme_id = t.id
      WHERE st.story_id = ANY($1)
    `, [storyIds]);

    // Get images for selected stories in batch
    const imagesResult = await query(`
      SELECT story_id, storage_path
      FROM images
      WHERE story_id = ANY($1) AND "default" = true
    `, [storyIds]);

    // Get audio for selected stories in batch
    const audioResult = await query(`
      SELECT story_id, storage_path
      FROM audio
      WHERE story_id = ANY($1)
    `, [storyIds]);

    // Get vote counts for selected stories in batch
    const voteCountsResult = await query(`
      SELECT 
        story_id,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) as upvotes,
        COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as downvotes,
        COUNT(*) as total_votes,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) - COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as score
      FROM story_votes
      WHERE story_id = ANY($1)
      GROUP BY story_id
    `, [storyIds]);

    // Create lookup maps for efficient processing
    const themesMap = new Map<number, string[]>();
    themesResult.rows.forEach(row => {
      if (!themesMap.has(row.story_id)) {
        themesMap.set(row.story_id, []);
      }
      themesMap.get(row.story_id)!.push(row.description);
    });

    const imagesMap = new Map<number, boolean>();
    imagesResult.rows.forEach(row => {
      imagesMap.set(row.story_id, true);
    });

    const audioMap = new Map<number, boolean>();
    audioResult.rows.forEach(row => {
      audioMap.set(row.story_id, true);
    });

    const voteCountsMap = new Map<number, { upvotes: number; downvotes: number; totalVotes: number; score: number }>();
    voteCountsResult.rows.forEach((row: { story_id: number; upvotes: string; downvotes: string; total_votes: string; score: string }) => {
      voteCountsMap.set(row.story_id, {
        upvotes: parseInt(row.upvotes) || 0,
        downvotes: parseInt(row.downvotes) || 0,
        totalVotes: parseInt(row.total_votes) || 0,
        score: parseInt(row.score) || 0
      });
    });

    // Process selected stories and add metadata
    const processedStories = selectedStories.map((story) => {
      const themes = themesMap.get(story.id) || [];
      const hasImage = imagesMap.has(story.id);
      const hasAudio = audioMap.has(story.id);
      const voteCounts = voteCountsMap.get(story.id) || { upvotes: 0, downvotes: 0, totalVotes: 0, score: 0 };

      return {
        id: story.id,
        story_uuid: story.story_uuid,
        title: story.title,
        main_character: story.main_character,
        setting: story.setting,
        created_at: story.created_at,
        themes: themes,
        ageRange: story.age_range || 'Unknown',
        hasImage: hasImage,
        hasAudio: hasAudio,
        slug: createSlug(story.title),
        excerpt: story.content ? story.content.substring(0, 200) + '...' : '',
        voteData: {
          counts: voteCounts
        }
      };
    });

    const response = NextResponse.json(processedStories);
    
    // Add cache control headers to prevent excessive caching by Cloudflare
    // Cache for 2 minutes since featured stories change less frequently
    response.headers.set('Cache-Control', 'public, max-age=120, s-maxage=120, stale-while-revalidate=600');
    response.headers.set('CDN-Cache-Control', 'max-age=120');
    response.headers.set('Vercel-CDN-Cache-Control', 'max-age=120');
    
    // Add CORS headers
    response.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;

  } catch (error) {
    console.error('Featured stories API error:', error);
    const errorResponse = NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
    // Don't cache error responses
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    // Add CORS headers to error responses too
    errorResponse.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    errorResponse.headers.set('Access-Control-Allow-Credentials', 'true');
    return errorResponse;
  }
}