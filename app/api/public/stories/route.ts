import { NextResponse } from 'next/server';
import { query } from '../../../../lib/postgres-client';

// Function to create URL-friendly slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

// Handle preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'https://mystorymaker.app',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}

export async function GET() {
  try {
    // Fetch public stories with age ranges
    const storiesResult = await query(`
      SELECT s.*, ar.range as age_range
      FROM stories s
      LEFT JOIN age_ranges ar ON s.age_range = ar.id
      WHERE s.is_public = true AND s.is_deleted = false
      ORDER BY s.created_at DESC
      LIMIT 50
    `);

    if (storiesResult.rows.length === 0) {
      const response = NextResponse.json([]);
      // Add cache control headers to prevent excessive caching
      response.headers.set('Cache-Control', 'public, max-age=60, s-maxage=60, stale-while-revalidate=300');
      response.headers.set('CDN-Cache-Control', 'max-age=60');
      response.headers.set('Vercel-CDN-Cache-Control', 'max-age=60');
      // Add CORS headers
      response.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
      response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
      response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
      response.headers.set('Access-Control-Allow-Credentials', 'true');
      return response;
    }

    const storyIds = storiesResult.rows.map((story: { id: number }) => story.id);

    // Get themes for all stories
    const themesResult = await query(`
      SELECT st.story_id, t.description
      FROM stories_themes st
      JOIN themes t ON st.theme_id = t.id
      WHERE st.story_id = ANY($1)
    `, [storyIds]);

    // Get images for all stories
    const imagesResult = await query(`
      SELECT story_id
      FROM images
      WHERE story_id = ANY($1) AND "default" = true
    `, [storyIds]);

    // Get audio for all stories
    const audioResult = await query(`
      SELECT story_id
      FROM audio
      WHERE story_id = ANY($1)
    `, [storyIds]);

    // Get vote counts for all stories
    const voteCountsResult = await query(`
      SELECT 
        story_id,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) as upvotes,
        COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as downvotes,
        COUNT(*) as total_votes,
        COUNT(CASE WHEN vote_type = 'up' THEN 1 END) - COUNT(CASE WHEN vote_type = 'down' THEN 1 END) as score
      FROM story_votes
      WHERE story_id = ANY($1)
      GROUP BY story_id
    `, [storyIds]);

    // Group data by story_id
    const themesByStory = new Map<number, string[]>();
    const imagesByStory = new Set<number>();
    const audioByStory = new Set<number>();
    const votesByStory = new Map<number, { upvotes: number; downvotes: number; totalVotes: number; score: number }>();

    themesResult.rows.forEach((theme: { story_id: number; description: string }) => {
      if (!themesByStory.has(theme.story_id)) {
        themesByStory.set(theme.story_id, []);
      }
      themesByStory.get(theme.story_id)?.push(theme.description);
    });

    imagesResult.rows.forEach((image: { story_id: number }) => {
      imagesByStory.add(image.story_id);
    });

    audioResult.rows.forEach((audio: { story_id: number }) => {
      audioByStory.add(audio.story_id);
    });

    voteCountsResult.rows.forEach((vote: { story_id: number; upvotes: string; downvotes: string; total_votes: string; score: string }) => {
      votesByStory.set(vote.story_id, {
        upvotes: parseInt(vote.upvotes) || 0,
        downvotes: parseInt(vote.downvotes) || 0,
        totalVotes: parseInt(vote.total_votes) || 0,
        score: parseInt(vote.score) || 0
      });
    });

    // Process stories and add metadata
    const processedStories = storiesResult.rows.map((story: {
      id: number;
      story_uuid: string;
      title: string;
      main_character: string;
      setting: string;
      created_at: string;
      age_range: string;
      content: string;
    }) => {
      const themes = themesByStory.get(story.id) || [];
      const hasImage = imagesByStory.has(story.id);
      const hasAudio = audioByStory.has(story.id);
      const voteData = votesByStory.get(story.id) || {
        upvotes: 0,
        downvotes: 0,
        totalVotes: 0,
        score: 0
      };

      return {
        id: story.id,
        story_uuid: story.story_uuid,
        title: story.title,
        main_character: story.main_character,
        setting: story.setting,
        created_at: story.created_at,
        themes: themes,
        ageRange: story.age_range || 'Unknown',
        hasImage: hasImage,
        hasAudio: hasAudio,
        slug: createSlug(story.title),
        excerpt: story.content ? story.content.substring(0, 200) + '...' : '',
        voteData: {
          counts: voteData
        }
      };
    });

    const response = NextResponse.json(processedStories);
    
    // Add cache control headers to prevent excessive caching by Cloudflare
    // Cache for 1 minute, allow stale content for 5 minutes while revalidating
    response.headers.set('Cache-Control', 'public, max-age=60, s-maxage=60, stale-while-revalidate=300');
    response.headers.set('CDN-Cache-Control', 'max-age=60');
    response.headers.set('Vercel-CDN-Cache-Control', 'max-age=60');
    
    // Add CORS headers
    response.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;

  } catch (error) {
    console.error('Public stories API error:', error);
    const errorResponse = NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
    // Don't cache error responses
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    return errorResponse;
  }
} 