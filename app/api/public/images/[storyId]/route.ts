import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/postgres-client';
import { downloadFile } from '@/lib/r2-storage-utils';

interface RouteParams {
  params: Promise<{
    storyId: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { storyId } = await params;

    if (!storyId) {
      return NextResponse.json({ error: 'Invalid story ID' }, { status: 400 });
    }

    // First, verify the story exists and is public
    const storyResult = await query(
      'SELECT id, is_public, is_deleted FROM stories WHERE id = $1 AND is_public = true AND is_deleted = false',
      [storyId]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found or not public' }, { status: 404 });
    }

    // Get the default image for this story
    const imageResult = await query(
      'SELECT storage_path FROM images WHERE story_id = $1 AND "default" = true AND (deleted IS NULL OR deleted = false)',
      [storyId]
    );

    if (imageResult.rows.length === 0) {
      return NextResponse.json({ error: 'Image not found' }, { status: 404 });
    }

    const imageData = imageResult.rows[0];

    // Get the file from R2 Storage
    let file: Uint8Array;
    try {
      file = await downloadFile('images', imageData.storage_path);
    } catch (error) {
      console.error('Error downloading image file from R2:', error);
      return NextResponse.json({ error: 'Image file not accessible' }, { status: 404 });
    }

    // Return the image file with appropriate headers
    return new NextResponse(file, {
      status: 200,
      headers: {
        'Content-Type': 'image/webp',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Disposition': `inline; filename="story-image-${storyId}.webp"`,
        // Additional headers for better social media crawler support
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET',
        'Access-Control-Allow-Headers': 'Content-Type',
        // Facebook crawler specific headers
        'X-Content-Type-Options': 'nosniff',
        'X-Frame-Options': 'SAMEORIGIN',
      },
    });

  } catch (error) {
    console.error('Public image API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 