import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../../../lib/postgres-client';
import { downloadFile } from '../../../../../lib/r2-storage-utils';

interface RouteParams {
  params: Promise<{
    uuid: string;
  }>;
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { uuid } = await params;

    if (!uuid) {
      return NextResponse.json({ error: 'Invalid story UUID' }, { status: 400 });
    }

    // First, get the story by UUID to get the numeric ID and verify it's public
    const storyResult = await query(
      'SELECT id, is_public, is_deleted FROM stories WHERE story_uuid = $1 AND is_public = true AND is_deleted = false',
      [uuid]
    );

    if (storyResult.rows.length === 0) {
      return NextResponse.json({ error: 'Story not found or not public' }, { status: 404 });
    }

    const story = storyResult.rows[0];

    // Get the audio file using the numeric ID
    const audioResult = await query(
      'SELECT storage_path FROM audio WHERE story_id = $1',
      [story.id]
    );

    if (audioResult.rows.length === 0) {
      return NextResponse.json({ error: 'Audio not found' }, { status: 404 });
    }

    const audioData = audioResult.rows[0];

    // Get the file from R2 Storage
    let file: Uint8Array;
    try {
      file = await downloadFile('audio', audioData.storage_path);
    } catch (error) {
      console.error('Error downloading audio file from R2:', error);
      return NextResponse.json({ error: 'Audio file not accessible' }, { status: 404 });
    }

    // Return the audio file with appropriate headers
    return new NextResponse(file, {
      status: 200,
      headers: {
        'Content-Type': 'audio/mpeg',
        'Cache-Control': 'public, max-age=31536000', // Cache for 1 year
        'Content-Disposition': `inline; filename="story-audio-${story.id}.mp3"`,
      },
    });

  } catch (error) {
    console.error('Public audio API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
}