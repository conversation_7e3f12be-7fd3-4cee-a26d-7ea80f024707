import { NextRequest, NextResponse } from 'next/server';
import { auth } from '../../../../../auth';
import { TranscriptionService } from '../../../../../lib/services/transcriptionService';
import { FeatureService } from '../../../../../lib/services/featureService';
import { Feature } from '../../../../../lib/types/features';
import { query } from '../../../../../lib/postgres-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ storyId: string }> }
) {
  try {
    const session = await auth();
    const { storyId } = await params;
    const storyIdInt = parseInt(storyId, 10);

    if (isNaN(storyIdInt)) {
      return NextResponse.json({ error: 'Invalid story ID' }, { status: 400 });
    }

    // Check for share token in query params
    const url = new URL(request.url);
    const shareToken = url.searchParams.get('token');

    let authorizedUserId: string;

    // If no user but share token provided, validate the share token
    if (!session?.user?.id && shareToken) {
      const shareResult = await query(`
        SELECT s.user_id, ss.expires_at
        FROM story_shares ss
        JOIN stories s ON ss.story_id = s.id
        WHERE ss.access_token = $1 AND s.id = $2
      `, [shareToken, storyIdInt]);

      if (shareResult.rows.length === 0) {
        return NextResponse.json({ error: 'Invalid share token' }, { status: 401 });
      }

      const share = shareResult.rows[0];

      // Check if share has expired
      if (new Date(share.expires_at) < new Date()) {
        return NextResponse.json({ error: 'Share token expired' }, { status: 401 });
      }

      // Use the story owner's user ID for transcription lookup
      authorizedUserId = share.user_id;
    } else if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    } else {
      authorizedUserId = session.user.id;
      
      // Check if user has transcription access
      const featureService = new FeatureService();
      const transcriptionAccess = await featureService.checkFeatureAccess(Feature.TRANSCRIPTION_GENERATION, authorizedUserId);
      
      if (!transcriptionAccess.hasAccess) {
        return NextResponse.json({ 
          error: 'Feature access required',
          message: 'Transcription access is only available for Family plan subscribers.',
          upgradeRequired: transcriptionAccess.upgradeRequired,
          requiredPlan: transcriptionAccess.requiredPlan
        }, { status: 403 });
      }
    }

    // Initialize transcription service
    const transcriptionService = new TranscriptionService();

    // Get transcription status for the story
    const status = await transcriptionService.getTranscriptionStatus(storyIdInt, authorizedUserId);

    if (status === null) {
      return NextResponse.json({ error: 'Audio not found for this story' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      status: status
    });

  } catch (error) {
    console.error('Error fetching transcription status:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message }, 
        { status: 500 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 