import { NextResponse } from 'next/server';
import { query } from '../../../lib/postgres-client';

// <PERSON>le preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': 'https://mystorymaker.app',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Allow-Credentials': 'true',
    },
  });
}

export async function GET() {
  try {
    const result = await query('SELECT * FROM themes ORDER BY name');

    const response = NextResponse.json(result.rows);
    
    // Add cache control headers - themes don't change frequently
    // Cache for 1 hour, allow stale content for 24 hours while revalidating
    response.headers.set('Cache-Control', 'public, max-age=3600, s-maxage=3600, stale-while-revalidate=86400');
    response.headers.set('CDN-Cache-Control', 'max-age=3600');
    response.headers.set('Vercel-CDN-Cache-Control', 'max-age=3600');
    
    // Add CORS headers
    response.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
    response.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;
  } catch (error) {
    console.error('Themes API error:', error);
    const errorResponse = NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
    // Don't cache error responses
    errorResponse.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    // Add CORS headers to error responses too
    errorResponse.headers.set('Access-Control-Allow-Origin', 'https://mystorymaker.app');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    errorResponse.headers.set('Access-Control-Allow-Credentials', 'true');
    return errorResponse;
  }
} 