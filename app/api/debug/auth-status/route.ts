import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { pool } from '@/auth';

interface DiagnosticsResponse {
  timestamp: string;
  environment: string | undefined;
  checks: {
    envVars?: {
      AUTH_SECRET: boolean;
      AUTH_SECRET_LENGTH: number;
      POSTGRES_HOST: boolean;
      POSTGRES_DATABASE: boolean;
      POSTGRES_USER: boolean;
      POSTGRES_PASSWORD: boolean;
      POSTGRES_PORT: string;
      POSTGRES_SSL: string | undefined;
    };
    database?: {
      connected: boolean;
      currentTime?: string;
      version?: string;
      error?: string;
    };
    nextAuthTables?: {
      found: string[];
      allPresent: boolean;
      error?: string;
    };
    session?: {
      hasSession: boolean;
      hasUser: boolean;
      userId: string | null;
      userEmail: string | null;
      error?: string;
    };
    activeSessions?: {
      count: number;
      error?: string;
    };
  };
}

export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with specific debug header
    const isDebugAllowed = process.env.NODE_ENV === 'development' ||
                          request.headers.get('x-debug-key') === process.env.DEBUG_KEY;

    if (!isDebugAllowed) {
      return NextResponse.json({ error: 'Debug endpoint not available' }, { status: 404 });
    }

    const diagnostics: DiagnosticsResponse = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      checks: {}
    };

    // Check environment variables (without exposing values)
    diagnostics.checks.envVars = {
      AUTH_SECRET: !!process.env.AUTH_SECRET,
      AUTH_SECRET_LENGTH: process.env.AUTH_SECRET?.length || 0,
      POSTGRES_HOST: !!process.env.POSTGRES_HOST,
      POSTGRES_DATABASE: !!process.env.POSTGRES_DATABASE,
      POSTGRES_USER: !!process.env.POSTGRES_USER,
      POSTGRES_PASSWORD: !!process.env.POSTGRES_PASSWORD,
      POSTGRES_PORT: process.env.POSTGRES_PORT || '5432',
      POSTGRES_SSL: process.env.POSTGRES_SSL,
    };

    // Test database connection
    try {
      const dbResult = await pool.query('SELECT NOW() as current_time, version() as pg_version');
      diagnostics.checks.database = {
        connected: true,
        currentTime: dbResult.rows[0]?.current_time,
        version: dbResult.rows[0]?.pg_version?.split(' ')[0] + ' ' + dbResult.rows[0]?.pg_version?.split(' ')[1],
      };
    } catch (dbError) {
      diagnostics.checks.database = {
        connected: false,
        error: dbError instanceof Error ? dbError.message : 'Unknown database error'
      };
    }

    // Test NextAuth tables
    try {
      const tablesResult = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('accounts', 'sessions', 'users', 'verification_tokens')
        ORDER BY table_name
      `);
      
      diagnostics.checks.nextAuthTables = {
        found: tablesResult.rows.map(row => row.table_name),
        allPresent: tablesResult.rows.length === 4
      };
    } catch (tableError) {
      diagnostics.checks.nextAuthTables = {
        error: tableError instanceof Error ? tableError.message : 'Unknown table check error'
      };
    }

    // Test session retrieval
    try {
      const session = await auth();
      diagnostics.checks.session = {
        hasSession: !!session,
        hasUser: !!session?.user,
        userId: session?.user?.id || null,
        userEmail: session?.user?.email || null,
      };
    } catch (sessionError) {
      diagnostics.checks.session = {
        error: sessionError instanceof Error ? sessionError.message : 'Unknown session error'
      };
    }

    // Check if we can create a test session (only in development)
    if (process.env.NODE_ENV === 'development') {
      try {
        const testSessionResult = await pool.query(`
          SELECT COUNT(*) as session_count FROM sessions WHERE expires > NOW()
        `);
        diagnostics.checks.activeSessions = {
          count: parseInt(testSessionResult.rows[0]?.session_count || '0')
        };
      } catch (sessionCountError) {
        diagnostics.checks.activeSessions = {
          error: sessionCountError instanceof Error ? sessionCountError.message : 'Unknown session count error'
        };
      }
    }

    return NextResponse.json(diagnostics);

  } catch (error) {
    console.error('Debug endpoint error:', error);
    return NextResponse.json({
      error: 'Debug endpoint failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
