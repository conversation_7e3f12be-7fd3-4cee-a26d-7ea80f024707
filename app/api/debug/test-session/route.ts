import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with specific debug header
    const isDebugAllowed = process.env.NODE_ENV === 'development' || 
                          request.headers.get('x-debug-key') === process.env.DEBUG_KEY;
    
    if (!isDebugAllowed) {
      return NextResponse.json({ error: 'Debug endpoint not available' }, { status: 404 });
    }

    // Test the NextAuth session endpoint directly
    const baseUrl = request.nextUrl.origin;
    const sessionUrl = `${baseUrl}/api/auth/session`;
    
    console.log('Testing NextAuth session endpoint:', sessionUrl);
    
    try {
      const sessionResponse = await fetch(sessionUrl, {
        method: 'GET',
        headers: {
          'Cookie': request.headers.get('cookie') || '',
          'User-Agent': request.headers.get('user-agent') || 'Debug-Test',
        },
      });

      const responseText = await sessionResponse.text();
      let responseData;
      
      try {
        responseData = JSON.parse(responseText);
      } catch {
        responseData = { rawResponse: responseText };
      }

      return NextResponse.json({
        sessionEndpointTest: {
          status: sessionResponse.status,
          statusText: sessionResponse.statusText,
          headers: Object.fromEntries(sessionResponse.headers.entries()),
          data: responseData,
          isJson: responseText.startsWith('{') || responseText.startsWith('['),
          responseLength: responseText.length,
        },
        timestamp: new Date().toISOString(),
      });

    } catch (fetchError) {
      return NextResponse.json({
        sessionEndpointTest: {
          error: 'Failed to fetch session endpoint',
          message: fetchError instanceof Error ? fetchError.message : 'Unknown fetch error',
        },
        timestamp: new Date().toISOString(),
      });
    }

  } catch (error) {
    console.error('Session test endpoint error:', error);
    return NextResponse.json({
      error: 'Session test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
