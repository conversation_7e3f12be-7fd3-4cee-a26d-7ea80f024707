import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/auth';

export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with specific debug header
    const isDebugAllowed = process.env.NODE_ENV === 'development' || 
                          request.headers.get('x-debug-key') === process.env.DEBUG_KEY;
    
    if (!isDebugAllowed) {
      return NextResponse.json({ error: 'Debug endpoint not available' }, { status: 404 });
    }

    const results: {
      timestamp: string;
      connectionTest: { success: boolean; error?: string };
      tableCheck: { success: boolean; tables?: string[]; error?: string };
      sessionTest: { success: boolean; error?: string };
    } = {
      timestamp: new Date().toISOString(),
      connectionTest: { success: false },
      tableCheck: { success: false },
      sessionTest: { success: false }
    };

    // Test 1: Basic connection
    try {
      await pool.query('SELECT NOW() as current_time');
      results.connectionTest.success = true;
    } catch (error) {
      results.connectionTest.error = error instanceof Error ? error.message : 'Unknown connection error';
    }

    // Test 2: Check NextAuth tables
    try {
      const tableResult = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('users', 'accounts', 'sessions', 'verification_token')
        ORDER BY table_name
      `);
      
      results.tableCheck.success = true;
      results.tableCheck.tables = tableResult.rows.map(row => row.table_name);
    } catch (error) {
      results.tableCheck.error = error instanceof Error ? error.message : 'Unknown table check error';
    }

    // Test 3: Try to query sessions table (this is what NextAuth does)
    try {
      await pool.query('SELECT COUNT(*) FROM sessions WHERE expires > NOW()');
      results.sessionTest.success = true;
    } catch (error) {
      results.sessionTest.error = error instanceof Error ? error.message : 'Unknown session test error';
    }

    return NextResponse.json(results);

  } catch (error) {
    console.error('NextAuth DB test error:', error);
    return NextResponse.json({
      error: 'NextAuth DB test failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
