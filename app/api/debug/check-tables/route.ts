import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/auth';

interface TableInfo {
  table_name: string;
  table_type: string;
}

interface ColumnInfo {
  table_name?: string;
  column_name: string;
  data_type: string;
  is_nullable: string;
  column_default: string | null;
}

interface IndexInfo {
  schemaname: string;
  tablename: string;
  indexname: string;
  indexdef: string;
}

interface ConstraintInfo {
  table_name: string;
  constraint_name: string;
  constraint_type: string;
  column_name: string;
  foreign_table_name: string;
  foreign_column_name: string;
}

interface RowCountInfo {
  table_name: string;
  row_count: string;
}

interface TableDiagnostics {
  timestamp: string;
  tables: {
    nextauth?: TableInfo[];
    users_structure?: ColumnInfo[];
    sessions_structure?: ColumnInfo[];
    verification_structure?: ColumnInfo[];
    row_counts?: RowCountInfo[];
    error?: string;
    users_error?: string;
    sessions_error?: string;
    verification_error?: string;
    counts_error?: string;
  };
  indexes: {
    list?: IndexInfo[];
    error?: string;
  };
  constraints: {
    foreign_keys?: ConstraintInfo[];
    error?: string;
  };
}

export async function GET(request: NextRequest) {
  try {
    // Only allow in development or with specific debug header
    const isDebugAllowed = process.env.NODE_ENV === 'development' ||
                          request.headers.get('x-debug-key') === process.env.DEBUG_KEY;

    if (!isDebugAllowed) {
      return NextResponse.json({ error: 'Debug endpoint not available' }, { status: 404 });
    }

    const diagnostics: TableDiagnostics = {
      timestamp: new Date().toISOString(),
      tables: {},
      indexes: {},
      constraints: {}
    };

    // Check which NextAuth tables exist
    try {
      const tablesResult = await pool.query(`
        SELECT table_name, table_type 
        FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name IN ('users', 'accounts', 'sessions', 'verification_token', 'verification_tokens')
        ORDER BY table_name
      `);
      
      diagnostics.tables.nextauth = tablesResult.rows;
    } catch (error) {
      diagnostics.tables.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check users table structure
    try {
      const usersResult = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'users'
        ORDER BY ordinal_position
      `);
      
      diagnostics.tables.users_structure = usersResult.rows;
    } catch (error) {
      diagnostics.tables.users_error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check sessions table structure
    try {
      const sessionsResult = await pool.query(`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'sessions'
        ORDER BY ordinal_position
      `);
      
      diagnostics.tables.sessions_structure = sessionsResult.rows;
    } catch (error) {
      diagnostics.tables.sessions_error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check verification table structure (both singular and plural)
    try {
      const verificationResult = await pool.query(`
        SELECT 'verification_token' as table_name, column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'verification_token'
        UNION ALL
        SELECT 'verification_tokens' as table_name, column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'verification_tokens'
        ORDER BY table_name, column_name
      `);
      
      diagnostics.tables.verification_structure = verificationResult.rows;
    } catch (error) {
      diagnostics.tables.verification_error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check indexes
    try {
      const indexesResult = await pool.query(`
        SELECT 
          schemaname,
          tablename,
          indexname,
          indexdef
        FROM pg_indexes 
        WHERE schemaname = 'public' 
        AND tablename IN ('users', 'accounts', 'sessions', 'verification_token', 'verification_tokens')
        ORDER BY tablename, indexname
      `);
      
      diagnostics.indexes.list = indexesResult.rows;
    } catch (error) {
      diagnostics.indexes.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check foreign key constraints
    try {
      const constraintsResult = await pool.query(`
        SELECT 
          tc.table_name, 
          tc.constraint_name, 
          tc.constraint_type,
          kcu.column_name,
          ccu.table_name AS foreign_table_name,
          ccu.column_name AS foreign_column_name 
        FROM information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
          AND tc.table_schema = kcu.table_schema
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
          AND ccu.table_schema = tc.table_schema
        WHERE tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_schema = 'public'
        AND tc.table_name IN ('users', 'accounts', 'sessions', 'verification_token', 'verification_tokens')
        ORDER BY tc.table_name, tc.constraint_name
      `);
      
      diagnostics.constraints.foreign_keys = constraintsResult.rows;
    } catch (error) {
      diagnostics.constraints.error = error instanceof Error ? error.message : 'Unknown error';
    }

    // Check sample data counts
    try {
      const countsResult = await pool.query(`
        SELECT 
          'users' as table_name, 
          COUNT(*) as row_count 
        FROM users
        UNION ALL
        SELECT 
          'sessions' as table_name, 
          COUNT(*) as row_count 
        FROM sessions
        UNION ALL
        SELECT 
          'accounts' as table_name, 
          COUNT(*) as row_count 
        FROM accounts
      `);
      
      diagnostics.tables.row_counts = countsResult.rows;
    } catch (error) {
      diagnostics.tables.counts_error = error instanceof Error ? error.message : 'Unknown error';
    }

    return NextResponse.json(diagnostics);

  } catch (error) {
    console.error('Table check endpoint error:', error);
    return NextResponse.json({
      error: 'Table check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
