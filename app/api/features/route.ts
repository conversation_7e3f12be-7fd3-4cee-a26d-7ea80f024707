import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../lib/utils/auth-helpers';
import { FeatureService } from '../../../lib/services/featureService';

export async function GET() {
  try {
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      // For unauthenticated users, return free plan features
      const featureService = new FeatureService();
      const features = await featureService.getFeatureSummary();
      
      return NextResponse.json({
        plan: 'free',
        features: features.features,
        authenticated: false
      });
    }
    
    const featureService = new FeatureService();
    const featureSummary = await featureService.getFeatureSummary(user.id);

    return NextResponse.json({
      ...featureSummary,
      authenticated: true
    });
  } catch (error) {
    console.error('Error checking feature access:', error);
    return NextResponse.json(
      { error: 'Failed to check feature access' }, 
      { status: 500 }
    );
  }
} 