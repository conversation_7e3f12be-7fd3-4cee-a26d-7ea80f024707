import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { AccessCodeService } from '../../../../lib/services/accessCodeService';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;
    const body = await request.json();
    const { code } = body;

    if (!code || typeof code !== 'string') {
      return NextResponse.json(
        { error: 'Access code is required' },
        { status: 400 }
      );
    }

    const accessCodeService = new AccessCodeService();
    const result = await accessCodeService.redeemAccessCode(userId, code);

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: result.message,
        newPlan: result.newPlan
      });
    } else {
      return NextResponse.json(
        { error: result.message },
        { status: 400 }
      );
    }
  } catch (error) {
    console.error('Access code redemption error:', error);
    return NextResponse.json(
      { error: 'Failed to redeem access code' }, 
      { status: 500 }
    );
  }
} 