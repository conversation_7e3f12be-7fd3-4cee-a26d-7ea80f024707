import { NextResponse } from 'next/server';
import { query } from '../../../lib/postgres-client';

export async function GET() {
  try {
    const result = await query(
      'SELECT id, name, gender, voice FROM voices ORDER BY name ASC'
    );

    // Add constructed sample_path using voice field directly (already includes gender prefix)
    const voicesWithSamplePath = result.rows.map(voice => ({
      ...voice,
      sample_path: `${voice.voice}.mp3`
    }));

    return NextResponse.json(voicesWithSamplePath);
  } catch (error) {
    console.error('Error fetching voices:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}