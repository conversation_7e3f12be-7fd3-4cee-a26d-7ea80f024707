import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const { fullName, email, subject, message, turnstileToken } = await request.json();

    // Validate required fields
    if (!fullName || !email || !subject || !message || !turnstileToken) {
      return NextResponse.json(
        { error: 'All fields are required, including security verification' },
        { status: 400 }
      );
    }

    // Validate Turnstile token in production
    if (process.env.NODE_ENV === 'production') {
      if (!turnstileToken) {
        return NextResponse.json(
          { error: 'Security verification required' },
          { status: 400 }
        );
      }

      try {
        const turnstileResponse = await fetch('https://challenges.cloudflare.com/turnstile/v0/siteverify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
          body: new URLSearchParams({
            secret: process.env.TURNSTILE_SECRET_KEY!,
            response: turnstileToken,
          }),
        });

        const turnstileResult = await turnstileResponse.json();

        if (!turnstileResult.success) {
          return NextResponse.json(
            { error: 'Security verification failed' },
            { status: 400 }
          );
        }
      } catch (error) {
        console.error('Turnstile verification error:', error);
        return NextResponse.json(
          { error: 'Security verification failed' },
          { status: 500 }
        );
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Send email using Resend
    if (process.env.RESEND_API_KEY) {
      try {
        const { Resend } = await import('resend');
        const resend = new Resend(process.env.RESEND_API_KEY);
        await resend.emails.send({
          from: 'MyStoryMaker Contact <<EMAIL>>',
          to: ['<EMAIL>'], // Replace with your actual email
          replyTo: email,
          subject: `Contact Form: ${subject}`,
          html: `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
              <h2 style="color: #1e293b; border-bottom: 2px solid #3b82f6; padding-bottom: 10px;">
                New Contact Form Submission
              </h2>
              <div style="background: #f8fafc; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p><strong>Name:</strong> ${fullName}</p>
                <p><strong>Email:</strong> ${email}</p>
                <p><strong>Subject:</strong> ${subject}</p>
                <p><strong>Verified:</strong> ${process.env.NODE_ENV === 'production' ? 'Cloudflare Turnstile' : 'Development Mode'}</p>
              </div>
              <div style="background: #ffffff; padding: 20px; border: 1px solid #e2e8f0; border-radius: 8px;">
                <h3 style="color: #1e293b; margin-top: 0;">Message:</h3>
                <p style="line-height: 1.6; white-space: pre-wrap;">${message}</p>
              </div>
              <p style="color: #64748b; font-size: 12px; margin-top: 20px;">
                Sent from MyStoryMaker Contact Form at ${new Date().toLocaleString()}
              </p>
            </div>
          `
        });
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
        // Continue execution - we'll still log the submission
      }
    }

    return NextResponse.json(
      { message: 'Contact form submitted successfully' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Contact form error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}