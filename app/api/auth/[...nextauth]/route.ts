import { handlers } from "@/auth"
import { NextRequest, NextResponse } from 'next/server'

// Wrap handlers with error handling
async function handleGET(request: NextRequest) {
  try {
    return await handlers.GET(request);
  } catch (error) {
    console.error('NextAuth GET handler error:', error);

    // Return a proper JSON error response instead of letting it fall through to HTML
    return NextResponse.json(
      {
        error: 'Authentication service error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );
  }
}

async function handlePOST(request: NextRequest) {
  try {
    return await handlers.POST(request);
  } catch (error) {
    console.error('NextAuth POST handler error:', error);

    // Return a proper JSON error response instead of letting it fall through to HTML
    return NextResponse.json(
      {
        error: 'Authentication service error',
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );
  }
}

export { handleGET as GET, handlePOST as POST }