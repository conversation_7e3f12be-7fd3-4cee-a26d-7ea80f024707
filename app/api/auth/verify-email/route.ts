import { NextRequest, NextResponse } from 'next/server';
import { Pool } from 'pg';

// Create PostgreSQL connection pool
const pool = new Pool({
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DATABASE,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const token = searchParams.get('token');
    const email = searchParams.get('email');

    if (!token || !email) {
      return NextResponse.redirect(
        new URL('/sign-in?error=Invalid verification link', request.url)
      );
    }

    // Check if token exists and is not expired
    const tokenResult = await pool.query(
      'SELECT * FROM verification_token WHERE identifier = $1 AND token = $2 AND expires > NOW()',
      [email, token]
    );

    if (tokenResult.rows.length === 0) {
      return NextResponse.redirect(
        new URL('/sign-in?error=Verification link has expired or is invalid', request.url)
      );
    }

    // Check if user exists
    const userResult = await pool.query(
      'SELECT id, email, "emailVerified" FROM users WHERE email = $1',
      [email]
    );

    if (userResult.rows.length === 0) {
      return NextResponse.redirect(
        new URL('/sign-in?error=User not found', request.url)
      );
    }

    const user = userResult.rows[0];

    // If already verified, redirect to sign-in
    if (user.emailVerified) {
      return NextResponse.redirect(
        new URL('/sign-in?message=Email already verified. You can sign in now.', request.url)
      );
    }

    // Verify the email
    await pool.query(
      'UPDATE users SET "emailVerified" = NOW() WHERE email = $1',
      [email]
    );

    // Delete the verification token
    await pool.query(
      'DELETE FROM verification_token WHERE identifier = $1 AND token = $2',
      [email, token]
    );

    // Redirect to sign-in with success message
    return NextResponse.redirect(
      new URL('/sign-in?message=Email verified successfully! You can now sign in.', request.url)
    );

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.redirect(
      new URL('/sign-in?error=An error occurred during verification', request.url)
    );
  }
} 