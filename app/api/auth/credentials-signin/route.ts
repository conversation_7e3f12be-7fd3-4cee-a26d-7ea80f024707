import { NextRequest, NextResponse } from 'next/server';
import bcrypt from 'bcryptjs';
import { pool } from '@/auth';
import PostgresAdapter from "@auth/pg-adapter";
import crypto from 'crypto';

// Create adapter instance
const adapter = PostgresAdapter(pool);

// Simple in-memory rate limiting (for production, use Redis or database)
const rateLimitMap = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const MAX_ATTEMPTS = 5; // 5 attempts per 15 minutes

function getRateLimitKey(ip: string, email: string): string {
  return `${ip}:${email}`;
}

function isRateLimited(key: string): boolean {
  const now = Date.now();
  const attempts = rateLimitMap.get(key) || [];
  
  // Remove old attempts outside the window
  const recentAttempts = attempts.filter((time: number) => now - time < RATE_LIMIT_WINDOW);
  rateLimitMap.set(key, recentAttempts);
  
  return recentAttempts.length >= MAX_ATTEMPTS;
}

function recordAttempt(key: string): void {
  const attempts = rateLimitMap.get(key) || [];
  attempts.push(Date.now());
  rateLimitMap.set(key, attempts);
}

// Input validation
function validateInput(email: string, password: string): string | null {
  if (!email || typeof email !== 'string') {
    return 'Invalid email format';
  }
  
  if (!password || typeof password !== 'string') {
    return 'Invalid password format';
  }
  
  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return 'Invalid email format';
  }
  
  // Normalize email
  if (email.length > 254) {
    return 'Email too long';
  }
  
  if (password.length > 128) {
    return 'Password too long';
  }
  
  return null;
}

export async function POST(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               '127.0.0.1';
    
    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch {
      return NextResponse.json({ error: 'Invalid request format' }, { status: 400 });
    }
    
    const { email, password } = body;
    
    // Input validation
    const validationError = validateInput(email, password);
    if (validationError) {
      return NextResponse.json({ error: validationError }, { status: 400 });
    }
    
    // Normalize email
    const normalizedEmail = email.toLowerCase().trim();
    
    // Rate limiting
    const rateLimitKey = getRateLimitKey(ip, normalizedEmail);
    if (isRateLimited(rateLimitKey)) {
      // Always delay for 2 seconds on rate limit to prevent timing attacks
      await new Promise(resolve => setTimeout(resolve, 2000));
      return NextResponse.json({ 
        error: 'Too many attempts. Please try again later.' 
      }, { status: 429 });
    }

    // Always record the attempt (before checking credentials)
    recordAttempt(rateLimitKey);

    // Query user from our custom users table
    const result = await pool.query(
      'SELECT id, email, name, password_hash, "emailVerified" FROM users WHERE LOWER(email) = $1',
      [normalizedEmail]
    );
    
    // Always perform password comparison to prevent timing attacks
    const user = result.rows[0];
    const hasValidUser = user && user.password_hash;
    const passwordToCheck = hasValidUser ? user.password_hash : '$2a$10$dummy.hash.to.prevent.timing.attacks.dummy.hash.value';
    
    // Always verify password (even with dummy hash) to maintain consistent timing
    const isValidPassword = await bcrypt.compare(password, passwordToCheck);
    
    // Only proceed if we have a valid user AND valid password
    if (!hasValidUser || !isValidPassword) {
      // Consistent delay for all failed attempts
      const elapsed = Date.now() - startTime;
      const minDelay = 1000; // 1 second minimum
      if (elapsed < minDelay) {
        await new Promise(resolve => setTimeout(resolve, minDelay - elapsed));
      }
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return NextResponse.json({ 
        error: 'Please verify your email address before signing in. Check your inbox for a verification link.' 
      }, { status: 401 });
    }

    // Create database session using NextAuth adapter
    try {
      // First ensure the user exists in NextAuth format
      let adapterUser = await adapter.getUser?.(user.id.toString());
      
      if (!adapterUser) {
        adapterUser = await adapter.createUser?.({
          id: user.id.toString(),
          email: user.email,
          name: user.name,
          emailVerified: user.emailVerified,
        });
      }

      if (!adapterUser) {
        throw new Error('Failed to create or retrieve adapter user');
      }
      
      // Generate cryptographically secure session token
      const sessionToken = crypto.randomUUID();
      const expires = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000); // 30 days

      const session = await adapter.createSession?.({
        sessionToken,
        userId: adapterUser.id,
        expires,
      });

      if (!session) {
        throw new Error('Failed to create session');
      }

      // Create secure response with session cookie
      const cookieName = process.env.NODE_ENV === 'production' 
        ? '__Secure-next-auth.session-token' 
        : 'next-auth.session-token';
      
      const cookieValue = `${cookieName}=${sessionToken}; HttpOnly; SameSite=Lax; Path=/; Expires=${expires.toUTCString()}${
        process.env.NODE_ENV === 'production' ? '; Secure' : ''
      }`;

      const response = NextResponse.json({ 
        success: true, 
        user: {
          id: adapterUser.id,
          email: adapterUser.email,
          name: adapterUser.name
        }
      });

      // Set security headers
      response.headers.set('Set-Cookie', cookieValue);
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      
      return response;

    } catch (sessionError) {
      console.error('❌ Error creating session:', sessionError);
      return NextResponse.json({ error: 'Authentication service temporarily unavailable' }, { status: 500 });
    }

  } catch (error) {
    console.error('❌ Sign-in error:', error);
    return NextResponse.json({ error: 'Authentication service temporarily unavailable' }, { status: 500 });
  }
} 