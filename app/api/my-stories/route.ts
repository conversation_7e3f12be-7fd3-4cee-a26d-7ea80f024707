import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../lib/utils/auth-helpers';
import { query } from '../../../lib/postgres-client';
import { getSafeImageUrl } from '../../../lib/r2-storage-utils';

// Function to create URL-friendly slug from title
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim();
}

export async function GET() {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;

    // Fetch stories with related themes and age range
    const storiesResult = await query(`
      SELECT s.*, ar.range as age_range
      FROM stories s
      LEFT JOIN age_ranges ar ON s.age_range = ar.id
      WHERE s.user_id = $1 AND s.is_deleted = false
      ORDER BY s.created_at DESC
    `, [userId]);

    if (storiesResult.rows.length === 0) {
      return NextResponse.json([]);
    }

    const storyIds = storiesResult.rows.map((story: { id: number }) => story.id);
    
    // Get themes for all stories
    const themesResult = await query(`
      SELECT st.story_id, t.name, t.description
      FROM stories_themes st
      JOIN themes t ON st.theme_id = t.id
      WHERE st.story_id = ANY($1)
    `, [storyIds]);

    // Get tags for all stories
    const tagsResult = await query(`
      SELECT st.story_id, t.name
      FROM story_tags st
      JOIN tags t ON st.tag_id = t.id
      WHERE st.story_id = ANY($1)
    `, [storyIds]);

    // Get default images for all stories
    const imagesResult = await query(`
      SELECT story_id, storage_path, updated_at
      FROM images
      WHERE story_id = ANY($1) AND "default" = true AND (deleted IS NULL OR deleted = false)
    `, [storyIds]);

    // Group themes, tags, and images by story_id
    const themesByStory = new Map<number, string[]>();
    const tagsByStory = new Map<number, string[]>();
    const imagesByStory = new Map<number, { storage_path: string; updated_at: string }>();

    themesResult.rows.forEach((theme: { story_id: number; name: string; description: string }) => {
      if (!themesByStory.has(theme.story_id)) {
        themesByStory.set(theme.story_id, []);
      }
      themesByStory.get(theme.story_id)?.push(theme.description);
    });

    tagsResult.rows.forEach((tag: { story_id: number; name: string }) => {
      if (!tagsByStory.has(tag.story_id)) {
        tagsByStory.set(tag.story_id, []);
      }
      tagsByStory.get(tag.story_id)?.push(tag.name);
    });

    imagesResult.rows.forEach((image: { story_id: number; storage_path: string; updated_at: string }) => {
      imagesByStory.set(image.story_id, image);
    });

    // Process stories and add image URLs and slugs
    const processedStories = storiesResult.rows.map((story: {
      id: number;
      story_uuid: string;
      title: string;
      main_character: string;
      setting: string;
      created_at: string;
      age_range: string;
      is_public: boolean;
    }) => {
      const themes = themesByStory.get(story.id) || [];
      const tags = tagsByStory.get(story.id) || [];
      
      let imageUrl = null;
      const defaultImage = imagesByStory.get(story.id);
      if (defaultImage) {
        const timestamp = new Date(defaultImage.updated_at).getTime().toString();
        // Use safe UUID-based endpoint that doesn't expose user IDs
        imageUrl = getSafeImageUrl(story.story_uuid, timestamp);
      }

      return {
        id: story.id,
        story_uuid: story.story_uuid,
        title: story.title,
        main_character: story.main_character,
        setting: story.setting,
        created_at: story.created_at,
        themes: themes,
        tags: tags,
        ageRange: story.age_range || 'Unknown',
        imageUrl: imageUrl,
        slug: createSlug(story.title),
        is_public: story.is_public || false
      };
    });

    return NextResponse.json(processedStories);

  } catch (error) {
    console.error('Stories API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' }, 
      { status: 500 }
    );
  }
} 