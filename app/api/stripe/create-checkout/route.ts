import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { stripeService } from '../../../../lib/services/stripeService';
import { query } from '../../../../lib/postgres-client';
import { getBaseUrl } from '../../../../lib/utils/url-helpers';

export async function POST(request: NextRequest) {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;
    const body = await request.json();
    const { planName, billingPeriod } = body;

    if (!planName || !billingPeriod) {
      return NextResponse.json(
        { error: 'Plan name and billing period are required' },
        { status: 400 }
      );
    }

    if (!['monthly', 'yearly'].includes(billingPeriod)) {
      return NextResponse.json(
        { error: 'Billing period must be monthly or yearly' },
        { status: 400 }
      );
    }

    // Get plan details with Stripe price IDs
    const planResult = await query(`
      SELECT *, 
        stripe_monthly_price_id, 
        stripe_yearly_price_id 
      FROM subscription_plans 
      WHERE name = $1 AND active = true
    `, [planName]);

    if (planResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Plan not found' },
        { status: 404 }
      );
    }

    const plan = planResult.rows[0];
    const priceId = billingPeriod === 'monthly' 
      ? plan.stripe_monthly_price_id 
      : plan.stripe_yearly_price_id;

    if (!priceId || priceId.includes('placeholder')) {
      return NextResponse.json(
        { error: 'Stripe price ID not configured for this plan' },
        { status: 500 }
      );
    }

    // Create checkout session
    const baseUrl = getBaseUrl();
    const session = await stripeService.createCheckoutSession(
      userId,
      user!.email!,
      priceId,
      planName,
      billingPeriod as 'monthly' | 'yearly',
      `${baseUrl}/pricing?success=true&session_id={CHECKOUT_SESSION_ID}`,
      `${baseUrl}/pricing?cancelled=true`
    );

    return NextResponse.json({
      success: true,
      checkoutUrl: session.url,
      sessionId: session.id
    });

  } catch (error) {
    console.error('Error creating checkout session:', error);
    return NextResponse.json(
      { error: 'Failed to create checkout session' },
      { status: 500 }
    );
  }
} 