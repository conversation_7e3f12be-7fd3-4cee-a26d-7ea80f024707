import { NextResponse } from 'next/server';
import { getAuthenticatedUser } from '../../../../lib/utils/auth-helpers';
import { stripeService } from '../../../../lib/services/stripeService';
import { query } from '../../../../lib/postgres-client';
import { getBaseUrl } from '../../../../lib/utils/url-helpers';

export async function POST() {
  try {
    // Verify user is authenticated
    const { user, error } = await getAuthenticatedUser();
    
    if (error) {
      return error;
    }
    
    const userId = user!.id;

    // Get user's Stripe customer ID
    const result = await query(
      'SELECT stripe_customer_id FROM users WHERE id = $1',
      [userId]
    );

    const stripeCustomerId = result.rows[0]?.stripe_customer_id;

    if (!stripeCustomerId) {
      return NextResponse.json(
        { error: 'No active subscription found' },
        { status: 404 }
      );
    }

    // Create billing portal session
    const baseUrl = getBaseUrl();
    const portalUrl = await stripeService.createBillingPortalSession(
      stripeCustomerId,
      `${baseUrl}/profile`
    );

    return NextResponse.json({
      success: true,
      portalUrl
    });

  } catch (error) {
    console.error('Error creating billing portal session:', error);
    return NextResponse.json(
      { error: 'Failed to create billing portal session' },
      { status: 500 }
    );
  }
} 