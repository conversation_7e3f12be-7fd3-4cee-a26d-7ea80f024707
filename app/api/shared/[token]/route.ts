import { NextRequest, NextResponse } from 'next/server';
import { query } from '../../../../lib/postgres-client';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ token: string }> }
) {
  try {
    const { token } = await params;

    if (!token) {
      return NextResponse.redirect(new URL('/404', request.url));
    }

    // Look up the story by access token
    const shareResult = await query(
      `SELECT s.story_uuid, s.title, ss.expires_at
       FROM story_shares ss
       JOIN stories s ON ss.story_id = s.id
       WHERE ss.access_token = $1`,
      [token]
    );

    if (shareResult.rows.length === 0) {
      // Share not found
      return NextResponse.redirect(new URL('/404', request.url));
    }

    const share = shareResult.rows[0];

    // Check if share has expired
    if (new Date(share.expires_at) < new Date()) {
      // Share expired
      return NextResponse.redirect(new URL('/404', request.url));
    }

    // Create slug from title (must match createSlug function in story page)
    const slug = share.title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single
      .trim();

    // Redirect to unified story page with shared source and token
    const redirectUrl = new URL(`/stories/${share.story_uuid}/${slug}`, request.url);
    redirectUrl.searchParams.set('source', 'shared');
    redirectUrl.searchParams.set('token', token);
    
    return NextResponse.redirect(redirectUrl);

  } catch (error) {
    console.error('Error looking up shared story:', error);
    return NextResponse.redirect(new URL('/404', request.url));
  }
} 