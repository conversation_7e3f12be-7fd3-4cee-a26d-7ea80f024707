import { type Metadata, type Viewport } from 'next'
import { Session<PERSON>rovider } from "next-auth/react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mon<PERSON> } from 'next/font/google'
import { GoogleAnalytics, PageViewTracker } from './components/analytics'
import LoadingBar from './components/ui/LoadingBar'
import SEOOptimizations from './components/SEOOptimizations'
import { StagewiseToolbar } from '@stagewise/toolbar-next'
import { ReactPlugin } from '@stagewise-plugins/react'
import './globals.css'

const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
  display: 'swap',
  preload: false,
  fallback: ['system-ui', '-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 'sans-serif'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
  display: 'swap',
  preload: false,
  fallback: ['ui-monospace', 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', 'monospace'],
})

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  viewportFit: 'cover'
}

export const metadata: Metadata = {
  metadataBase: new URL('https://mystorymaker.app'),
  title: {
    default: 'MyStoryMaker - Create Magical Stories for Your Children',
    template: '%s | MyStoryMaker'
  },
  description: 'Transform simple ideas into beautifully crafted stories that captivate young minds and inspire creativity. Create personalized stories for your children with AI technology.',
  keywords: [
    'children stories', 
    'AI storytelling', 
    'kids books', 
    'story generator', 
    'educational stories',
    'personalized stories',
    'bedtime stories',
    'creative writing',
    'family entertainment',
    'child development'
  ],
  authors: [{ name: 'MyStoryMaker', url: 'https://mystorymaker.app' }],
  creator: 'MyStoryMaker',
  publisher: 'MyStoryMaker',
  category: 'Education',
  classification: 'Children\'s Entertainment',
  icons: {
    icon: [
      { url: '/favicons/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/favicons/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/favicons/favicon.ico', sizes: 'any' }
    ],
    apple: [
      { url: '/favicons/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
    ],
    shortcut: '/favicons/favicon.ico'
  },
  manifest: '/favicons/site.webmanifest?v=1',
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://mystorymaker.app',
    title: 'MyStoryMaker - Create Magical Stories for Your Children',
    description: 'Transform simple ideas into beautifully crafted stories that captivate young minds and inspire creativity. Create personalized stories for your children with AI technology.',
    siteName: 'MyStoryMaker',
    images: [
      {
        url: '/favicons/android-chrome-512x512.png',
        width: 512,
        height: 512,
        alt: 'MyStoryMaker Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'MyStoryMaker - Create Magical Stories for Your Children',
    description: 'Transform simple ideas into beautifully crafted stories that captivate young minds and inspire creativity.',
    images: ['/favicons/android-chrome-512x512.png'],
    creator: '@mystorymaker',
    site: '@mystorymaker'
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: true,
      noimageindex: false,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION,
    yandex: process.env.YANDEX_VERIFICATION,
    yahoo: process.env.YAHOO_VERIFICATION,
    other: {
      'msvalidate.01': process.env.BING_VERIFICATION || '',
    }
  },
  alternates: {
    canonical: 'https://mystorymaker.app',
    languages: {
      'en-US': 'https://mystorymaker.app',
    }
  },
  other: {
    'theme-color': '#1e293b',
    'color-scheme': 'dark light',
    'format-detection': 'telephone=no'
  }
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID

  return (
    <html lang="en">
      <head>
        <SEOOptimizations />
      </head>
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <SessionProvider>
          <LoadingBar />
          {children}
          {gaId && <GoogleAnalytics measurementId={gaId} />}
          <PageViewTracker />
          <StagewiseToolbar config={{ plugins: [ReactPlugin] }} />
        </SessionProvider>
      </body>
    </html>
  )
}