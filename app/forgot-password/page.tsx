'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import Navigation from '../components/Navigation';
import Footer from '../components/Footer';

export default function ForgotPasswordPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to sign-in page after a short delay
    const timer = setTimeout(() => {
      router.push('/sign-in');
    }, 3000);

    return () => clearTimeout(timer);
  }, [router]);

  return (
    <div className="min-h-screen">
      <Navigation />
      
      <div className="flex items-center justify-center min-h-[calc(100vh-200px)] px-4">
        <div className="max-w-md w-full">
          <div className="bg-slate-800 rounded-lg p-8 shadow-xl text-center">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-white mb-2">
                Password Reset
              </h1>
              <p className="text-gray-400">
                We use magic links for secure, passwordless authentication
              </p>
            </div>

            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-3">
                <div className="flex-shrink-0">
                  <svg className="w-5 h-5 text-blue-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="text-left">
                  <p className="text-blue-800 text-sm font-medium mb-2">No password needed!</p>
                  <p className="text-blue-700 text-sm">
                    Simply enter your email on the sign-in page and we'll send you a secure magic link to access your account.
                  </p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <Link href="/sign-in">
                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-semibold transition-colors">
                  Go to Sign In
                </button>
              </Link>
              
              <p className="text-gray-400 text-sm">
                Redirecting automatically in 3 seconds...
              </p>
            </div>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
} 