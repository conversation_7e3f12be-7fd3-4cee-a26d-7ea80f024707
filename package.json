{"name": "mystorymaker4", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "build:turbo": "next build --turbo", "build:analyze": "ANALYZE=true npm run build", "build:docker": "docker build --build-arg BUILDKIT_INLINE_CACHE=1 -t mystorymaker .", "start": "next start", "lint": "next lint", "analyze": "npx @next/bundle-analyzer@latest"}, "dependencies": {"@auth/pg-adapter": "^1.9.1", "@aws-sdk/client-s3": "^3.826.0", "@aws-sdk/s3-request-presigner": "^3.826.0", "@heroicons/react": "^2.2.0", "@next/third-parties": "^15.3.3", "@stagewise-plugins/react": "^0.4.7", "@stagewise/toolbar-next": "^0.4.7", "@stripe/stripe-js": "^7.3.1", "@types/jsonwebtoken": "^9.0.9", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "assemblyai": "^4.12.2", "bcryptjs": "^3.0.2", "dotenv": "^16.5.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "next": "15.3.2", "next-auth": "^5.0.0-beta.28", "nodemailer": "^6.10.1", "pg": "^8.16.0", "react": "^19.0.0", "react-dom": "^19.0.0", "resend": "^4.5.2", "stripe": "^18.2.1", "svix": "^1.66.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "cross-env": "^7.0.3", "eslint": "^9", "eslint-config-next": "15.3.2", "tailwindcss": "^4", "typescript": "^5"}}