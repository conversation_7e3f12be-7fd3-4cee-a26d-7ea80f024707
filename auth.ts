import NextAuth from "next-auth"
import PostgresAdapter from "@auth/pg-adapter"
import { Pool } from "pg"

import authConfig from "./auth.config"

// Environment variable validation for security
function validateEnvironment() {
  const required = [
    'AUTH_SECRET',
    'POSTGRES_HOST',
    'POSTGRES_DATABASE',
    'POSTGRES_USER',
    'POSTGRES_PASSWORD'
  ];
  
  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }
  
  // Validate AUTH_SECRET length
  if (process.env.AUTH_SECRET && process.env.AUTH_SECRET.length < 32) {
    throw new Error('AUTH_SECRET should be at least 32 characters for security');
  }
}

// Validate environment on startup
validateEnvironment();

// SSL configuration - can be controlled via environment variable
// For Docker internal networks, set POSTGRES_SSL=false or leave unset
// For external PostgreSQL services (like AWS RDS), set POSTGRES_SSL=true
const getSSLConfig = () => {
  // If POSTGRES_SSL is explicitly set, use that value
  if (process.env.POSTGRES_SSL !== undefined) {
    return process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false;
  }
  
  // For Dokploy/internal Docker networks, default to no SSL
  if (process.env.POSTGRES_HOST && !process.env.POSTGRES_HOST.includes('localhost')) {
    return false;
  }
  
  // Default behavior for other environments
  return process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false;
};

// Create PostgreSQL connection pool (aligned with main app settings)
const pool = new Pool({
  host: process.env.POSTGRES_HOST,
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DATABASE,
  user: process.env.POSTGRES_USER,
  password: process.env.POSTGRES_PASSWORD,
  ssl: getSSLConfig(),
  // Connection pool settings aligned with main app
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000, // Increased to match main app (was 2000)
})

// Test database connection on startup
pool.on('connect', () => {
  console.log('✅ PostgreSQL pool connected for NextAuth');
});

pool.on('error', (err) => {
  console.error('❌ PostgreSQL pool error for NextAuth:', err);
});

// Test connection immediately (skip during build)
if (process.env.SKIP_DB_TEST !== 'true') {
  // Use a timeout to avoid hanging the build process
  Promise.race([
    pool.query('SELECT NOW()'),
    new Promise((_, reject) =>
      setTimeout(() => reject(new Error('Database connection timeout')), 5000)
    )
  ])
    .then(() => console.log('✅ NextAuth database connection test successful'))
    .catch((err) => {
      console.warn('⚠️ NextAuth database connection test failed (this may be expected during build):', err.message);
      // Don't throw error to avoid breaking the build
    });
} else {
  console.log('⏭️ Skipping NextAuth database connection test during build');
}

// Create the adapter with error handling
let adapter;
try {
  adapter = PostgresAdapter(pool);
  console.log('✅ NextAuth PostgresAdapter created successfully');
} catch (adapterError) {
  console.error('❌ Failed to create NextAuth PostgresAdapter:', adapterError);
  throw new Error(`NextAuth adapter creation failed: ${adapterError instanceof Error ? adapterError.message : 'Unknown error'}`);
}

// Export pool for use in custom authentication
export { pool };

export const { handlers, auth, signIn, signOut } = NextAuth({
  secret: process.env.AUTH_SECRET,
  trustHost: true, // Trust the host in production
  adapter,
  session: { 
    strategy: "database", // Use database sessions
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },
  // Security configurations
  useSecureCookies: process.env.NODE_ENV === 'production',
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.session-token' : 'next-auth.session-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production' ? '__Secure-next-auth.callback-url' : 'next-auth.callback-url',
      options: {
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production' ? '__Host-next-auth.csrf-token' : 'next-auth.csrf-token',
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  providers: [
    // Only OAuth providers work well with database sessions
    // Custom credentials authentication will be handled via API route
    ...authConfig.providers,
  ],
  callbacks: {
    async session({ session, user }) {
      try {
        // Add user ID from database user to session (keep as integer to match database schema)
        if (user?.id) {
          session.user.id = user.id;
        }

        // Log session callback in development
        if (process.env.NODE_ENV === 'development') {
          console.log('NextAuth session callback:', {
            hasSession: !!session,
            hasUser: !!user,
            userId: user?.id
          });
        }

        return session;
      } catch (error) {
        console.error('❌ NextAuth session callback error:', error);
        // Return session even if there's an error to avoid breaking auth
        return session;
      }
    },
    async signIn({ user, account }) {
      // Additional security checks for OAuth sign-ins
      if (account?.provider === 'google' || account?.provider === 'facebook') {
        // Verify email is provided by OAuth provider
        if (!user.email) {
          return false;
        }
        
        // For production, you might want to verify the email domain
        // or implement additional checks here
        return true;
      }
      
      return true;
    },
    async redirect({ url, baseUrl }) {
      // Ensure redirects are safe and within our domain
      if (url.startsWith("/")) return `${baseUrl}${url}`;
      if (new URL(url).origin === baseUrl) return url;
      return baseUrl;
    },
  },
  // Include other config options from shared config
  pages: authConfig.pages,
  debug: process.env.NODE_ENV === 'development', // Enable debug in development only
  
  // Additional security options
  events: {
    async signIn({ user, account }) {
      console.log('NextAuth signIn event:', { userId: user?.id, provider: account?.provider });
    },
    async signOut() {
      console.log('NextAuth signOut event');
    },
  },
}) 