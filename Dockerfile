FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
# Check https://github.com/nodejs/docker-node/tree/b4117f9333da4138b03a546ec926ef50a31506c3#nodealpine to understand why libc6-compat might be needed.
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
# Use npm ci with cache mount for faster installs (includes devDependencies for build)
RUN --mount=type=cache,target=/root/.npm \
    npm ci --ignore-scripts

# Install only production dependencies for runtime
FROM base AS prod-deps
RUN apk add --no-cache libc6-compat
WORKDIR /app
COPY package.json package-lock.json* ./
RUN --mount=type=cache,target=/root/.npm \
    npm ci --omit=dev --ignore-scripts

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Next.js collects completely anonymous telemetry data about general usage.
# Learn more here: https://nextjs.org/telemetry
# Uncomment the following line in case you want to disable telemetry during the build.
ENV NEXT_TELEMETRY_DISABLED 1

# Clear any existing .next directory before build
RUN rm -rf .next

# Use build cache mount for faster builds
RUN --mount=type=cache,target=/app/.next/cache \
    npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production
# Uncomment the following line in case you want to disable telemetry during runtime.
ENV NEXT_TELEMETRY_DISABLED 1

# Environment variables (uncomment and set as needed)
#ENV POSTGRES_HOST=${POSTGRES_HOST}
#ENV POSTGRES_PORT=${POSTGRES_PORT}
#ENV POSTGRES_DATABASE=${POSTGRES_DATABASE}
#ENV POSTGRES_USER=${POSTGRES_USER}
#ENV POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
#ENV POSTGRES_SSL=${POSTGRES_SSL}
#ENV NEXT_PUBLIC_TURNSTILE_SITE_KEY=${NEXT_PUBLIC_TURNSTILE_SITE_KEY}
#ENV TURNSTILE_SECRET_KEY=${TURNSTILE_SECRET_KEY}
#ENV R2_IMAGES_BUCKET_NAME=${R2_IMAGES_BUCKET_NAME}
#ENV R2_AUDIO_BUCKET_NAME=${R2_AUDIO_BUCKET_NAME}
#ENV R2_ENDPOINT=${R2_ENDPOINT}
#ENV R2_ACCESS_KEY_ID=${R2_ACCESS_KEY_ID}
#ENV R2_SECRET_ACCESS_KEY=${R2_SECRET_ACCESS_KEY}

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy production dependencies
COPY --from=prod-deps /app/node_modules ./node_modules
COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

# Automatically leverage output traces to reduce image size
# https://nextjs.org/docs/advanced-features/output-file-tracing
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
# set hostname to localhost
ENV HOSTNAME "0.0.0.0"

# server.js is created by next build from the standalone output
# https://nextjs.org/docs/pages/api-reference/next-config-js/output
CMD ["node", "server.js"] 