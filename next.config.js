/** @type {import('next').NextConfig} */
const nextConfig = {
  output: 'standalone',
  
  // Environment variables
  env: {
    NEXT_PUBLIC_BASE_URL: process.env.NEXT_PUBLIC_BASE_URL || 'https://mystorymaker.app',
  },

  // Image optimization
  images: {
    formats: ['image/webp'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    minimumCacheTTL: 31536000, // 1 year
    dangerouslyAllowSVG: false,
    // Restore image CSP for security - safe now that CAPTCHA widget is removed
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },

  // Compression
  compress: true,

  // Next.js 15 specific optimizations
  reactStrictMode: true,

  // Performance optimizations
  poweredByHeader: false,

  // External packages for server components (moved from experimental)
  serverExternalPackages: ['sharp'],

  // Turbopack configuration (stable in Next.js 15)
  turbopack: {
    // Enable Turbopack optimizations
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Bundle optimization (safe optimizations only)
  experimental: {
    optimizePackageImports: [
      'lucide-react',
      '@heroicons/react',
      '@clerk/nextjs',
      'react-dom',
      'next/image',
      'next/link'
    ],
    webpackBuildWorker: true,
    // Better tree shaking
    esmExternals: true,
  },

  // Webpack configuration optimized for Next.js 15
  webpack: (config, { isServer, dev }) => {
    // Only apply webpack config when not using Turbopack
    if (!process.env.TURBOPACK) {
      // Server-side externals for browser-only dependencies
      if (isServer) {
        config.externals = config.externals || [];
        config.externals.push({
          'jspdf': 'jspdf',
          'html2canvas': 'html2canvas',
        });
      }

      // Production optimizations
      if (!dev) {
        // Better tree shaking
        config.optimization = {
          ...config.optimization,
          usedExports: true,
          sideEffects: false,
        };

        // Optimize bundle splitting
        config.optimization.splitChunks = {
          ...config.optimization.splitChunks,
          cacheGroups: {
            ...config.optimization.splitChunks?.cacheGroups,
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            common: {
              name: 'common',
              minChunks: 2,
              chunks: 'all',
              priority: 5,
              reuseExistingChunk: true,
            },
          },
        };
      }
    }
    return config;
  },

  // Headers for caching and security
  async headers() {
    return [
      // CORS headers for API routes - more flexible configuration
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: process.env.NODE_ENV === 'development' ? '*' : 'https://mystorymaker.app',
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, POST, PUT, DELETE, PATCH, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With, Origin, Accept',
          },
          {
            key: 'Access-Control-Allow-Credentials',
            value: 'true',
          },
          {
            key: 'Access-Control-Max-Age',
            value: '86400', // 24 hours
          },
        ],
      },
      // CORS headers for public API routes (more permissive)
      {
        source: '/api/public/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*', // Allow all origins for public APIs
          },
          {
            key: 'Access-Control-Allow-Methods',
            value: 'GET, OPTIONS',
          },
          {
            key: 'Access-Control-Allow-Headers',
            value: 'Content-Type, Authorization, X-Requested-With, Origin, Accept',
          },
        ],
      },
      // Static asset caching
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
      {
        source: '/fonts/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/_next/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
      {
        source: '/favicons/site.webmanifest',
        headers: [
          {
            key: 'Content-Type',
            value: 'application/manifest+json',
          },
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400',
          },
        ],
      },
    ];
  },
};

module.exports = nextConfig;
