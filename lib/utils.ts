/**
 * Get the base URL for the application
 * Priority order:
 * 1. Client-side: window.location.origin
 * 2. NEXT_PUBLIC_BASE_URL (explicit override)
 * 3. NEXT_PUBLIC_APP_URL (app-specific URL)
 * 4. VERCEL_URL (Vercel deployment URL)
 * 5. NEXT_PUBLIC_VERCEL_URL (public Vercel URL)
 * 6. Production/development fallbacks
 */
export function getBaseUrl(): string {
  // If we're on the client side, use the current origin
  if (typeof window !== 'undefined') {
    return window.location.origin;
  }

  // Server-side: try environment variables in priority order
  
  // 1. Explicit base URL override (highest priority)
  if (process.env.NEXT_PUBLIC_BASE_URL) {
    return process.env.NEXT_PUBLIC_BASE_URL.replace(/\/$/, ''); // Remove trailing slash
  }

  // 2. App-specific URL
  if (process.env.NEXT_PUBLIC_APP_URL) {
    return process.env.NEXT_PUBLIC_APP_URL.replace(/\/$/, '');
  }

  // 3. Vercel deployment URL (internal)
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }

  // 4. Public Vercel URL (if set)
  if (process.env.NEXT_PUBLIC_VERCEL_URL) {
    return process.env.NEXT_PUBLIC_VERCEL_URL.replace(/\/$/, '');
  }

  // 5. Environment-based fallbacks (only if no env vars are set)
  if (process.env.NODE_ENV === 'production') {
    // Production fallback - only used if no environment variables are configured
    return 'https://mystorymaker.app';
  }

  // Development fallback
  return 'http://localhost:3000';
} 