'use client';

interface StoryData {
  id: number;
  title: string;
  content: string;
  themes: string[];
  ageRange: string;
  createdAt: string;
  imageUrl?: string;
}

export class StoryPDFGenerator {
  async generateStoryPDF(story: StoryData): Promise<Blob> {
    // Dynamic import to prevent server-side issues
    const { jsPDF } = await import('jspdf');
    const doc = new jsPDF();

    // Helper function to add footer
    const addFooter = async (pageNumber: number, totalPages: number) => {
      const footerHeight = 20;
      doc.setFillColor(45, 45, 45); // Dark background
      doc.rect(0, doc.internal.pageSize.height - footerHeight, doc.internal.pageSize.width, footerHeight, 'F');

      try {
        const logoResponse = await fetch('/images/logo.png');
        
        if (!logoResponse.ok) {
          throw new Error(`Logo fetch failed: ${logoResponse.status} ${logoResponse.statusText}`);
        }
        
        const logoBlob = await logoResponse.blob();
        
        const logoBase64 = await new Promise<string>((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = () => {
            resolve(reader.result as string);
          };
          reader.onerror = () => {
            reject(new Error('FileReader failed'));
          };
          reader.readAsDataURL(logoBlob);
        });

        // Add logo (12mm size)
        const logoSize = 12;
        const logoY = doc.internal.pageSize.height - footerHeight + 4;
        doc.addImage(logoBase64, 'PNG', 20, logoY, logoSize, logoSize);

        // Add "MyStoryMaker" text next to logo
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('MyStoryMaker', 20 + logoSize + 4, logoY + 8);

        // Add site URL in the middle
        doc.setFont('helvetica', 'normal');
        const siteUrl = 'mystorymaker.app';
        const urlWidth = doc.getTextWidth(siteUrl);
        const centerX = (doc.internal.pageSize.width - urlWidth) / 2;
        doc.text(siteUrl, centerX, logoY + 8);

        // Add page numbers on the right
        doc.setFontSize(10);
        doc.text(`Page ${pageNumber} of ${totalPages}`, doc.internal.pageSize.width - 20, logoY + 8, { align: 'right' });

      } catch (error) {
        console.error('Error loading logo:', error);
        // Fallback to text-only if logo fails to load
        const y = doc.internal.pageSize.height - footerHeight + 12;

        // Add "MyStoryMaker" text on the left
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.text('MyStoryMaker', 20, y);

        // Add site URL in the middle
        doc.setFont('helvetica', 'normal');
        const siteUrl = 'mystorymaker.app';
        const urlWidth = doc.getTextWidth(siteUrl);
        const centerX = (doc.internal.pageSize.width - urlWidth) / 2;
        doc.text(siteUrl, centerX, y);

        // Add page numbers on the right
        doc.setFontSize(10);
        doc.text(`Page ${pageNumber} of ${totalPages}`, doc.internal.pageSize.width - 20, y, { align: 'right' });
      }
    };

    // Add image if available
    if (story.imageUrl) {
      try {
        const base64Image = await this.loadImageAsBase64(story.imageUrl);

        // Add image to PDF - full width, top third of page
        doc.addImage(base64Image, 'JPEG', 0, 0, 210, 100); // A4 width is 210mm

        // Add semi-transparent black bar at the bottom of the image
        doc.setFillColor(0, 0, 0);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        doc.setGState(new (doc as any).GState({ opacity: 0.6 }));
        doc.rect(0, 70, 210, 30, 'F'); // Black bar for text

        // Reset opacity for text
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        doc.setGState(new (doc as any).GState({ opacity: 1 }));

        // Add title on the transparent overlay
        doc.setTextColor(255, 255, 255);
        doc.setFontSize(24);
        doc.text(story.title, 20, 85);

        // Add metadata
        doc.setFontSize(12);
        const metadata = [
          `Theme: ${story.themes.join(', ')}`,
          `Age Range: ${story.ageRange}`,
          `Created: ${new Date(story.createdAt).toLocaleDateString()}`
        ];
        doc.text(metadata.join('  •  '), 20, 93);

        // Reset text color for content
        doc.setTextColor(0, 0, 0);

        // Add content starting after the image
        doc.setFontSize(13);
        const content = story.content.split('\n\n');
        let y = 120; // Start content after the image
        let pageNumber = 1;

        // Calculate total pages
        let totalPages = 1;
        let tempY = y;
        content.forEach(paragraph => {
          const lines = doc.splitTextToSize(paragraph, 170);
          lines.forEach(() => {
            if (tempY > 260) {
              totalPages++;
              tempY = 20;
            }
            tempY += 7;
          });
          tempY += 7;
        });

        // Add content with footers
        for (const paragraph of content) {
          const lines = doc.splitTextToSize(paragraph, 170);
          for (const line of lines) {
            if (y > 260) {
              await addFooter(pageNumber, totalPages);
              doc.addPage();
              pageNumber++;
              y = 20;
              doc.setTextColor(0, 0, 0);
              doc.setFontSize(13);
            }
            doc.text(line, 20, y);
            y += 7;
          }
          y += 7;
        }

        // Add footer to last page
        await addFooter(pageNumber, totalPages);

      } catch (error) {
        console.error('Error adding image to PDF:', error);
        // Fallback to text-only version
        await this.createTextOnlyPDF(doc, story, addFooter);
      }
    } else {
      // Text-only version if no image
      await this.createTextOnlyPDF(doc, story, addFooter);
    }

    return doc.output('blob');
  }

  private async createTextOnlyPDF(doc: import('jspdf').jsPDF, story: StoryData, addFooter: (pageNumber: number, totalPages: number) => Promise<void>) {
    doc.setFontSize(24);
    doc.text(story.title, 20, 20);

    doc.setFontSize(12);
    const metadata = [
      `Theme: ${story.themes.join(', ')}`,
      `Age Range: ${story.ageRange}`,
      `Created: ${new Date(story.createdAt).toLocaleDateString()}`
    ];
    doc.text(metadata.join('  •  '), 20, 30);

    doc.setFontSize(13);
    const content = story.content.split('\n\n');
    let y = 40;
    let pageNumber = 1;

    // Calculate total pages
    let totalPages = 1;
    let tempY = y;
    content.forEach(paragraph => {
      const lines = doc.splitTextToSize(paragraph, 170);
      lines.forEach(() => {
        if (tempY > 260) {
          totalPages++;
          tempY = 20;
        }
        tempY += 7;
      });
      tempY += 7;
    });

    // Add content with footers
    for (const paragraph of content) {
      const lines = doc.splitTextToSize(paragraph, 170);
      for (const line of lines) {
        if (y > 260) {
          await addFooter(pageNumber, totalPages);
          doc.addPage();
          pageNumber++;
          y = 20;
          doc.setFontSize(13);
        }
        doc.text(line, 20, y);
        y += 7;
      }
      y += 7;
    }

    // Add footer to last page
    await addFooter(pageNumber, totalPages);
  }

  private async loadImageAsBase64(imageUrl: string): Promise<string> {
    const response = await fetch(imageUrl);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }
} 