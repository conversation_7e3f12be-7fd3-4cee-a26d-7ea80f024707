import { NextResponse } from 'next/server';

// CORS configuration - allow both production and development origins
const getAllowedOrigins = (): string[] => {
  const origins = ['https://mystorymaker.app'];
  
  // In development, also allow localhost
  if (process.env.NODE_ENV === 'development') {
    origins.push('http://localhost:3000', 'http://127.0.0.1:3000');
  }
  
  // Allow ngrok tunnels for development
  if (process.env.NODE_ENV === 'development' && process.env.NGROK_URL) {
    origins.push(process.env.NGROK_URL);
  }
  
  return origins;
};

const CORS_CONFIG = {
  origins: getAllowedOrigins(),
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  headers: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
};

/**
 * Check if origin is allowed
 */
function isOriginAllowed(origin: string | null): boolean {
  if (!origin) return true; // Allow same-origin requests
  return CORS_CONFIG.origins.includes(origin);
}

/**
 * Add CORS headers to a NextResponse
 */
export function addCorsHeaders(response: NextResponse, requestOrigin?: string | null): NextResponse {
  // Determine which origin to allow
  let allowedOrigin = 'https://mystorymaker.app'; // Default to production
  
  if (requestOrigin && isOriginAllowed(requestOrigin)) {
    allowedOrigin = requestOrigin;
  }
  
  response.headers.set('Access-Control-Allow-Origin', allowedOrigin);
  response.headers.set('Access-Control-Allow-Methods', CORS_CONFIG.methods.join(', '));
  response.headers.set('Access-Control-Allow-Headers', CORS_CONFIG.headers.join(', '));
  response.headers.set('Access-Control-Allow-Credentials', CORS_CONFIG.credentials.toString());
  return response;
}

/**
 * Create a CORS preflight response
 */
export function createCorsPreflightResponse(requestOrigin?: string | null): NextResponse {
  const response = new NextResponse(null, { status: 200 });
  return addCorsHeaders(response, requestOrigin);
}

/**
 * Create a JSON response with CORS headers
 */
export function createCorsJsonResponse(data: unknown, status = 200, requestOrigin?: string | null): NextResponse {
  const response = NextResponse.json(data, { status });
  return addCorsHeaders(response, requestOrigin);
}

/**
 * Create an error response with CORS headers
 */
export function createCorsErrorResponse(error: string, status = 500, requestOrigin?: string | null): NextResponse {
  const response = NextResponse.json({ error }, { status });
  return addCorsHeaders(response, requestOrigin);
} 