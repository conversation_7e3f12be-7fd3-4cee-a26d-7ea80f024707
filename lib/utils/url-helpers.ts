/**
 * Ensures a URL has a proper scheme (http/https) for Stripe API calls
 */
export function ensureAbsoluteUrl(url: string, fallbackBase?: string): string {
  // If URL already has a scheme, return as-is
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url;
  }
  
  // If URL starts with /, it's a relative path
  if (url.startsWith('/')) {
    const baseUrl = process.env.NEXTAUTH_URL || fallbackBase || 'http://localhost:3000';
    return `${baseUrl}${url}`;
  }
  
  // If no scheme and doesn't start with /, assume it needs http://
  return `http://${url}`;
}

/**
 * Gets the base URL for the application
 */
export function getBaseUrl(): string {
  // In production, use NEXTAUTH_URL
  if (process.env.NEXTAUTH_URL) {
    return process.env.NEXTAUTH_URL;
  }
  
  // In development, construct from Vercel environment variables if available
  if (process.env.VERCEL_URL) {
    return `https://${process.env.VERCEL_URL}`;
  }
  
  // Fallback to localhost for development
  return 'http://localhost:3000';
} 