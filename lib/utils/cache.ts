// In-memory cache for API responses and database queries
class Memory<PERSON>ache {
  private cache = new Map<string, { data: unknown; expiry: number }>();
  private static instance: MemoryCache;

  static getInstance(): MemoryCache {
    if (!MemoryCache.instance) {
      MemoryCache.instance = new MemoryCache();
    }
    return MemoryCache.instance;
  }

  set(key: string, data: unknown, ttlMs: number = 300000): void { // 5 minutes default
    const expiry = Date.now() + ttlMs;
    this.cache.set(key, { data, expiry });
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }

    return item.data as T;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }

  // Get cache statistics
  getStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

// Cache wrapper for async functions
export function withCache<T extends unknown[], R>(
  fn: (...args: T) => Promise<R>,
  keyGenerator: (...args: T) => string,
  ttlMs: number = 300000
) {
  return async (...args: T): Promise<R> => {
    const cache = MemoryCache.getInstance();
    const key = keyGenerator(...args);
    
    // Try to get from cache first
    const cached = cache.get<R>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute function and cache result
    const result = await fn(...args);
    cache.set(key, result, ttlMs);
    return result;
  };
}

// Cache for database queries
export const dbCache = {
  // Cache user stories
  getUserStories: withCache(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (_userId: string) => {
      // This would be replaced with actual database call
      return [];
    },
    (userId: string) => `user_stories:${userId}`,
    600000 // 10 minutes
  ),

  // Cache public stories
  getPublicStories: withCache(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (_page: number = 1, _limit: number = 10) => {
      // This would be replaced with actual database call
      return [];
    },
    (page: number, limit: number) => `public_stories:${page}:${limit}`,
    300000 // 5 minutes
  ),

  // Cache story details
  getStoryById: withCache(
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    async (_storyId: string) => {
      // This would be replaced with actual database call
      return null;
    },
    (storyId: string) => `story:${storyId}`,
    600000 // 10 minutes
  ),

  // Cache themes
  getThemes: withCache(
    async () => {
      // This would be replaced with actual database call
      return [];
    },
    () => 'themes',
    3600000 // 1 hour
  ),

  // Cache age ranges
  getAgeRanges: withCache(
    async () => {
      // This would be replaced with actual database call
      return [];
    },
    () => 'age_ranges',
    3600000 // 1 hour
  ),

  // Cache voices
  getVoices: withCache(
    async () => {
      // This would be replaced with actual database call
      return [];
    },
    () => 'voices',
    3600000 // 1 hour
  ),

  // Invalidate user-specific cache
  invalidateUserCache: (userId: string) => {
    const cache = MemoryCache.getInstance();
    const keysToDelete = cache.getStats().keys.filter(key => 
      key.includes(`user_stories:${userId}`) || 
      key.includes(`story:`) // Invalidate all stories as user might have updated one
    );
    keysToDelete.forEach(key => cache.delete(key));
  },

  // Invalidate story cache
  invalidateStoryCache: (storyId: string) => {
    const cache = MemoryCache.getInstance();
    cache.delete(`story:${storyId}`);
    // Also invalidate public stories cache as it might include this story
    const keysToDelete = cache.getStats().keys.filter(key => 
      key.startsWith('public_stories:')
    );
    keysToDelete.forEach(key => cache.delete(key));
  },

  // Clear all cache
  clearAll: () => {
    MemoryCache.getInstance().clear();
  },

  // Get cache statistics
  getStats: () => {
    return MemoryCache.getInstance().getStats();
  }
};

// Response caching for API routes
export function withResponseCache<T extends Record<string, unknown>>(
  handler: (req: T, res: { status: (code: number) => { json: (data: unknown) => void }; json: (data: unknown) => void }) => Promise<void>,
  keyGenerator: (req: T) => string,
  ttlMs: number = 300000
) {
  return async (req: T, res: { status: (code: number) => { json: (data: unknown) => void }; json: (data: unknown) => void }) => {
    const cache = MemoryCache.getInstance();
    const key = `response:${keyGenerator(req)}`;
    
    // Try to get cached response
    const cached = cache.get(key);
    if (cached) {
      return res.status(200).json(cached);
    }

    // Execute handler and cache response
    const originalJson = res.json;

    res.json = function(data: unknown) {
      cache.set(key, data, ttlMs);
      return originalJson.call(this, data);
    };

    return handler(req, res);
  };
}

// Cleanup expired cache entries periodically (server-side only)
if (typeof window === 'undefined' && typeof global !== 'undefined') {
  setInterval(() => {
    MemoryCache.getInstance().cleanup();
  }, 600000); // Clean every 10 minutes
}

export default MemoryCache; 