// Utility function to generate consistent colors for themes
// Same themes will always get the same color across the app

const THEME_COLORS = [
  'bg-purple-700',   // Fantasy, Magic, Mystical - WCAG AA compliant
  'bg-pink-700',     // Fairy Tale, Princess, Romance - WCAG AA compliant
  'bg-blue-700',     // Adventure, Ocean, Sky - WCAG AA compliant
  'bg-green-700',    // Nature, Forest, Animals - WCAG AA compliant
  'bg-orange-700',   // Friendship, Warmth, Autumn - WCAG AA compliant
  'bg-red-700',      // Action, Dragons, Fire - WCAG AA compliant
  'bg-indigo-700',   // Mystery, Night, Dreams - WCAG AAA compliant
  'bg-teal-700',     // Underwater, Mermaids, Exploration - WCAG AA compliant
  'bg-yellow-800',   // Sunshine, Happiness, Summer - WCAG AA compliant (yellow needs darker)
  'bg-cyan-700',     // Ice, Winter, Crystal - WCAG AA compliant
  'bg-emerald-700',  // Growth, Spring, Life - WCAG AA compliant
  'bg-rose-700',     // Love, Flowers, Beauty - WCAG AA compliant
  'bg-violet-700',   // Enchantment, Spells, Wonder - WCAG AAA compliant
  'bg-amber-700',    // Treasure, Gold, Ancient - WCAG AA compliant
  'bg-lime-800',     // Fresh, New, Energy - WCAG AA compliant (lime needs darker)
  'bg-fuchsia-700',  // Magical, Sparkle, Fantasy - WCAG AA compliant
  'bg-slate-700',    // Modern, Tech, Urban - WCAG AA compliant
  'bg-stone-700',    // Earth, Solid, Grounded - WCAG AA compliant
  'bg-zinc-700',     // Industrial, Metal, Strong - WCAG AA compliant
  'bg-sky-700',      // Flying, Freedom, Open - WCAG AA compliant
  'bg-purple-800',   // Deep Fantasy, Dark Magic - WCAG AAA compliant
  'bg-pink-800',     // Deep Romance, Elegant - WCAG AAA compliant
  'bg-blue-800',     // Deep Ocean, Night Sky - WCAG AAA compliant
  'bg-green-800',    // Deep Forest, Ancient Nature - WCAG AAA compliant
  'bg-orange-800',   // Deep Warmth, Sunset - WCAG AAA compliant
  'bg-red-800',      // Deep Action, Intense Fire - WCAG AAA compliant
  'bg-indigo-800',   // Deep Mystery, Midnight - WCAG AAA compliant
  'bg-teal-800',     // Deep Ocean, Abyss - WCAG AAA compliant
];

// Improved hash function with better distribution
function hashString(str: string): number {
  let hash = 5381; // Use a different initial value
  const cleanStr = str.toLowerCase().trim();
  
  for (let i = 0; i < cleanStr.length; i++) {
    const char = cleanStr.charCodeAt(i);
    hash = ((hash << 5) + hash) + char; // hash * 33 + char
  }
  
  // Additional mixing to reduce collisions
  hash = hash ^ (hash >>> 16);
  hash = hash * 0x85ebca6b;
  hash = hash ^ (hash >>> 13);
  hash = hash * 0xc2b2ae35;
  hash = hash ^ (hash >>> 16);
  
  return Math.abs(hash);
}

// Get consistent color for a theme
export function getThemeColor(theme: string): string {
  const hash = hashString(theme.toLowerCase().trim());
  const colorIndex = hash % THEME_COLORS.length;
  return THEME_COLORS[colorIndex];
}

// Get multiple theme colors for an array of themes
export function getThemeColors(themes: string[]): string[] {
  return themes.map(theme => getThemeColor(theme));
} 