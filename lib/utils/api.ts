// API Response Types
export interface ApiResponse<T = unknown> {
  data?: T;
  error?: string;
  message?: string;
  upgradeRequired?: boolean;
}

// API Configuration
export interface ApiConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  headers?: Record<string, string>;
  body?: unknown;
  timeout?: number;
}

// Default configuration
const DEFAULT_CONFIG: ApiConfig = {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
};

// Custom error class for API errors
export class ApiError extends Error {
  status: number;
  upgradeRequired?: boolean;

  constructor(message: string, status: number, upgradeRequired?: boolean) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.upgradeRequired = upgradeRequired;
  }
}

// Main API utility function
export async function apiCall<T = unknown>(
  endpoint: string,
  config: ApiConfig = {}
): Promise<T> {
  const mergedConfig = { ...DEFAULT_CONFIG, ...config };
  
  // Create AbortController for timeout
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), mergedConfig.timeout);

  try {
    const fetchOptions: RequestInit = {
      method: mergedConfig.method,
      headers: mergedConfig.headers,
      signal: controller.signal,
    };

    // Add body for non-GET requests
    if (mergedConfig.body && mergedConfig.method !== 'GET') {
      fetchOptions.body = typeof mergedConfig.body === 'string' 
        ? mergedConfig.body 
        : JSON.stringify(mergedConfig.body);
    }

    const response = await fetch(endpoint, fetchOptions);
    clearTimeout(timeoutId);

    // Parse response
    let data: ApiResponse<T>;
    try {
      data = await response.json();
    } catch {
      // If JSON parsing fails, create a generic response
      data = { error: 'Invalid response format' };
    }

    // Handle non-OK responses
    if (!response.ok) {
      const errorMessage = data.error || data.message || `HTTP ${response.status}`;
      throw new ApiError(errorMessage, response.status, data.upgradeRequired);
    }

    // Return the data directly if it exists, otherwise return the full response
    return (data.data !== undefined ? data.data : data) as T;

  } catch (error) {
    clearTimeout(timeoutId);
    
    if (error instanceof ApiError) {
      throw error;
    }
    
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new ApiError('Request timeout', 408);
      }
      throw new ApiError(error.message, 0);
    }
    
    throw new ApiError('Unknown error occurred', 0);
  }
}

// Convenience methods for common HTTP methods
export const api = {
  get: <T = unknown>(endpoint: string, config?: Omit<ApiConfig, 'method'>) =>
    apiCall<T>(endpoint, { ...config, method: 'GET' }),

  post: <T = unknown>(endpoint: string, body?: unknown, config?: Omit<ApiConfig, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...config, method: 'POST', body }),

  put: <T = unknown>(endpoint: string, body?: unknown, config?: Omit<ApiConfig, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...config, method: 'PUT', body }),

  patch: <T = unknown>(endpoint: string, body?: unknown, config?: Omit<ApiConfig, 'method' | 'body'>) =>
    apiCall<T>(endpoint, { ...config, method: 'PATCH', body }),

  delete: <T = unknown>(endpoint: string, config?: Omit<ApiConfig, 'method'>) =>
    apiCall<T>(endpoint, { ...config, method: 'DELETE' }),
};

// Story API types
interface VoteData {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
  userVote: 'up' | 'down' | null;
}

interface VoteResponse {
  counts: {
    upvotes: number;
    downvotes: number;
    totalVotes: number;
    score: number;
  };
  voteType: 'up' | 'down' | null;
}

// Specialized API functions for common patterns
export const storyApi = {
  // Vote on a story
  vote: async (storyId: string | number, voteType: 'up' | 'down', isPublic = false): Promise<VoteResponse> => {
    const endpoint = isPublic 
      ? `/api/public/stories/${storyId}/vote`
      : `/api/stories/${storyId}/vote`;
    
    return api.post<VoteResponse>(endpoint, { voteType });
  },

  // Get vote data for a story
  getVotes: async (storyId: string | number, isPublic = false): Promise<VoteData> => {
    const endpoint = isPublic 
      ? `/api/public/stories/${storyId}/vote`
      : `/api/stories/${storyId}/vote`;
    
    return api.get<VoteData>(endpoint);
  },

  // Get public story by UUID
  getPublicStory: async (uuid: string) => {
    return api.get(`/api/public/stories/${uuid}`);
  },

  // Get featured stories
  getFeaturedStories: async () => {
    return api.get('/api/public/stories/featured');
  },

  // Get public stories list
  getPublicStories: async () => {
    return api.get('/api/public/stories');
  },
};

// Audio API types
interface AudioGenerationResponse {
  audio_url: string;
  message?: string;
}

export const audioApi = {
  // Generate audio for a story
  generateAudio: async (storyId: string, forceRegenerate = false): Promise<AudioGenerationResponse> => {
    return api.post<AudioGenerationResponse>('/api/generate-audio', {
      story_id: storyId,
      force_regenerate: forceRegenerate,
    });
  },

  // Get transcription for a story
  getTranscription: async (storyUuid: string, isPublic = false) => {
    const endpoint = isPublic 
      ? `/api/public/transcriptions/${storyUuid}`
      : `/api/transcriptions/${storyUuid}`;
    
    return api.get(endpoint);
  },
};

// User API types
interface UserPreferences {
  primaryTheme: string;
  secondaryTheme: string;
  preferredAgeRange: string;
  voice: string;
}

export const userApi = {
  // Get user preferences
  getPreferences: async (): Promise<UserPreferences> => {
    return api.get<UserPreferences>('/api/user/preferences');
  },

  // Update user preferences
  updatePreferences: async (preferences: UserPreferences) => {
    return api.post('/api/user/preferences', preferences);
  },
};

export const subscriptionApi = {
  // Get subscription limits
  getLimits: async () => {
    return api.get('/api/subscription/limits');
  },

  // Get premium features
  getPremiumFeatures: async () => {
    return api.get('/api/subscription/premium-features');
  },
};

// Data API types
interface Theme {
  id: string;
  name: string;
  description: string;
}

interface AgeRange {
  id: string;
  range: string;
}

interface Voice {
  id: string;
  name: string;
  gender: string;
  voice: string;
  sample_path?: string;
}

export const dataApi = {
  // Get themes
  getThemes: async (): Promise<Theme[]> => {
    return api.get<Theme[]>('/api/themes');
  },

  // Get age ranges
  getAgeRanges: async (): Promise<AgeRange[]> => {
    return api.get<AgeRange[]>('/api/age-ranges');
  },

  // Get voices
  getVoices: async (): Promise<Voice[]> => {
    return api.get<Voice[]>('/api/voices');
  },

  // Get all data in parallel
  getAllData: async (): Promise<{ themes: Theme[]; ageRanges: AgeRange[]; voices: Voice[] }> => {
    const [themes, ageRanges, voices] = await Promise.all([
      dataApi.getThemes(),
      dataApi.getAgeRanges(),
      dataApi.getVoices(),
    ]);

    return { themes, ageRanges, voices };
  },
};

export const contactApi = {
  // Send contact form
  sendMessage: async (formData: {
    fullName: string;
    email: string;
    subject: string;
    message: string;
    turnstileToken?: string | null;
  }) => {
    return api.post('/api/contact', formData);
  },
};

// Error handling utilities
export const handleApiError = (error: unknown): string => {
  if (error instanceof ApiError) {
    return error.message;
  }
  
  if (error instanceof Error) {
    return error.message;
  }
  
  return 'An unexpected error occurred';
};

export const isUpgradeRequiredError = (error: unknown): boolean => {
  return error instanceof ApiError && error.upgradeRequired === true;
};

// Retry utility for failed requests
export async function retryApiCall<T>(
  apiCallFn: () => Promise<T>,
  maxRetries = 3,
  delay = 1000
): Promise<T> {
  let lastError: unknown;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await apiCallFn();
    } catch (error) {
      lastError = error;
      
      // Don't retry on client errors (4xx) except for 408 (timeout)
      if (error instanceof ApiError && error.status >= 400 && error.status < 500 && error.status !== 408) {
        throw error;
      }
      
      // Don't retry on the last attempt
      if (attempt === maxRetries) {
        break;
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw lastError;
} 