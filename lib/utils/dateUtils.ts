export function getNextMonthResetDate(): Date {
  const now = new Date();
  const nextMonth = new Date(now.getFullYear(), now.getMonth() + 1, 1);
  return nextMonth;
}

export function getDaysUntilReset(): number {
  const now = new Date();
  const resetDate = getNextMonthResetDate();
  const timeDiff = resetDate.getTime() - now.getTime();
  return Math.ceil(timeDiff / (1000 * 3600 * 24));
}

export function formatResetDate(date: Date): string {
  return date.toLocaleDateString('en-US', {
    month: 'long',
    day: 'numeric',
    year: 'numeric'
  });
}

export function getMonthName(date: Date): string {
  return date.toLocaleDateString('en-US', { month: 'long' });
}

export function isNewMonth(lastCheckDate: Date): boolean {
  const now = new Date();
  return now.getMonth() !== lastCheckDate.getMonth() || 
         now.getFullYear() !== lastCheckDate.getFullYear();
}