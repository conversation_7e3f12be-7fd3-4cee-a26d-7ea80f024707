import { auth } from '@/auth';
import { NextResponse } from 'next/server';

export async function getAuthenticatedUser() {
  const session = await auth();

  if (!session?.user?.id) {
    return { user: null, error: NextResponse.json({ error: 'Unauthorized' }, { status: 401 }) };
  }

  return { user: { id: session.user.id, email: session.user.email, name: session.user.name }, error: null };
}

export async function requireAuth() {
  const { user, error } = await getAuthenticatedUser();

  if (error) {
    throw new Error('Unauthorized');
  }

  return user!;
}