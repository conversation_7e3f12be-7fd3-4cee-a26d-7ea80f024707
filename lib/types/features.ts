// Client-side compatible feature types and enums

// Define all available features in the system
export enum Feature {
  // Story features
  STORY_CREATION = 'story_creation',
  STORY_SHARING = 'story_sharing',
  STORY_PDF_EXPORT = 'story_pdf_export',
  
  // Image features
  IMAGE_GENERATION = 'image_generation',
  IMAGE_REGENERATION = 'image_regeneration',
  
  // Audio features
  AUDIO_GENERATION = 'audio_generation',
  AUDIO_PLAYBACK = 'audio_playback',
  PREMIUM_VOICES = 'premium_voices',
  
  // Transcription features
  TRANSCRIPTION_GENERATION = 'transcription_generation',
  TRANSCRIPTION_HIGHLIGHTING = 'transcription_highlighting',
  TRANSCRIPTION_WORD_TIMING = 'transcription_word_timing',
  
  // Advanced features
  PRIORITY_SUPPORT = 'priority_support',
  ADVANCED_ANALYTICS = 'advanced_analytics'
}

// Feature access levels
export enum AccessLevel {
  NONE = 'none',
  LIMITED = 'limited', 
  FULL = 'full'
}

export interface FeatureAccess {
  hasAccess: boolean;
  accessLevel: AccessLevel;
  limit?: number;
  remaining?: number;
  upgradeRequired: boolean;
  requiredPlan?: string;
} 