import { r2Storage, StorageType } from './services/r2StorageService';

/**
 * Gets a safe image URL that doesn't expose user IDs (recommended)
 * @param storyUuid - The story UUID
 * @param cacheBust - Cache busting parameter (timestamp or boolean)
 */
export function getSafeImageUrl(storyUuid: string, cacheBust?: boolean | string): string {
  const baseUrl = `/api/images/${storyUuid}`;
  
  if (cacheBust === true) {
    return `${baseUrl}?t=${Date.now()}`;
  } else if (typeof cacheBust === 'string') {
    return `${baseUrl}?t=${cacheBust}`;
  }
  
  return baseUrl;
}

/**
 * @deprecated Use getSafeImageUrl instead. This function can expose user IDs in URLs.
 * Gets the secure URL for an image that goes through our API
 * @param storyUuid - The story UUID (for backward compatibility)
 * @param cacheBust - Cache busting parameter
 * @param storagePath - Optional storage path for direct access (SECURITY RISK: can expose user IDs)
 */
export function getImageSecureUrl(storyUuid: string, cacheBust?: boolean | string, storagePath?: string): string {
  // If storage path is provided, use the path-based endpoint
  if (storagePath) {
    const baseUrl = `/api/images/${storagePath}`;
    
    if (cacheBust === true) {
      return `${baseUrl}?t=${Date.now()}`;
    } else if (typeof cacheBust === 'string') {
      return `${baseUrl}?t=${cacheBust}`;
    }
    
    return baseUrl;
  }
  
  // Fallback to UUID-based endpoint (legacy)
  const baseUrl = `/api/images/${storyUuid}`;
  
  if (cacheBust === true) {
    return `${baseUrl}?t=${Date.now()}`;
  } else if (typeof cacheBust === 'string') {
    // Use provided timestamp for consistent cache busting
    return `${baseUrl}?t=${cacheBust}`;
  }
  
  return baseUrl;
}

/**
 * Gets a safe audio URL that doesn't expose user IDs
 * Uses story ID endpoint which is secure and user-private
 */
export function getAudioSecureUrl(storyId: string): string {
  return `/api/audio/${storyId}`;
}

/**
 * Deletes an image from R2 storage
 */
export async function deleteImage(storagePath: string): Promise<boolean> {
  try {
    return await r2Storage.deleteFile('images', storagePath);
  } catch (error) {
    console.error('Error deleting image:', error);
    return false;
  }
}

/**
 * Deletes an audio file from R2 storage
 */
export async function deleteAudio(storagePath: string): Promise<boolean> {
  try {
    return await r2Storage.deleteFile('audio', storagePath);
  } catch (error) {
    console.error('Error deleting audio:', error);
    return false;
  }
}

/**
 * Uploads a file to R2 storage
 */
export async function uploadFile(
  type: StorageType,
  key: string,
  body: Buffer | Uint8Array | Blob,
  contentType: string
): Promise<string> {
  return await r2Storage.uploadFile(type, key, body, contentType);
}

/**
 * Downloads a file from R2 storage
 */
export async function downloadFile(type: StorageType, key: string): Promise<Uint8Array> {
  return await r2Storage.downloadFile(type, key);
}

/**
 * Checks if a file exists in R2 storage
 */
export async function fileExists(type: StorageType, key: string): Promise<boolean> {
  return await r2Storage.fileExists(type, key);
}

/**
 * Gets a signed URL for a file (useful for external services like AssemblyAI)
 */
export async function getSignedUrl(type: StorageType, key: string, expiresIn: number = 3600): Promise<string> {
  return await r2Storage.getSignedUrl(type, key, expiresIn);
}

/**
 * Gets file metadata
 */
export async function getFileMetadata(type: StorageType, key: string) {
  return await r2Storage.getFileMetadata(type, key);
}

// Re-export the storage service for direct access if needed
export { r2Storage }; 