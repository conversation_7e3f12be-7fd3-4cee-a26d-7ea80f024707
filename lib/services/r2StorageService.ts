import { S3Client, PutObjectCommand, GetObjectCommand, DeleteObjectCommand, HeadObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

export type StorageType = 'images' | 'audio';

export class R2StorageService {
  private s3Client: S3Client;
  private imagesBucket: string;
  private audioBucket: string;

  constructor() {
    this.imagesBucket = process.env.R2_IMAGES_BUCKET_NAME!;
    this.audioBucket = process.env.R2_AUDIO_BUCKET_NAME!;
    
    if (!this.imagesBucket || !this.audioBucket) {
      throw new Error('R2 bucket names must be configured in environment variables');
    }

    this.s3Client = new S3Client({
      region: 'auto', // R2 uses 'auto' for region
      endpoint: process.env.R2_ENDPOINT!,
      credentials: {
        accessKeyId: process.env.R2_ACCESS_KEY_ID!,
        secretAccessKey: process.env.R2_SECRET_ACCESS_KEY!,
      },
    });
  }

  private getBucketName(type: StorageType): string {
    return type === 'images' ? this.imagesBucket : this.audioBucket;
  }

  async uploadFile(
    type: StorageType,
    key: string, 
    body: Buffer | Uint8Array | Blob, 
    contentType: string
  ): Promise<string> {
    try {
      const command = new PutObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
        Body: body,
        ContentType: contentType,
        // Add cache control headers
        CacheControl: type === 'images' ? 'public, max-age=31536000, immutable' : 'private, max-age=3600',
      });

      await this.s3Client.send(command);
      return key;
    } catch (error) {
      console.error(`R2 upload error for ${type}:`, error);
      throw new Error(`Failed to upload ${type} file to R2`);
    }
  }

  async downloadFile(type: StorageType, key: string): Promise<Uint8Array> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      if (!response.Body) {
        throw new Error('No file body returned');
      }

      // Convert stream to Uint8Array
      const chunks: Uint8Array[] = [];
      const reader = response.Body.transformToWebStream().getReader();
      
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        chunks.push(value);
      }

      // Combine chunks
      const totalLength = chunks.reduce((sum, chunk) => sum + chunk.length, 0);
      const result = new Uint8Array(totalLength);
      let offset = 0;
      
      for (const chunk of chunks) {
        result.set(chunk, offset);
        offset += chunk.length;
      }

      return result;
    } catch (error) {
      console.error(`R2 download error for ${type}:`, error);
      throw new Error(`Failed to download ${type} file from R2`);
    }
  }

  async deleteFile(type: StorageType, key: string): Promise<boolean> {
    try {
      const command = new DeleteObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch (error) {
      console.error(`R2 delete error for ${type}:`, error);
      return false;
    }
  }

  async fileExists(type: StorageType, key: string): Promise<boolean> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
      });

      await this.s3Client.send(command);
      return true;
    } catch {
      return false;
    }
  }

  async getSignedUrl(type: StorageType, key: string, expiresIn: number = 3600): Promise<string> {
    try {
      const command = new GetObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
      });

      return await getSignedUrl(this.s3Client, command, { expiresIn });
    } catch (error) {
      console.error(`R2 signed URL error for ${type}:`, error);
      throw new Error(`Failed to create signed URL for ${type}`);
    }
  }

  async getFileMetadata(type: StorageType, key: string): Promise<{
    contentType?: string;
    contentLength?: number;
    lastModified?: Date;
  }> {
    try {
      const command = new HeadObjectCommand({
        Bucket: this.getBucketName(type),
        Key: key,
      });

      const response = await this.s3Client.send(command);
      
      return {
        contentType: response.ContentType,
        contentLength: response.ContentLength,
        lastModified: response.LastModified,
      };
    } catch (error) {
      console.error(`R2 metadata error for ${type}:`, error);
      throw new Error(`Failed to get metadata for ${type} file`);
    }
  }
}

// Singleton instance
export const r2Storage = new R2StorageService(); 