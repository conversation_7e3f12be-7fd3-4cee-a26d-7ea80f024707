import { query } from '../postgres-client';

export interface Tag {
  id: number;
  name: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

export interface CreateTagData {
  name: string;
  user_id: string;
}

export interface UpdateTagData {
  name: string;
}

export class TagService {
  static async getUserTags(userId: string): Promise<Tag[]> {
    try {
      const result = await query(
        'SELECT * FROM tags WHERE user_id = $1 ORDER BY created_at DESC',
        [userId]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to fetch tags: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async createTag(data: CreateTagData): Promise<Tag> {
    try {
      const result = await query(
        'INSERT INTO tags (name, user_id, created_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING *',
        [data.name.trim(), data.user_id, new Date().toISOString(), new Date().toISOString()]
      );

      if (result.rows.length === 0) {
        throw new Error('Failed to create tag');
      }

      return result.rows[0];
    } catch (error) {
      throw new Error(`Failed to create tag: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async updateTag(id: number, data: UpdateTagData, userId: string): Promise<Tag> {
    try {
      const result = await query(
        'UPDATE tags SET name = $1, updated_at = $2 WHERE id = $3 AND user_id = $4 RETURNING *',
        [data.name.trim(), new Date().toISOString(), id, userId]
      );

      if (result.rows.length === 0) {
        throw new Error('Tag not found or unauthorized');
      }

      return result.rows[0];
    } catch (error) {
      throw new Error(`Failed to update tag: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async deleteTag(id: number, userId: string): Promise<void> {
    try {
      const result = await query(
        'DELETE FROM tags WHERE id = $1 AND user_id = $2',
        [id, userId]
      );

      if (result.rowCount === 0) {
        throw new Error('Tag not found or unauthorized');
      }
    } catch (error) {
      throw new Error(`Failed to delete tag: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async getTagById(id: number, userId: string): Promise<Tag | null> {
    try {
      const result = await query(
        'SELECT * FROM tags WHERE id = $1 AND user_id = $2',
        [id, userId]
      );

      return result.rows.length > 0 ? result.rows[0] : null;
    } catch (error) {
      throw new Error(`Failed to fetch tag: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async searchTags(userId: string, searchTerm: string): Promise<Tag[]> {
    try {
      const result = await query(
        'SELECT * FROM tags WHERE user_id = $1 AND name ILIKE $2 ORDER BY created_at DESC',
        [userId, `%${searchTerm}%`]
      );

      return result.rows;
    } catch (error) {
      throw new Error(`Failed to search tags: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  static async getTagCount(userId: string): Promise<number> {
    try {
      const result = await query(
        'SELECT COUNT(*) as count FROM tags WHERE user_id = $1',
        [userId]
      );

      return parseInt(result.rows[0].count) || 0;
    } catch (error) {
      throw new Error(`Failed to get tag count: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
}