import Stripe from 'stripe';
import { query } from '../postgres-client';

if (!process.env.STRIPE_SECRET_KEY) {
  throw new Error('STRIPE_SECRET_KEY is not set in environment variables');
}

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
  apiVersion: '2025-05-28.basil',
});

export interface StripeSubscriptionData {
  stripeCustomerId: string;
  stripeSubscriptionId: string;
  stripePriceId: string;
  status: string;
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  cancelAtPeriodEnd: boolean;
}

export class StripeService {
  // Create or get Stripe customer
  async createOrGetCustomer(userId: string, email: string, name?: string): Promise<string> {
    try {
      // Check if user already has a Stripe customer ID
      const result = await query(
        'SELECT stripe_customer_id FROM users WHERE id = $1',
        [userId]
      );

      if (result.rows[0]?.stripe_customer_id) {
        return result.rows[0].stripe_customer_id;
      }

      // Create new Stripe customer
      const customer = await stripe.customers.create({
        email,
        name,
        metadata: {
          userId: userId,
        },
      });

      // Save customer ID to database
      await query(
        'UPDATE users SET stripe_customer_id = $1 WHERE id = $2',
        [customer.id, userId]
      );

      return customer.id;
    } catch (error) {
      console.error('Error creating/getting Stripe customer:', error);
      throw new Error('Failed to create customer');
    }
  }

  // Create checkout session for subscription
  async createCheckoutSession(
    userId: string,
    email: string,
    priceId: string,
    planName: string,
    billingPeriod: 'monthly' | 'yearly',
    successUrl: string,
    cancelUrl: string
  ): Promise<Stripe.Checkout.Session> {
    try {
  
      
      const customerId = await this.createOrGetCustomer(userId, email);

      const session = await stripe.checkout.sessions.create({
        customer: customerId,
        payment_method_types: ['card'],
        line_items: [
          {
            price: priceId,
            quantity: 1,
          },
        ],
        mode: 'subscription',
        success_url: successUrl,
        cancel_url: cancelUrl,
        metadata: {
          userId: userId,
          planName: planName,
        },
        subscription_data: {
          metadata: {
            userId: userId,
            planName: planName,
          },
        },
      });

      // Store checkout session in database
      await this.saveCheckoutSession(userId, session, planName, billingPeriod, successUrl, cancelUrl);

      return session;
    } catch (error) {
      console.error('Error creating checkout session:', error);
      console.error('URLs used:', { successUrl, cancelUrl });
      throw new Error('Failed to create checkout session');
    }
  }

  // Save checkout session to database
  private async saveCheckoutSession(
    userId: string,
    session: Stripe.Checkout.Session,
    planName: string,
    billingPeriod: 'monthly' | 'yearly',
    successUrl: string,
    cancelUrl: string
  ): Promise<void> {
    try {
      // Validate expires_at timestamp
      let expiresAt = null;
      if (session.expires_at) {
        const expiresDate = new Date(session.expires_at * 1000);
        if (!isNaN(expiresDate.getTime())) {
          expiresAt = expiresDate;
        }
      }

      await query(`
        INSERT INTO stripe_checkout_sessions (
          user_id,
          stripe_session_id,
          stripe_customer_id,
          plan_name,
          billing_period,
          amount_total,
          currency,
          status,
          success_url,
          cancel_url,
          expires_at
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
      `, [
        userId,
        session.id,
        session.customer,
        planName,
        billingPeriod,
        session.amount_total,
        session.currency,
        session.status,
        successUrl,
        cancelUrl,
        expiresAt
      ]);
    } catch (error) {
      console.error('Error saving checkout session:', error);
      // Don't throw here - checkout session creation succeeded
    }
  }

  // Create billing portal session for subscription management
  async createBillingPortalSession(customerId: string, returnUrl: string): Promise<string> {
    try {
      const session = await stripe.billingPortal.sessions.create({
        customer: customerId,
        return_url: returnUrl,
      });

      return session.url;
    } catch (error) {
      console.error('Error creating billing portal session:', error);
      throw new Error('Failed to create billing portal session');
    }
  }

  // Handle subscription webhook events
  async handleSubscriptionUpdate(subscription: Stripe.Subscription): Promise<void> {
    try {


      const userId = subscription.metadata.userId;
      const planName = subscription.metadata.planName;

      if (!userId || !planName) {
        console.error('Missing metadata in subscription:', subscription.id);
        return;
      }

      // Get plan details
      const planResult = await query(
        'SELECT * FROM subscription_plans WHERE name = $1',
        [planName]
      );

      if (planResult.rows.length === 0) {
        console.error('Plan not found:', planName);
        
        // Try to find a default plan or create a basic subscription record
        const defaultPlanResult = await query(
          'SELECT * FROM subscription_plans WHERE name = $1',
          ['free']
        );
        
        if (defaultPlanResult.rows.length === 0) {
          console.error('No default plan found, skipping subscription update');
          return;
        }
        

        planResult.rows[0] = defaultPlanResult.rows[0];
      }

      const plan = planResult.rows[0];
      const status = this.mapStripeStatusToLocal(subscription.status);

      // Extract and validate timestamp values
      let startDate = this.extractStripeTimestamp(subscription, 'current_period_start');
      let endDate = this.extractStripeTimestamp(subscription, 'current_period_end');
      
      // If timestamps are missing, fetch the complete subscription from Stripe
      if (!startDate || !endDate) {

        try {
          const fullSubscription = await stripe.subscriptions.retrieve(subscription.id);
          startDate = this.extractStripeTimestamp(fullSubscription, 'current_period_start');
          endDate = this.extractStripeTimestamp(fullSubscription, 'current_period_end');
          

        } catch (fetchError) {
          console.error('Failed to fetch complete subscription:', fetchError);
        }
      }
      
      // If we still don't have valid timestamps, use fallback dates
      if (!startDate || !endDate) {
        console.warn('Using fallback dates for subscription:', subscription.id);
        const now = new Date();
        startDate = now;
        endDate = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 days from now
      }

      // Update or create user subscription
      await query(`
        INSERT INTO user_subscriptions (
          user_id, 
          plan_id, 
          status, 
          current_period_start, 
          current_period_end,
          cancel_at_period_end,
          stripe_subscription_id,
          stripe_customer_id
        ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
        ON CONFLICT (user_id) 
        DO UPDATE SET 
          plan_id = $2,
          status = $3,
          current_period_start = $4,
          current_period_end = $5,
          cancel_at_period_end = $6,
          stripe_subscription_id = $7,
          updated_at = NOW()
      `, [
        userId,
        plan.id,
        status,
        startDate,
        endDate,
        subscription.cancel_at_period_end || false,
        subscription.id,
        subscription.customer
      ]);


    } catch (error) {
      console.error('Error handling subscription update:', error);
      throw error;
    }
  }

  // Handle subscription deletion
  async handleSubscriptionDeleted(subscription: Stripe.Subscription): Promise<void> {
    try {
      const userId = subscription.metadata.userId;

      if (!userId) {
        console.error('Missing userId in deleted subscription:', subscription.id);
        return;
      }

      // Set subscription to cancelled
      await query(
        'UPDATE user_subscriptions SET status = $1, updated_at = NOW() WHERE user_id = $2',
        ['cancelled', userId]
      );


    } catch (error) {
      console.error('Error handling subscription deletion:', error);
      throw error;
    }
  }

  // Handle checkout session completion
  async handleCheckoutSessionCompleted(session: Stripe.Checkout.Session): Promise<void> {
    try {
      // Update checkout session status
      await query(`
        UPDATE stripe_checkout_sessions 
        SET 
          status = $1,
          payment_status = $2,
          completed_at = NOW(),
          updated_at = NOW()
        WHERE stripe_session_id = $3
      `, [
        session.status,
        session.payment_status,
        session.id
      ]);


    } catch (error) {
      console.error('Error handling checkout session completion:', error);
      throw error;
    }
  }

  // Get checkout session from database
  async getCheckoutSession(sessionId: string): Promise<{
    id: number;
    user_id: number;
    stripe_session_id: string;
    stripe_customer_id: string;
    plan_name: string;
    billing_period: string;
    amount_total: number;
    currency: string;
    status: string;
    payment_status: string;
    success_url: string;
    cancel_url: string;
    expires_at: Date;
    completed_at: Date;
    created_at: Date;
    updated_at: Date;
  } | null> {
    try {
      const result = await query(
        'SELECT * FROM stripe_checkout_sessions WHERE stripe_session_id = $1',
        [sessionId]
      );
      return result.rows[0] || null;
    } catch (error) {
      console.error('Error fetching checkout session:', error);
      return null;
    }
  }

  // Get subscription details from Stripe
  async getStripeSubscription(subscriptionId: string): Promise<Stripe.Subscription | null> {
    try {
      const subscription = await stripe.subscriptions.retrieve(subscriptionId);
      return subscription;
    } catch (error) {
      console.error('Error fetching Stripe subscription:', error);
      return null;
    }
  }

  // Cancel subscription (at period end)
  async cancelSubscription(subscriptionId: string): Promise<void> {
    try {
      await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: true,
      });
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  // Immediately cancel subscription
  async cancelSubscriptionImmediately(subscriptionId: string): Promise<void> {
    try {
      await stripe.subscriptions.cancel(subscriptionId);
    } catch (error) {
      console.error('Error cancelling subscription immediately:', error);
      throw new Error('Failed to cancel subscription immediately');
    }
  }

  // Reactivate cancelled subscription
  async reactivateSubscription(subscriptionId: string): Promise<void> {
    try {
      await stripe.subscriptions.update(subscriptionId, {
        cancel_at_period_end: false,
      });
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      throw new Error('Failed to reactivate subscription');
    }
  }

  // Map Stripe status to local status
  private mapStripeStatusToLocal(stripeStatus: string): 'active' | 'cancelled' | 'expired' {
    switch (stripeStatus) {
      case 'active':
      case 'trialing':
        return 'active';
      case 'canceled':
      case 'unpaid':
        return 'cancelled';
      case 'past_due':
      case 'incomplete':
      case 'incomplete_expired':
        return 'expired';
      default:
        return 'cancelled';
    }
  }

  // Safely extract timestamp from Stripe object
  private extractStripeTimestamp(obj: unknown, fieldName: string): Date | null {
    const typedObj = obj as Record<string, unknown>;
    const timestamp = typedObj[fieldName] || typedObj[this.camelCase(fieldName)];
    
    if (!timestamp || typeof timestamp !== 'number') {
      return null;
    }

    const date = new Date(timestamp * 1000);
    return isNaN(date.getTime()) ? null : date;
  }

  // Convert snake_case to camelCase
  private camelCase(str: string): string {
    return str.replace(/_([a-z])/g, (_, letter) => letter.toUpperCase());
  }

  // Get price IDs for plans (these need to be configured in Stripe dashboard)
  getPriceIds(): Record<string, { monthly: string; yearly: string }> {
    return {
      starter: {
        monthly: process.env.STRIPE_STARTER_MONTHLY_PRICE_ID || '',
        yearly: process.env.STRIPE_STARTER_YEARLY_PRICE_ID || '',
      },
      family: {
        monthly: process.env.STRIPE_FAMILY_MONTHLY_PRICE_ID || '',
        yearly: process.env.STRIPE_FAMILY_YEARLY_PRICE_ID || '',
      },
    };
  }
}

export const stripeService = new StripeService(); 