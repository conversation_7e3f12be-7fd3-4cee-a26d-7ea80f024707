import { AssemblyAI } from 'assemblyai';
import { query } from '../postgres-client';

interface WordTimestamp {
  word: string;
  start: number;
  end: number;
  confidence: number;
}

interface TranscriptionResult {
  text: string;
  words: WordTimestamp[];
  id: string;
}



export class TranscriptionService {
  private client: AssemblyAI;

  constructor() {
    const apiKey = process.env.ASSEMBLYAI_API_KEY;
    if (!apiKey) {
      throw new Error('ASSEMBLYAI_API_KEY environment variable is required');
    }
    this.client = new AssemblyAI({ apiKey });
  }

  /**
   * Transcribe audio using AssemblyAI
   */
  async transcribeAudio(audioUrl: string): Promise<TranscriptionResult> {
    try {
      const config = {
        audio: audioUrl,
        format_text: true,
        punctuate: true,
        speech_model: "universal" as const,
        language_code: "en_us",
      };

      const transcript = await this.client.transcripts.transcribe(config);

      if (transcript.status === 'error') {
        throw new Error(`Transcription failed: ${transcript.error}`);
      }

      if (!transcript.text) {
        throw new Error('Transcription completed but no text returned');
      }

      // Check if words are available - if not, create empty array for now
      if (!transcript.words || transcript.words.length === 0) {
        return {
          text: transcript.text,
          words: [], // Empty array - highlighting won't work but transcription will
          id: transcript.id
        };
      }

      return {
        text: transcript.text,
        words: transcript.words.map(word => ({
          word: word.text,
          start: word.start,
          end: word.end,
          confidence: word.confidence
        })),
        id: transcript.id
      };
    } catch (error) {
      throw new Error(`Failed to transcribe audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get transcription status ID from transcription_status table
   */
  async getTranscriptionStatusId(status: 'not_started' | 'generating' | 'complete' | 'failed'): Promise<number> {
    try {
      const result = await query(
        'SELECT id FROM transcription_status WHERE status = $1',
        [status]
      );

      if (result.rows.length === 0) {
        throw new Error(`Transcription status '${status}' not found in database`);
      }

      return result.rows[0].id;
    } catch (error) {
      throw error;
    }
  }

  /**
   * Set transcription status in audio table using status ID
   */
  async setTranscriptionStatus(
    storyId: number, 
    userId: string, 
    status: 'not_started' | 'generating' | 'complete' | 'failed'
  ): Promise<void> {
    try {
      // Get the status ID from the transcription_status table
      const statusId = await this.getTranscriptionStatusId(status);

      await query(
        'UPDATE audio SET transcription_status = $1, updated_at = $2 WHERE story_id = $3 AND user_id = $4',
        [statusId, new Date().toISOString(), storyId, userId]
      );
    } catch (error) {
      throw new Error(`Failed to set transcription status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Save transcription data to audio table
   */
  async saveTranscription(
    storyId: number, 
    userId: string, 
    transcriptionData: TranscriptionResult
  ): Promise<void> {
    try {
      // Get the 'complete' status ID
      const completeStatusId = await this.getTranscriptionStatusId('complete');

      // Update the existing audio record with transcription data
      await query(
        `UPDATE audio SET 
         transcription_text = $1, 
         transcription_words = $2, 
         transcription_status = $3, 
         updated_at = $4 
         WHERE story_id = $5 AND user_id = $6`,
        [
          transcriptionData.text,
          JSON.stringify(transcriptionData.words),
          completeStatusId,
          new Date().toISOString(),
          storyId,
          userId
        ]
      );
    } catch (error) {
      throw new Error(`Failed to save transcription: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Get existing transcription for a story from audio table
   */
  async getTranscription(storyId: number, userId: string): Promise<TranscriptionResult | null> {
    try {
      const result = await query(
        `SELECT a.transcription_text, a.transcription_words, ts.status
         FROM audio a
         LEFT JOIN transcription_status ts ON a.transcription_status = ts.id
         WHERE a.story_id = $1 AND a.user_id = $2`,
        [storyId, userId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      const audioRecord = result.rows[0];

      // Check if transcription is complete and data exists
      if (audioRecord.status !== 'complete' || 
          !audioRecord.transcription_text || 
          !audioRecord.transcription_words) {
        return null;
      }

      // Parse transcription_words safely
      let words = [];
      try {
        if (audioRecord.transcription_words) {
          // If it's already an object/array, use it directly
          if (typeof audioRecord.transcription_words === 'object') {
            words = audioRecord.transcription_words;
          } else {
            // If it's a string, parse it
            words = JSON.parse(audioRecord.transcription_words);
          }
        }
      } catch {
        words = []; // Fallback to empty array
      }

      return {
        text: audioRecord.transcription_text,
        words: words,
        id: '' // We don't need assemblyai_id for playback
      };
    } catch {
      return null;
    }
  }

  /**
   * Get transcription status for a story
   */
  async getTranscriptionStatus(storyId: number, userId: string): Promise<string | null> {
    try {
      const result = await query(
        `SELECT ts.status
         FROM audio a
         LEFT JOIN transcription_status ts ON a.transcription_status = ts.id
         WHERE a.story_id = $1 AND a.user_id = $2`,
        [storyId, userId]
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0].status || 'not_started';
    } catch {
      return null;
    }
  }

  /**
   * Get the public URL for an audio file to send to AssemblyAI
   */
  async getPublicAudioUrl(storyId: string, userId: string): Promise<string> {
    try {
      // Get the storage path from the database
      const result = await query(
        'SELECT storage_path FROM audio WHERE story_id = $1 AND user_id = $2',
        [parseInt(storyId), userId]
      );

      if (result.rows.length === 0) {
        throw new Error('Audio file not found');
      }

      // Import R2 storage utils
      const { getSignedUrl } = await import('../r2-storage-utils');
      
      // Create a signed URL that AssemblyAI can access
      const signedUrl = await getSignedUrl('audio', result.rows[0].storage_path, 3600); // 1 hour expiry

      return signedUrl;
    } catch (error) {
      throw error;
    }
  }
} 