import { query } from '../postgres-client';
import { SubscriptionService } from './subscriptionService';

export interface AccessCode {
  id: number;
  code: string;
  plan: string;
  expires_at: Date | null;
  max_uses: number | null;
  uses: number;
  active: boolean;
  duration_months: number | null;
  notes: string | null;
  created_by: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface AccessCodeValidationResult {
  valid: boolean;
  message: string;
  accessCode?: AccessCode;
}

export interface AccessCodeRedemptionResult {
  success: boolean;
  message: string;
  newPlan?: string;
}

export class AccessCodeService {
  private subscriptionService: SubscriptionService;

  constructor() {
    this.subscriptionService = new SubscriptionService();
  }

  async validateAccessCode(code: string): Promise<AccessCodeValidationResult> {
    try {
      const result = await query(
        'SELECT * FROM access_codes WHERE code = $1 AND active = true',
        [code.trim().toUpperCase()]
      );

      if (result.rows.length === 0) {
        return {
          valid: false,
          message: 'Invalid access code'
        };
      }

      const accessCode = result.rows[0];

      // Check if expired
      if (accessCode.expires_at && new Date(accessCode.expires_at) < new Date()) {
        return {
          valid: false,
          message: 'Access code has expired'
        };
      }

      // Check if max uses reached
      if (accessCode.max_uses && accessCode.uses >= accessCode.max_uses) {
        return {
          valid: false,
          message: 'Access code has reached maximum uses'
        };
      }

      return {
        valid: true,
        message: `Valid access code for ${accessCode.plan} plan`,
        accessCode: {
          id: accessCode.id,
          code: accessCode.code,
          plan: accessCode.plan,
          expires_at: accessCode.expires_at ? new Date(accessCode.expires_at) : null,
          max_uses: accessCode.max_uses,
          uses: accessCode.uses,
          active: accessCode.active,
          duration_months: accessCode.duration_months,
          notes: accessCode.notes,
          created_by: accessCode.created_by,
          created_at: new Date(accessCode.created_at),
          updated_at: new Date(accessCode.updated_at)
        }
      };
    } catch (error) {
      console.error('Error validating access code:', error);
      return {
        valid: false,
        message: 'Error validating access code'
      };
    }
  }

  async redeemAccessCode(userId: string, code: string): Promise<AccessCodeRedemptionResult> {
    try {
      // First validate the code
      const validation = await this.validateAccessCode(code);
      if (!validation.valid || !validation.accessCode) {
        return {
          success: false,
          message: validation.message
        };
      }

      const accessCode = validation.accessCode;

      // Check if user already has a premium plan
      const currentPlan = await this.subscriptionService.getUserPlan(userId);
      if (currentPlan !== 'free') {
        return {
          success: false,
          message: 'You already have an active subscription'
        };
      }

      // Create subscription for the user WITHOUT Stripe billing
      // This creates a subscription that won't attempt to bill through Stripe
      const durationMonths = accessCode.duration_months || 12; // Default to 12 months if not specified
      await this.subscriptionService.createAccessCodeSubscription(userId, accessCode.plan, durationMonths);

      // Increment usage count
      await query(
        'UPDATE access_codes SET uses = uses + 1, updated_at = NOW() WHERE id = $1',
        [accessCode.id]
      );

      return {
        success: true,
        message: `Successfully activated ${accessCode.plan} plan!`,
        newPlan: accessCode.plan
      };
    } catch (error) {
      console.error('Error redeeming access code:', error);
      return {
        success: false,
        message: 'Error redeeming access code'
      };
    }
  }

  async getUserAccessCodeHistory(): Promise<AccessCode[]> {
    try {
      // This would require a user_access_codes table to track which codes were used by which users
      // For now, we'll return empty array since we don't have that tracking
      return [];
    } catch (error) {
      console.error('Error fetching user access code history:', error);
      return [];
    }
  }
} 