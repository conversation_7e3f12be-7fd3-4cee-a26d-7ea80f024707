interface StoryRequest {
  mainCharacter: string;
  setting: string;
  theme: string[];
  details: string;
  ageRange: string;
  tags: string[];
  user_id: string;
}

interface StoryResponse {
  title: string;
  story?: string;
  content?: string;
  image?: string;
  image_prompt?: string;
}

export class StoryService {
  private apiEndpoint: string;
  private apiToken: string;

  constructor() {
    this.apiEndpoint = process.env.STORY_API_ENDPOINT!;
    this.apiToken = process.env.STORY_API_TOKEN!;
    
    if (!this.apiEndpoint || !this.apiToken) {
      throw new Error('Missing API configuration');
    }
  }

  async generateStory(requestData: StoryRequest): Promise<StoryResponse> {

    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiToken}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      console.error('External API error:', response.status, response.statusText);
      throw new Error(`Failed to create story: ${response.status}`);
    }

    const responseText = await response.text();
    
    if (!responseText) {
      throw new Error('Empty response from story API');
    }

    try {
      const result = JSON.parse(responseText);
      return result;
    } catch (parseError) {
      console.error('Failed to parse API response:', parseError);
      // Only log first 200 characters of response for debugging
      console.error('Response preview:', responseText.substring(0, 200) + '...');
      throw new Error('Invalid response from story API');
    }
  }
} 