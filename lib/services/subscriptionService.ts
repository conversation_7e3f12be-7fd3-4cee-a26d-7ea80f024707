import { query } from '../postgres-client';
import { auth } from '@/auth';



export interface SubscriptionPlan {
  id: number;
  name: string;
  display_name: string;
  price_monthly: number;
  price_yearly: number;
  stories_per_month: number;
  image_regenerations_per_story: number;
  features: Record<string, boolean>;
  active: boolean;
}

export interface UserSubscription {
  id: number;
  user_id: number;
  plan_id: number;
  status: 'active' | 'cancelled' | 'expired';
  current_period_start: Date;
  current_period_end: Date;
  cancel_at_period_end: boolean;
  stripe_subscription_id?: string | null;
  stripe_customer_id?: string | null;
  stripe_price_id?: string | null;
  plan: SubscriptionPlan;
}

// Helper method to get free plan limits from database
async function getFreePlanLimits(): Promise<{
  storiesPerMonth: number;
  imageRegenerationsPerStory: number;
  planName: string;
}> {
  try {
    const result = await query(
      'SELECT stories_per_month, image_regenerations_per_story, display_name FROM subscription_plans WHERE name = $1 AND active = true',
      ['free']
    );
    
    if (result.rows.length > 0) {
      const plan = result.rows[0];
      return {
        storiesPerMonth: plan.stories_per_month,
        imageRegenerationsPerStory: plan.image_regenerations_per_story,
        planName: plan.display_name
      };
    }
  } catch (error) {
    console.error('Error fetching free plan limits:', error);
  }
  
  // Fallback if database query fails
  return {
    storiesPerMonth: 3,
    imageRegenerationsPerStory: 2,
    planName: 'Free Plan'
  };
}

export class SubscriptionService {
  async getUserPlan(userId?: string): Promise<string> {
    try {
      let currentUserId = userId;
      
      if (!currentUserId) {
        const session = await auth();
        if (!session?.user?.id) {
          return 'free';
        }
        currentUserId = session.user.id;
      }

      const result = await query(`
        SELECT sp.name 
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = $1 
        AND us.status = 'active' 
        AND us.current_period_end > NOW()
      `, [currentUserId]);

      return result.rows[0]?.name || 'free';
    } catch (error) {
      console.error('Error fetching user plan:', error);
      return 'free';
    }
  }

  async getUserSubscription(userId?: string): Promise<UserSubscription | null> {
    try {
      let currentUserId = userId;
      
      if (!currentUserId) {
        const session = await auth();
        if (!session?.user?.id) {
          return null;
        }
        currentUserId = session.user.id;
      }

      const result = await query(`
        SELECT 
          us.*,
          sp.name as plan_name,
          sp.display_name,
          sp.price_monthly,
          sp.price_yearly,
          sp.stories_per_month,
          sp.image_regenerations_per_story,
          sp.features,
          sp.active
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = $1 
        AND us.status = 'active'
      `, [currentUserId]);

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        id: row.id,
        user_id: row.user_id,
        plan_id: row.plan_id,
        status: row.status,
        current_period_start: new Date(row.current_period_start),
        current_period_end: new Date(row.current_period_end),
        cancel_at_period_end: row.cancel_at_period_end,
        stripe_subscription_id: row.stripe_subscription_id,
        stripe_customer_id: row.stripe_customer_id,
        stripe_price_id: row.stripe_price_id,
        plan: {
          id: row.plan_id,
          name: row.plan_name,
          display_name: row.display_name,
          price_monthly: parseFloat(row.price_monthly),
          price_yearly: parseFloat(row.price_yearly),
          stories_per_month: row.stories_per_month,
          image_regenerations_per_story: row.image_regenerations_per_story,
          features: row.features,
          active: row.active
        }
      };
    } catch (error) {
      console.error('Error fetching user subscription:', error);
      return null;
    }
  }

  async hasPremiumFeatures(userId?: string): Promise<boolean> {
    try {
      const userPlan = await this.getUserPlan(userId);
      return userPlan !== 'free';
    } catch (error) {
      console.error('Error checking premium features:', error);
      return false;
    }
  }

  async hasFamilyPlanAccess(userId?: string): Promise<boolean> {
    try {
      const userPlan = await this.getUserPlan(userId);
      return userPlan === 'family';
    } catch (error) {
      console.error('Error checking family plan access:', error);
      return false;
    }
  }

  async getPremiumFeatures(userId?: string): Promise<{
    hasAccess: boolean;
    planName: string;
    upgradeRequired: boolean;
    hasFamilyPlanAccess: boolean;
  }> {
    const [userSubscription, hasAccess, hasFamilyPlanAccess] = await Promise.all([
      this.getUserSubscription(userId),
      this.hasPremiumFeatures(userId),
      this.hasFamilyPlanAccess(userId)
    ]);

    let planName: string;
    if (userSubscription) {
      planName = userSubscription.plan.display_name;
    } else {
      const freePlan = await getFreePlanLimits();
      planName = freePlan.planName;
    }

    return {
      hasAccess,
      planName,
      upgradeRequired: !hasAccess,
      hasFamilyPlanAccess
    };
  }

  async getStoriesCreatedThisMonth(userId: string): Promise<number> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    try {
      // Count ALL stories created this month (including deleted ones) to prevent abuse
      // This prevents users from deleting and recreating stories to bypass limits
      const result = await query(
        `SELECT COUNT(*) as count FROM stories 
         WHERE user_id = $1 
         AND created_at >= $2 
         AND created_at <= $3`,
        [userId, startOfMonth.toISOString(), endOfMonth.toISOString()]
      );

      return parseInt(result.rows[0]?.count) || 0;
    } catch (error) {
      console.error('Error counting user stories:', error);
      throw new Error('Failed to count user stories');
    }
  }



  async canUserCreateStory(userId: string): Promise<{
    canCreate: boolean;
    currentCount: number;
    limit: number;
    planName: string;
    remainingStories: number;
  }> {
    const [userSubscription, storiesThisMonth] = await Promise.all([
      this.getUserSubscription(userId),
      this.getStoriesCreatedThisMonth(userId)
    ]);

    // Use actual subscription data if available, otherwise fall back to free plan
    let limit: number;
    let planName: string;
    
    if (userSubscription) {
      limit = userSubscription.plan.stories_per_month;
      planName = userSubscription.plan.display_name;
    } else {
      // Fallback to free plan limits
      const freePlan = await getFreePlanLimits();
      limit = freePlan.storiesPerMonth;
      planName = freePlan.planName;
    }

    const canCreate = storiesThisMonth < limit;
    const remainingStories = Math.max(0, limit - storiesThisMonth);

    return {
      canCreate,
      currentCount: storiesThisMonth,
      limit,
      planName,
      remainingStories
    };
  }

  async getImageRegenerationsForStory(userId: string, storyId: number): Promise<number> {
    try {
      // Count ALL images with prompts (including deleted ones) to prevent abuse
      // This prevents users from deleting and recreating images to bypass limits
      const result = await query(
        `SELECT COUNT(*) as count FROM images 
         WHERE user_id = $1 AND story_id = $2 AND prompt IS NOT NULL`,
        [userId, storyId]
      );

      return parseInt(result.rows[0].count) || 0;
    } catch (error) {
      console.error('Error counting image regenerations for story:', error);
      throw new Error('Failed to count image regenerations for story');
    }
  }



  async canUserRegenerateImage(userId: string, storyId?: number): Promise<{
    canRegenerate: boolean;
    currentCount: number;
    limit: number;
    planName: string;
    remaining: number;
  }> {
    const userSubscription = await this.getUserSubscription(userId);

    // Use actual subscription data if available, otherwise fall back to free plan
    let limit: number;
    let planName: string;
    
    if (userSubscription) {
      limit = userSubscription.plan.image_regenerations_per_story;
      planName = userSubscription.plan.display_name;
    } else {
      // Fallback to free plan limits
      const freePlan = await getFreePlanLimits();
      limit = freePlan.imageRegenerationsPerStory;
      planName = freePlan.planName;
    }

    if (storyId) {
      // Check per-story limits
      const regenerationsForStory = await this.getImageRegenerationsForStory(userId, storyId);
      const canRegenerate = regenerationsForStory < limit;
      const remaining = Math.max(0, limit - regenerationsForStory);

      return {
        canRegenerate,
        currentCount: regenerationsForStory,
        limit,
        planName,
        remaining
      };
    } else {
      // Fallback: if no story ID provided, assume unlimited for backward compatibility
      return {
        canRegenerate: true,
        currentCount: 0,
        limit,
        planName,
        remaining: limit
      };
    }
  }

  async createSubscription(userId: string, planName: string): Promise<UserSubscription> {
    try {
      // Get the plan details
      const planResult = await query(
        'SELECT * FROM subscription_plans WHERE name = $1 AND active = true',
        [planName]
      );

      if (planResult.rows.length === 0) {
        throw new Error('Invalid plan name');
      }

      const plan = planResult.rows[0];

      // Cancel any existing subscription
      await query(
        'UPDATE user_subscriptions SET status = $1 WHERE user_id = $2',
        ['cancelled', userId]
      );

      // Create new subscription
      const subscriptionResult = await query(`
        INSERT INTO user_subscriptions (
          user_id, plan_id, status, current_period_start, current_period_end
        ) VALUES ($1, $2, $3, NOW(), NOW() + INTERVAL '1 month')
        RETURNING *
      `, [userId, plan.id, 'active']);

      const subscription = subscriptionResult.rows[0];

      return {
        id: subscription.id,
        user_id: subscription.user_id,
        plan_id: subscription.plan_id,
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start),
        current_period_end: new Date(subscription.current_period_end),
        cancel_at_period_end: subscription.cancel_at_period_end,
        plan: {
          id: plan.id,
          name: plan.name,
          display_name: plan.display_name,
          price_monthly: parseFloat(plan.price_monthly),
          price_yearly: parseFloat(plan.price_yearly),
          stories_per_month: plan.stories_per_month,
          image_regenerations_per_story: plan.image_regenerations_per_story,
          features: plan.features,
          active: plan.active
        }
      };
    } catch (error) {
      console.error('Error creating subscription:', error);
      throw new Error('Failed to create subscription');
    }
  }

  async createAccessCodeSubscription(userId: string, planName: string, durationMonths: number = 12): Promise<UserSubscription> {
    try {
      // Get the plan details
      const planResult = await query(
        'SELECT * FROM subscription_plans WHERE name = $1 AND active = true',
        [planName]
      );

      if (planResult.rows.length === 0) {
        throw new Error('Invalid plan name');
      }

      const plan = planResult.rows[0];

      // Cancel any existing subscription
      await query(
        'UPDATE user_subscriptions SET status = $1 WHERE user_id = $2',
        ['cancelled', userId]
      );

      // Create new subscription WITHOUT Stripe fields - this prevents any billing attempts
      // By explicitly setting stripe_subscription_id, stripe_customer_id, and stripe_price_id to NULL,
      // the system will not attempt to bill this subscription through Stripe
      const subscriptionResult = await query(`
        INSERT INTO user_subscriptions (
          user_id, plan_id, status, current_period_start, current_period_end,
          stripe_subscription_id, stripe_customer_id, stripe_price_id
        ) VALUES ($1, $2, $3, NOW(), NOW() + ($4 || ' months')::INTERVAL, NULL, NULL, NULL)
        RETURNING *
      `, [userId, plan.id, 'active', durationMonths]);

      const subscription = subscriptionResult.rows[0];

      return {
        id: subscription.id,
        user_id: subscription.user_id,
        plan_id: subscription.plan_id,
        status: subscription.status,
        current_period_start: new Date(subscription.current_period_start),
        current_period_end: new Date(subscription.current_period_end),
        cancel_at_period_end: subscription.cancel_at_period_end,
        plan: {
          id: plan.id,
          name: plan.name,
          display_name: plan.display_name,
          price_monthly: parseFloat(plan.price_monthly),
          price_yearly: parseFloat(plan.price_yearly),
          stories_per_month: plan.stories_per_month,
          image_regenerations_per_story: plan.image_regenerations_per_story,
          features: plan.features,
          active: plan.active
        }
      };
    } catch (error) {
      console.error('Error creating access code subscription:', error);
      throw new Error('Failed to create access code subscription');
    }
  }

  async isAccessCodeSubscription(userId: string): Promise<boolean> {
    try {
      const result = await query(`
        SELECT stripe_subscription_id, stripe_customer_id, stripe_price_id
        FROM user_subscriptions 
        WHERE user_id = $1 AND status = 'active'
      `, [userId]);

      if (result.rows.length === 0) {
        return false;
      }

      const subscription = result.rows[0];
      // If all Stripe fields are null, this is an access code subscription
      return !subscription.stripe_subscription_id && 
             !subscription.stripe_customer_id && 
             !subscription.stripe_price_id;
    } catch (error) {
      console.error('Error checking if subscription is access code based:', error);
      return false;
    }
  }

  async cancelSubscription(userId: string): Promise<void> {
    try {
      await query(
        'UPDATE user_subscriptions SET cancel_at_period_end = true WHERE user_id = $1 AND status = $2',
        [userId, 'active']
      );
    } catch (error) {
      console.error('Error cancelling subscription:', error);
      throw new Error('Failed to cancel subscription');
    }
  }

  async getAllPlans(): Promise<SubscriptionPlan[]> {
    try {
      const result = await query(
        'SELECT * FROM subscription_plans WHERE active = true ORDER BY price_monthly ASC'
      );

      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        display_name: row.display_name,
        price_monthly: parseFloat(row.price_monthly),
        price_yearly: parseFloat(row.price_yearly),
        stories_per_month: row.stories_per_month,
        image_regenerations_per_story: row.image_regenerations_per_story,
        features: row.features,
        active: row.active
      }));
    } catch (error) {
      console.error('Error fetching plans:', error);
      throw new Error('Failed to fetch plans');
    }
  }


}