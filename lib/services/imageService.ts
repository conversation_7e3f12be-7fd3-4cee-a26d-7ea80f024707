import { uploadFile } from '../r2-storage-utils';

export class ImageService {
  private base64ToBlob(base64: string, mimeType: string = 'image/webp'): Blob {
    // Clean the base64 string - remove data URL prefix if present
    let cleanBase64 = base64;
    
    // Remove data URL prefix (e.g., "data:image/webp;base64,")
    if (base64.includes(',')) {
      cleanBase64 = base64.split(',')[1];
    }
    
    // Remove any whitespace or newlines
    cleanBase64 = cleanBase64.replace(/\s/g, '');
    
    try {
      const byteCharacters = atob(cleanBase64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      return new Blob([byteArray], { type: mimeType });
    } catch (error) {
      console.error('Failed to decode base64 image:', error);
      console.error('Base64 string length:', base64.length);
      console.error('Cleaned base64 string length:', cleanBase64.length);
      console.error('First 100 chars of original:', base64.substring(0, 100));
      console.error('First 100 chars of cleaned:', cleanBase64.substring(0, 100));
      throw new Error('Invalid base64 image data');
    }
  }

  async uploadImage(base64Image: string, userId: string, storyId: number): Promise<string> {
    // Convert base64 to Blob
    const imageBlob = this.base64ToBlob(base64Image, 'image/webp');
    
    // Generate unique filename with timestamp
    const timestamp = Date.now();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const uniqueFilename = `image_${timestamp}_${randomSuffix}.webp`;
    
    // Create storage path: userId/storyId/uniqueFilename
    const storagePath = `${userId}/${storyId}/${uniqueFilename}`;
    
    // Convert blob to buffer for R2
    const arrayBuffer = await imageBlob.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);
    
    // Upload to R2 storage
    await uploadFile('images', storagePath, buffer, 'image/webp');

    return storagePath;
  }
} 