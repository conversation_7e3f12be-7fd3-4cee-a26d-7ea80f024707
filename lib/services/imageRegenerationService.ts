interface ImageRegenerationResponse {
  image: string; // Base64 encoded WebP image
  image_prompt: string; // The prompt used for generation
}

export class ImageRegenerationService {
  private apiEndpoint: string;
  private apiToken: string;

  constructor() {
    this.apiEndpoint = process.env.STORY_IMAGE_REGEN_API_ENDPOINT!;
    this.apiToken = process.env.STORY_IMAGE_REGEN_API_TOKEN!;
    
    if (!this.apiEndpoint || !this.apiToken) {
      throw new Error('Missing image regeneration API configuration');
    }
  }

  async generateImage(prompt: string, ageRange: string): Promise<string> {
    const requestData = {
      prompt,
      age_range: ageRange
    };

    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        let errorText = 'Unknown error';
        try {
          errorText = await response.text();
          console.error('ImageRegenerationService: Error response body:', errorText);
        } catch {
          console.error('ImageRegenerationService: Could not read error response body');
        }
        console.error('ImageRegenerationService: API error:', response.status, response.statusText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');

      if (contentType?.includes('application/json')) {
        const responseText = await response.text();
        
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (parseError) {
          console.error('ImageRegenerationService: Failed to parse JSON response:', parseError);
          console.error('ImageRegenerationService: Response text:', responseText.substring(0, 500));
          throw new Error('Invalid JSON response from image generation service');
        }

        if (result.image_data || result.image) {
          return result.image_data || result.image;
        } else {
          throw new Error('No image data in response');
        }
      } else {
        // Handle binary response
        const arrayBuffer = await response.arrayBuffer();
        const base64Data = Buffer.from(arrayBuffer).toString('base64');
        return base64Data;
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to generate image');
    }
  }

  async regenerateImage(prompt: string, ageRange: string): Promise<ImageRegenerationResponse> {
    const requestData = {
      prompt,
      age_range: ageRange
    };

    try {
      const response = await fetch(this.apiEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiToken}`
        },
        body: JSON.stringify(requestData)
      });

      if (!response.ok) {
        let errorText = 'Unknown error';
        try {
          errorText = await response.text();
          console.error('ImageRegenerationService: Error response body:', errorText);
        } catch {
          console.error('ImageRegenerationService: Could not read error response body');
        }
        console.error('ImageRegenerationService: API error:', response.status, response.statusText);
        throw new Error(`API request failed: ${response.status} ${response.statusText}`);
      }

      const contentType = response.headers.get('content-type');

      if (contentType?.includes('application/json')) {
        const responseText = await response.text();
        
        let result;
        try {
          result = JSON.parse(responseText);
        } catch (parseError) {
          console.error('ImageRegenerationService: Failed to parse JSON response:', parseError);
          console.error('ImageRegenerationService: Response text:', responseText.substring(0, 500));
          throw new Error('Invalid JSON response from image generation service');
        }

        if (result.image_data || result.image) {
          return {
            image: result.image_data || result.image,
            image_prompt: result.image_prompt || prompt
          };
        } else {
          throw new Error('No image data in response');
        }
      } else {
        // Handle binary response
        const arrayBuffer = await response.arrayBuffer();
        const base64Data = Buffer.from(arrayBuffer).toString('base64');
        return {
          image: base64Data,
          image_prompt: prompt
        };
      }
    } catch (error) {
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to regenerate image');
    }
  }
} 