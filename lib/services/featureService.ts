import { query } from '../postgres-client';
import { auth } from '@/auth';

// This service should only be used server-side
if (typeof window !== 'undefined') {
  throw new Error('FeatureService should only be used server-side');
}

// Import types from client-side compatible file
import { Feature, AccessLevel, FeatureAccess } from '../types/features';

// Base feature configuration
interface BaseFeatureConfig {
  access: AccessLevel;
  limit?: number;
}

// Feature configuration for each plan
export interface FeatureConfig {
  [Feature.STORY_CREATION]: BaseFeatureConfig;
  [Feature.STORY_SHARING]: BaseFeatureConfig;
  [Feature.STORY_PDF_EXPORT]: BaseFeatureConfig;
  [Feature.IMAGE_GENERATION]: BaseFeatureConfig;
  [Feature.IMAGE_REGENERATION]: BaseFeatureConfig;
  [Feature.AUDIO_GENERATION]: BaseFeatureConfig;
  [Feature.AUDIO_PLAYBACK]: BaseFeatureConfig;
  [Feature.PREMIUM_VOICES]: BaseFeatureConfig;
  [Feature.TRANSCRIPTION_GENERATION]: BaseFeatureConfig;
  [Feature.TRANSCRIPTION_HIGHLIGHTING]: BaseFeatureConfig;
  [Feature.TRANSCRIPTION_WORD_TIMING]: BaseFeatureConfig;
  [Feature.PRIORITY_SUPPORT]: BaseFeatureConfig;
  [Feature.ADVANCED_ANALYTICS]: BaseFeatureConfig;
}

// Plan feature configurations
const PLAN_FEATURES: Record<string, FeatureConfig> = {
  free: {
    [Feature.STORY_CREATION]: { access: AccessLevel.LIMITED, limit: 3 },
    [Feature.STORY_SHARING]: { access: AccessLevel.FULL },
    [Feature.STORY_PDF_EXPORT]: { access: AccessLevel.NONE },
    [Feature.IMAGE_GENERATION]: { access: AccessLevel.FULL },
    [Feature.IMAGE_REGENERATION]: { access: AccessLevel.LIMITED, limit: 2 },
    [Feature.AUDIO_GENERATION]: { access: AccessLevel.NONE },
    [Feature.AUDIO_PLAYBACK]: { access: AccessLevel.FULL },
    [Feature.PREMIUM_VOICES]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_GENERATION]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_HIGHLIGHTING]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_WORD_TIMING]: { access: AccessLevel.NONE },
    [Feature.PRIORITY_SUPPORT]: { access: AccessLevel.NONE },
    [Feature.ADVANCED_ANALYTICS]: { access: AccessLevel.NONE }
  },
  starter: {
    [Feature.STORY_CREATION]: { access: AccessLevel.LIMITED, limit: 15 },
    [Feature.STORY_SHARING]: { access: AccessLevel.FULL },
    [Feature.STORY_PDF_EXPORT]: { access: AccessLevel.FULL },
    [Feature.IMAGE_GENERATION]: { access: AccessLevel.FULL },
    [Feature.IMAGE_REGENERATION]: { access: AccessLevel.LIMITED, limit: 3 },
    [Feature.AUDIO_GENERATION]: { access: AccessLevel.FULL },
    [Feature.AUDIO_PLAYBACK]: { access: AccessLevel.FULL },
    [Feature.PREMIUM_VOICES]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_GENERATION]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_HIGHLIGHTING]: { access: AccessLevel.NONE },
    [Feature.TRANSCRIPTION_WORD_TIMING]: { access: AccessLevel.NONE },
    [Feature.PRIORITY_SUPPORT]: { access: AccessLevel.FULL },
    [Feature.ADVANCED_ANALYTICS]: { access: AccessLevel.NONE }
  },
  family: {
    [Feature.STORY_CREATION]: { access: AccessLevel.LIMITED, limit: 40 },
    [Feature.STORY_SHARING]: { access: AccessLevel.FULL },
    [Feature.STORY_PDF_EXPORT]: { access: AccessLevel.FULL },
    [Feature.IMAGE_GENERATION]: { access: AccessLevel.FULL },
    [Feature.IMAGE_REGENERATION]: { access: AccessLevel.LIMITED, limit: 5 },
    [Feature.AUDIO_GENERATION]: { access: AccessLevel.FULL },
    [Feature.AUDIO_PLAYBACK]: { access: AccessLevel.FULL },
    [Feature.PREMIUM_VOICES]: { access: AccessLevel.FULL },
    [Feature.TRANSCRIPTION_GENERATION]: { access: AccessLevel.FULL },
    [Feature.TRANSCRIPTION_HIGHLIGHTING]: { access: AccessLevel.FULL },
    [Feature.TRANSCRIPTION_WORD_TIMING]: { access: AccessLevel.FULL },
    [Feature.PRIORITY_SUPPORT]: { access: AccessLevel.FULL },
    [Feature.ADVANCED_ANALYTICS]: { access: AccessLevel.FULL }
  }
};

export class FeatureService {
  async getUserPlan(userId?: string): Promise<string> {
    try {
      let currentUserId = userId;
      
      if (!currentUserId) {
        const session = await auth();
        if (!session?.user?.id) {
          return 'free';
        }
        currentUserId = session.user.id;
      }

      const result = await query(`
        SELECT sp.name 
        FROM user_subscriptions us
        JOIN subscription_plans sp ON us.plan_id = sp.id
        WHERE us.user_id = $1 
        AND us.status = 'active' 
        AND us.current_period_end > NOW()
      `, [currentUserId]);

      return result.rows[0]?.name || 'free';
    } catch (error) {
      console.error('Error fetching user plan:', error);
      return 'free';
    }
  }

  async checkFeatureAccess(feature: Feature, userId?: string): Promise<FeatureAccess> {
    const userPlan = await this.getUserPlan(userId);
    const planFeatures = PLAN_FEATURES[userPlan] || PLAN_FEATURES['free'];
    const featureConfig = planFeatures[feature];

    if (!featureConfig || featureConfig.access === AccessLevel.NONE) {
      return {
        hasAccess: false,
        accessLevel: AccessLevel.NONE,
        upgradeRequired: true,
        requiredPlan: this.getRequiredPlanForFeature(feature)
      };
    }

    // For limited features, check usage against limits
    if (featureConfig.access === AccessLevel.LIMITED && featureConfig.limit && userId) {
      const remaining = await this.getRemainingUsage(feature, userId, featureConfig.limit);
      
      return {
        hasAccess: remaining > 0,
        accessLevel: featureConfig.access,
        limit: featureConfig.limit,
        remaining,
        upgradeRequired: remaining <= 0
      };
    }

    return {
      hasAccess: true,
      accessLevel: featureConfig.access,
      limit: featureConfig.limit,
      upgradeRequired: false
    };
  }

  async checkMultipleFeatures(features: Feature[], userId?: string): Promise<Record<Feature, FeatureAccess>> {
    const results: Record<Feature, FeatureAccess> = {} as Record<Feature, FeatureAccess>;
    
    for (const feature of features) {
      results[feature] = await this.checkFeatureAccess(feature, userId);
    }
    
    return results;
  }

  private getRequiredPlanForFeature(feature: Feature): string {
    // Find the lowest plan that has access to this feature
    for (const [planName, features] of Object.entries(PLAN_FEATURES)) {
      if (features[feature]?.access !== AccessLevel.NONE) {
        return planName;
      }
    }
    return 'family'; // Default to highest plan
  }

  private async getRemainingUsage(feature: Feature, userId: string, limit: number): Promise<number> {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);

    try {
      switch (feature) {
        case Feature.STORY_CREATION:
          // Count ALL stories created this month (including deleted ones) to prevent abuse
          // This prevents users from deleting and recreating stories to bypass limits
          const storyResult = await query(
            `SELECT COUNT(*) as count FROM stories 
             WHERE user_id = $1 
             AND created_at >= $2 
             AND created_at <= $3`,
            [userId, startOfMonth.toISOString(), endOfMonth.toISOString()]
          );
          const storiesUsed = parseInt(storyResult.rows[0]?.count) || 0;
          return Math.max(0, limit - storiesUsed);

        case Feature.IMAGE_REGENERATION:
          // Image regeneration uses per-story limits, not monthly limits
          // This shouldn't be called for image regeneration, but return limit as fallback
          return limit;

        default:
          return limit; // For features without usage tracking
      }
    } catch (error) {
      console.error(`Error getting remaining usage for ${feature}:`, error);
      return 0;
    }
  }

  // Convenience methods for common feature checks
  async canCreateStory(userId?: string): Promise<FeatureAccess> {
    return this.checkFeatureAccess(Feature.STORY_CREATION, userId);
  }

  async canGenerateAudio(userId?: string): Promise<FeatureAccess> {
    return this.checkFeatureAccess(Feature.AUDIO_GENERATION, userId);
  }

  async canGenerateTranscription(userId?: string): Promise<FeatureAccess> {
    return this.checkFeatureAccess(Feature.TRANSCRIPTION_GENERATION, userId);
  }

  async canUseTranscriptionHighlighting(userId?: string): Promise<FeatureAccess> {
    return this.checkFeatureAccess(Feature.TRANSCRIPTION_HIGHLIGHTING, userId);
  }

  async canRegenerateImage(userId?: string): Promise<FeatureAccess> {
    return this.checkFeatureAccess(Feature.IMAGE_REGENERATION, userId);
  }

  // Get all features for a user's plan
  async getUserFeatures(userId?: string): Promise<Record<Feature, FeatureAccess>> {
    const allFeatures = Object.values(Feature);
    return this.checkMultipleFeatures(allFeatures, userId);
  }

  // Get feature summary for UI display
  async getFeatureSummary(userId?: string): Promise<{
    plan: string;
    features: Record<string, FeatureAccess>;
  }> {
    const plan = await this.getUserPlan(userId);
    const features = await this.getUserFeatures(userId);
    
    return {
      plan,
      features: features as Record<string, FeatureAccess>
    };
  }
} 