import { query } from '../postgres-client';
import { uploadFile } from '../r2-storage-utils';
import { getAudioSecureUrl } from '../r2-storage-utils';

interface AudioRequest {
  title: string;
  text: string;
}

interface AudioResponse {
  audio: string;
}

export class AudioService {
  private apiEndpoint: string;
  private apiToken: string;

  constructor() {
    this.apiEndpoint = process.env.AUDIO_API_ENDPOINT!;
    this.apiToken = process.env.AUDIO_API_TOKEN!;
    
    if (!this.apiEndpoint || !this.apiToken) {
      throw new Error('Missing audio API configuration');
    }
  }

  async checkExistingAudio(storyId: number, userId: string): Promise<string | null> {
    try {
      const result = await query(
        'SELECT storage_path FROM audio WHERE story_id = $1 AND user_id = $2 LIMIT 1',
        [storyId, userId]
      );

      if (result.rows.length > 0) {
        return getAudioSecureUrl(storyId.toString());
      }
      
      return null;
    } catch (error) {
      console.error('Error checking existing audio:', error);
      return null;
    }
  }

  async generateAudio(requestData: AudioRequest): Promise<AudioResponse> {
    const response = await fetch(this.apiEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiToken}`
      },
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Audio API error:', errorText);
      throw new Error(`Failed to generate audio: ${response.status}`);
    }

    const responseData = await response.json();
    
    if (!responseData.audio) {
      throw new Error('No audio data in response');
    }

    return responseData;
  }

  private base64ToBlob(base64Audio: string): Blob {
    // Check if it's a data URL and extract the base64 part
    let cleanBase64: string;
    if (base64Audio.startsWith('data:')) {
      const base64Index = base64Audio.indexOf(',');
      if (base64Index === -1) {
        throw new Error('Invalid data URL format');
      }
      cleanBase64 = base64Audio.substring(base64Index + 1);
    } else {
      cleanBase64 = base64Audio;
    }
    
    // Clean the base64 string - remove any whitespace
    cleanBase64 = cleanBase64.replace(/\s/g, '');
    
    // Add padding if needed
    const paddedBase64 = cleanBase64 + '='.repeat((4 - cleanBase64.length % 4) % 4);
    
    try {
      const byteCharacters = atob(paddedBase64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      return new Blob([byteArray], { type: 'audio/mpeg' });
    } catch (decodeError) {
      console.error('Base64 decode error:', decodeError);
      throw new Error('Invalid audio data format');
    }
  }

  async uploadAudio(base64Audio: string, userId: string, storyId: number): Promise<string> {
    // Convert base64 to blob
    const audioBlob = this.base64ToBlob(base64Audio);

    // Create storage path: userId/storyId/audio.mp3
    const storagePath = `${userId}/${storyId}/audio.mp3`;

    // Convert blob to buffer for R2
    const arrayBuffer = await audioBlob.arrayBuffer();
    const buffer = new Uint8Array(arrayBuffer);

    // Upload to R2 storage
    await uploadFile('audio', storagePath, buffer, 'audio/mpeg');

    return storagePath;
  }

  async saveAudioRecord(userId: string, storyId: number, storagePath: string): Promise<void> {
    try {
      // First, check if a record already exists
      const existingResult = await query(
        'SELECT id FROM audio WHERE story_id = $1 AND user_id = $2 LIMIT 1',
        [storyId, userId]
      );

      const timestamp = new Date().toISOString();

      if (existingResult.rows.length > 0) {
        // Update existing record
        await query(
          'UPDATE audio SET storage_path = $1, updated_at = $2 WHERE story_id = $3 AND user_id = $4',
          [storagePath, timestamp, storyId, userId]
        );
      } else {
        // Insert new record
        await query(
          'INSERT INTO audio (user_id, story_id, storage_path, created_at, updated_at) VALUES ($1, $2, $3, $4, $5)',
          [userId, storyId, storagePath, timestamp, timestamp]
        );
      }
    } catch (error) {
      console.error('Failed to save audio record:', error);
      throw new Error('Failed to save audio record');
    }
  }

  getAudioUrl(storyId: number): string {
    return getAudioSecureUrl(storyId.toString());
  }
} 