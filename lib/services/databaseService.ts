import { query, transaction } from '../postgres-client';

interface StoryData {
  user_id: string;
  title: string;
  content: string;
  main_character: string;
  setting: string;
  details: string;
  age_range: number;
  created_at: string;
}

export class DatabaseService {
  async getAgeRangeId(ageRange: string): Promise<number> {
    try {
      const result = await query(
        'SELECT id FROM age_ranges WHERE range = $1',
        [ageRange]
      );

      if (result.rows.length === 0) {
        console.error('Age range not found:', ageRange);
        throw new Error('Invalid age range');
      }

      return result.rows[0].id;
    } catch (error) {
      console.error('Error fetching age range:', error);
      throw new Error('Invalid age range');
    }
  }

  async saveStory(storyData: StoryData): Promise<StoryData & { id: number; story_uuid: string }> {
    try {
      const result = await query(
        `INSERT INTO stories (user_id, title, content, main_character, setting, details, age_range, created_at, story_uuid)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, gen_random_uuid())
         RETURNING *`,
        [
          storyData.user_id,
          storyData.title,
          storyData.content,
          storyData.main_character,
          storyData.setting,
          storyData.details,
          storyData.age_range,
          storyData.created_at
        ]
      );

      if (result.rows.length === 0) {
        throw new Error('Failed to save story to database');
      }

      return result.rows[0];
    } catch (error) {
      console.error('Database save error:', error);
      throw new Error(`Failed to save story to database: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async saveImageRecord(
    userId: string, 
    storyId: number, 
    storagePath: string, 
    originalPrompt?: string, 
    prompt?: string,
    setAsDefault: boolean = false
  ): Promise<void> {
    try {
      await transaction(async (client) => {
        // If this should be the default image, first unset all other defaults for this story (only non-deleted)
        if (setAsDefault) {
          await client.query(
            'UPDATE images SET "default" = false WHERE story_id = $1 AND (deleted IS NULL OR deleted = false)',
            [storyId]
          );
        }

        // Insert the new image record
        await client.query(
          `INSERT INTO images (user_id, story_id, storage_path, original_prompt, prompt, "default", created_at)
           VALUES ($1, $2, $3, $4, $5, $6, $7)`,
          [
            userId,
            storyId,
            storagePath,
            originalPrompt || null,
            prompt || null,
            setAsDefault,
            new Date().toISOString()
          ]
        );
      });
    } catch (error) {
      console.error('Failed to save image record:', error);
      throw new Error('Failed to save image record');
    }
  }

  async getStoryImages(storyId: number): Promise<Array<{
    id: number;
    storage_path: string;
    default: boolean;
    original_prompt: string | null;
    prompt: string | null;
    created_at: string;
  }>> {
    try {
      const result = await query(
        `SELECT id, storage_path, "default", original_prompt, prompt, created_at
         FROM images
         WHERE story_id = $1 AND (deleted IS NULL OR deleted = false)
         ORDER BY created_at ASC`,
        [storyId]
      );

      return result.rows;
    } catch (error) {
      console.error('Failed to fetch story images:', error);
      throw new Error('Failed to fetch story images');
    }
  }

  async setDefaultImage(imageId: number, storyId: number): Promise<void> {
    try {
      await transaction(async (client) => {
        // First, unset all default images for this story (only non-deleted)
        await client.query(
          'UPDATE images SET "default" = false WHERE story_id = $1 AND (deleted IS NULL OR deleted = false)',
          [storyId]
        );

        // Then set the selected image as default and update the timestamp (only if not deleted)
        await client.query(
          'UPDATE images SET "default" = true, updated_at = $1 WHERE id = $2 AND story_id = $3 AND (deleted IS NULL OR deleted = false)',
          [new Date().toISOString(), imageId, storyId]
        );
      });
    } catch (error) {
      console.error('Failed to set default image:', error);
      throw new Error('Failed to set default image');
    }
  }

  private async unsetDefaultImages(storyId: number): Promise<void> {
    try {
      await query(
        'UPDATE images SET "default" = false WHERE story_id = $1 AND (deleted IS NULL OR deleted = false)',
        [storyId]
      );
    } catch (error) {
      console.error('Failed to unset default images:', error);
      throw new Error('Failed to unset default images');
    }
  }

  async linkStoryThemes(storyId: number, themes: string[]): Promise<void> {
    if (!themes || themes.length === 0) return;

    try {
      await transaction(async (client) => {
        // Batch fetch all themes to reduce database queries
        const placeholders = themes.map((_, index) => `$${index + 1}`).join(',');
        const themesResult = await client.query(
          `SELECT id, name FROM themes WHERE name IN (${placeholders})`,
          themes
        );

        if (themesResult.rows.length === 0) return;

        // Batch insert story-theme relationships
        const insertValues = themesResult.rows.map((theme: { id: number; name: string }, index: number) => 
          `($1, $${index + 2})`
        ).join(',');
        
        const insertParams = [storyId, ...themesResult.rows.map((theme: { id: number; name: string }) => theme.id)];
        
        await client.query(
          `INSERT INTO stories_themes (story_id, theme_id) VALUES ${insertValues}`,
          insertParams
        );
      });
    } catch (error) {
      console.error('Error linking themes:', error);
    }
  }

  async linkStoryTags(storyId: number, tags: string[], userId: string): Promise<void> {
    if (!tags || tags.length === 0) return;

    try {
      await transaction(async (client) => {
        for (const tagName of tags) {
          if (!tagName.trim()) continue;

          // First, try to find existing tag for this user
          const tagResult = await client.query(
            'SELECT id FROM tags WHERE name = $1 AND user_id = $2',
            [tagName.trim(), userId]
          );

          let tagId;
          if (tagResult.rows.length > 0) {
            // Tag exists, use it
            tagId = tagResult.rows[0].id;
          } else {
            // Tag doesn't exist, create it
            const createResult = await client.query(
              'INSERT INTO tags (name, user_id, created_at, updated_at) VALUES ($1, $2, $3, $4) RETURNING id',
              [tagName.trim(), userId, new Date().toISOString(), new Date().toISOString()]
            );
            tagId = createResult.rows[0].id;
          }

          // Link story to tag
          await client.query(
            'INSERT INTO story_tags (story_id, tag_id) VALUES ($1, $2) ON CONFLICT DO NOTHING',
            [storyId, tagId]
          );
        }
      });
    } catch (error) {
      console.error('Failed to link story tags:', error);
      throw new Error('Failed to link story tags');
    }
  }

  // New optimized method for fetching user stories with images
  async getUserStoriesOptimized(userId: string): Promise<Array<{
    id: number;
    story_uuid: string;
    title: string;
    content: string;
    main_character: string;
    setting: string;
    created_at: string;
    is_public: boolean;
    stories_themes?: Array<{ themes: { name: string; description: string } }>;
    story_tags?: Array<{ tags: { name: string } }>;
    age_ranges?: { range: string };
    images?: Array<{ id: number; storage_path: string; updated_at: string; default: boolean }>;
    hasAudio?: boolean;
  }>> {
    try {
      // Get stories with age ranges
      const storiesResult = await query(
        `SELECT s.*, ar.range as age_range
         FROM stories s
         LEFT JOIN age_ranges ar ON s.age_range = ar.id
         WHERE s.user_id = $1 AND s.is_deleted = false
         ORDER BY s.created_at DESC`,
        [userId]
      );

      if (storiesResult.rows.length === 0) return [];

      const storyIds = storiesResult.rows.map(story => story.id);
      
      // Get themes for all stories
      const themesResult = await query(
        `SELECT st.story_id, t.name, t.description
         FROM stories_themes st
         JOIN themes t ON st.theme_id = t.id
         WHERE st.story_id = ANY($1)`,
        [storyIds]
      );

      // Get tags for all stories
      const tagsResult = await query(
        `SELECT st.story_id, t.name
         FROM story_tags st
         JOIN tags t ON st.tag_id = t.id
         WHERE st.story_id = ANY($1)`,
        [storyIds]
      );

      // Get images for all stories
      const imagesResult = await query(
        `SELECT story_id, id, storage_path, updated_at, "default"
         FROM images
         WHERE story_id = ANY($1) AND (deleted IS NULL OR deleted = false)`,
        [storyIds]
      );

      // Get audio for all stories
      const audioResult = await query(
        `SELECT DISTINCT story_id
         FROM audio
         WHERE story_id = ANY($1)`,
        [storyIds]
      );

      // Group themes, tags, images, and audio by story_id
      const themesByStory = new Map();
      const tagsByStory = new Map();
      const imagesByStory = new Map();
      const audioByStory = new Set();

      themesResult.rows.forEach(theme => {
        if (!themesByStory.has(theme.story_id)) {
          themesByStory.set(theme.story_id, []);
        }
        themesByStory.get(theme.story_id).push({
          themes: { name: theme.name, description: theme.description }
        });
      });

      tagsResult.rows.forEach(tag => {
        if (!tagsByStory.has(tag.story_id)) {
          tagsByStory.set(tag.story_id, []);
        }
        tagsByStory.get(tag.story_id).push({
          tags: { name: tag.name }
        });
      });

      imagesResult.rows.forEach(image => {
        if (!imagesByStory.has(image.story_id)) {
          imagesByStory.set(image.story_id, []);
        }
        imagesByStory.get(image.story_id).push({
          id: image.id,
          storage_path: image.storage_path,
          updated_at: image.updated_at,
          default: image.default
        });
      });

      audioResult.rows.forEach(audio => {
        audioByStory.add(audio.story_id);
      });

      // Combine all data
      return storiesResult.rows.map(story => ({
        ...story,
        age_ranges: { range: story.age_range },
        stories_themes: themesByStory.get(story.id) || [],
        story_tags: tagsByStory.get(story.id) || [],
        images: imagesByStory.get(story.id) || [],
        hasAudio: audioByStory.has(story.id)
      }));
    } catch (error) {
      console.error('Error in getUserStoriesOptimized:', error);
      return [];
    }
  }

  // Method for fetching public stories for sitemap generation
  async getPublicStoriesForSitemap(): Promise<Array<{
    story_uuid: string;
    title: string;
    created_at: string;
    updated_at?: string;
  }>> {
    try {
      const result = await query(
        `SELECT story_uuid, title, created_at, updated_at
         FROM stories
         WHERE is_public = true AND is_deleted = false
         ORDER BY created_at DESC`,
        []
      );

      return result.rows;
    } catch (error) {
      console.error('Error fetching public stories for sitemap:', error);
      return [];
    }
  }
} 