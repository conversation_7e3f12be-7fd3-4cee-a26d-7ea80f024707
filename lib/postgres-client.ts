import { Pool, PoolClient, QueryResult } from 'pg';

// SSL configuration - can be controlled via environment variable
const getSSLConfig = () => {
  // If POSTGRES_SSL is explicitly set, use that value
  if (process.env.POSTGRES_SSL !== undefined) {
    return process.env.POSTGRES_SSL === 'true' ? { rejectUnauthorized: false } : false;
  }
  
  // For Dokploy/internal Docker networks, default to no SSL
  if (process.env.POSTGRES_HOST && !process.env.POSTGRES_HOST.includes('localhost')) {
    return false;
  }
  
  // Default behavior for other environments
  return process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false;
};

const pool = new Pool({
  host: process.env.POSTGRES_HOST || 'localhost',
  port: parseInt(process.env.POSTGRES_PORT || '5432'),
  database: process.env.POSTGRES_DATABASE || 'mystorymaker',
  user: process.env.POSTGRES_USER || 'postgres',
  password: process.env.POSTGRES_PASSWORD,
  ssl: getSSLConfig(),
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000, // Increased for Docker internal networks
});

export { pool as postgres };

// Helper function to execute queries with error handling
export async function query(text: string, params?: unknown[]): Promise<QueryResult> {
  const client = await pool.connect();
  try {
    const result = await client.query(text, params);
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    throw error;
  } finally {
    client.release();
  }
}

// Helper function for transactions
export async function transaction<T>(callback: (client: PoolClient) => Promise<T>): Promise<T> {
  const client = await pool.connect();
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Health check function for deployment monitoring
export async function healthCheck(): Promise<boolean> {
  try {
    const result = await query('SELECT 1 as health');
    return result.rows.length > 0;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
} 